<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_margin="5dp"
    app:cardCornerRadius="4dp">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <ImageView
            android:id="@+id/food_image"
            android:layout_width="match_parent"
            android:layout_height="200dp"
            android:background="@drawable/p1"
            android:scaleType="centerCrop" />

        <TextView
            android:id="@+id/food_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/food_image"
            android:layout_centerInParent="true"
            android:layout_margin="5dp"
            android:text="口水鸡"
            android:textColor="@color/Black"
            android:textSize="16sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tv_price"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/food_name"
            android:layout_margin="5dp"
            android:text="价格："
            android:textColor="@color/DarkBlue"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/food_price"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/food_name"
            android:layout_centerInParent="true"
            android:layout_margin="5dp"
            android:layout_toRightOf="@+id/tv_price"
            android:text="19.9"
            android:textColor="@color/DarkBlue" />

        <TextView
            android:id="@+id/tv_score"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/food_name"
            android:layout_marginLeft="20dp"
            android:layout_marginTop="5dp"
            android:layout_marginRight="5dp"
            android:layout_marginBottom="5dp"
            android:layout_toRightOf="@+id/food_price"
            android:text="评分："
            android:textColor="@color/DarkBlue"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/food_score"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/food_name"
            android:layout_centerInParent="true"
            android:layout_margin="5dp"
            android:layout_toRightOf="@+id/tv_score"
            android:text="4.7"
            android:textColor="@color/DarkBlue" />

        <TextView
            android:id="@+id/food_comment"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/food_score"
            android:layout_centerHorizontal="true"
            android:text="味道很好，菜量很足，本地销售冠军"
            android:textColor="@color/Black"
            android:textSize="12sp" />

    </RelativeLayout>

</androidx.cardview.widget.CardView>