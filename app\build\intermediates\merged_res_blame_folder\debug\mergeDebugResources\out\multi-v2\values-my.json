{"logs": [{"outputFile": "njust.dzh.ordersystem.app-mergeDebugResources-19:/values-my/values-my.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\8d06f4c23c6b78afc6b8e18c56daf21c\\transformed\\core-1.3.1\\res\\values-my\\values-my.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2958", "endColumns": "100", "endOffsets": "3054"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\313b7d5dbe5dc9219fc547695506f52e\\transformed\\appcompat-1.2.0\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,218,325,441,528,637,760,842,923,1014,1107,1202,1296,1396,1489,1584,1678,1769,1860,1945,2060,2169,2268,2394,2501,2609,2769,2872", "endColumns": "112,106,115,86,108,122,81,80,90,92,94,93,99,92,94,93,90,90,84,114,108,98,125,106,107,159,102,85", "endOffsets": "213,320,436,523,632,755,837,918,1009,1102,1197,1291,1391,1484,1579,1673,1764,1855,1940,2055,2164,2263,2389,2496,2604,2764,2867,2953"}}]}]}