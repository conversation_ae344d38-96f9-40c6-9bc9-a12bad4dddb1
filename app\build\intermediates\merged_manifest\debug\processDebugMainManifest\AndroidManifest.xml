<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="njust.dzh.ordersystem"
    android:versionCode="1"
    android:versionName="1.0" >

    <uses-sdk
        android:minSdkVersion="21"
        android:targetSdkVersion="32" />

    <application
        android:allowBackup="true"
        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
        android:debuggable="true"
        android:extractNativeLibs="true"
        android:icon="@mipmap/ic_icon"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_icon"
        android:supportsRtl="true"
        android:testOnly="true"
        android:theme="@style/MyTheme" >
        <activity
            android:name="njust.dzh.ordersystem.Activity.OrderActivity"
            android:exported="false"
            android:label="@string/order_info" />
        <activity
            android:name="njust.dzh.ordersystem.Activity.FoodActivity"
            android:exported="false"
            android:theme="@style/FoodActivityTheme" />
        <activity
            android:name="njust.dzh.ordersystem.Activity.LoginActivity"
            android:exported="false" />
        <activity
            android:name="njust.dzh.ordersystem.Activity.RegisterActivity"
            android:exported="false" />
        <activity
            android:name="njust.dzh.ordersystem.Activity.WelcomeActivity"
            android:exported="true" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name="njust.dzh.ordersystem.Activity.MainActivity"
            android:exported="false" >
        </activity>
    </application>

</manifest>