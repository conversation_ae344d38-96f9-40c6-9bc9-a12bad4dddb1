<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:ns1="urn:oasis:names:tc:xliff:document:1.2" xmlns:ns2="http://schemas.android.com/tools">
    <attr format="reference" name="animate_relativeTo"/>
    <attr format="reference" name="appBarLayoutStyle"/>
    <attr format="enum" name="arcMode">
        <enum name="startVertical" value="0"/>
        <enum name="startHorizontal" value="1"/>
        <enum name="flip" value="2"/>
    </attr>
    <attr format="reference" name="badgeStyle"/>
    <attr format="boolean" name="barrierAllowsGoneWidgets"/>
    <attr format="enum" name="barrierDirection">
        <enum name="left" value="0"/>
        <enum name="right" value="1"/>
        <enum name="top" value="2"/>
        <enum name="bottom" value="3"/>
        <enum name="start" value="5"/>
        <enum name="end" value="6"/>
    </attr>
    <attr format="dimension" name="barrierMargin"/>
    <attr format="reference" name="bottomAppBarStyle"/>
    <attr format="reference" name="bottomNavigationStyle"/>
    <attr format="reference" name="bottomSheetDialogTheme"/>
    <attr format="reference" name="bottomSheetStyle"/>
    <attr format="reference" name="cardViewStyle"/>
    <attr format="boolean" name="chainUseRtl"/>
    <attr format="reference" name="checkedIcon"/>
    <attr format="color" name="checkedIconTint"/>
    <attr format="reference" name="chipGroupStyle"/>
    <attr format="reference" name="chipStandaloneStyle"/>
    <attr format="reference" name="chipStyle"/>
    <attr format="dimension" name="circleRadius"/>
    <attr name="colorOnBackground"/>
    <attr format="color" name="colorOnError"/>
    <attr format="color" name="colorOnPrimary"/>
    <attr format="color" name="colorOnPrimarySurface"/>
    <attr format="color" name="colorOnSecondary"/>
    <attr format="color" name="colorOnSurface"/>
    <attr format="color" name="colorPrimarySurface"/>
    <attr format="color" name="colorPrimaryVariant"/>
    <attr format="color" name="colorSecondary"/>
    <attr format="color" name="colorSecondaryVariant"/>
    <attr format="color" name="colorSurface"/>
    <attr format="reference" name="constraintSet"/>
    <attr format="string" name="constraint_referenced_ids"/>
    <attr format="reference" name="content"/>
    <attr format="reference" name="coordinatorLayoutStyle"/>
    <attr format="enum" name="curveFit">
        <enum name="spline" value="0"/>
        <enum name="linear" value="1"/>
    </attr>
    <attr format="integer" name="defaultDuration"/>
    <attr format="float" name="deltaPolarAngle"/>
    <attr format="float" name="deltaPolarRadius"/>
    <attr format="enum" name="dragDirection">
        <enum name="dragUp" value="0"/>
        <enum name="dragDown" value="1"/>
        <enum name="dragLeft" value="2"/>
        <enum name="dragRight" value="3"/>
        <enum name="dragStart" value="4"/>
        <enum name="dragEnd" value="5"/>
    </attr>
    <attr format="enum" name="drawPath">
        <enum name="none" value="0"/>
        <enum name="path" value="1"/>
        <enum name="pathRelative" value="2"/>
        <enum name="deltaRelative" value="3"/>
        <enum name="asConfigured" value="4"/>
        <enum name="rectangles" value="5"/>
    </attr>
    <attr format="reference" name="drawerArrowStyle"/>
    <attr format="integer" name="duration"/>
    <attr format="color" name="elevationOverlayColor"/>
    <attr format="boolean" name="elevationOverlayEnabled"/>
    <attr format="boolean" name="ensureMinTouchTargetSize"/>
    <attr format="reference" name="extendedFloatingActionButtonStyle"/>
    <attr format="reference" name="floatingActionButtonStyle"/>
    <attr format="float" name="flow_firstHorizontalBias"/>
    <attr format="enum" name="flow_firstHorizontalStyle">
        <enum name="spread" value="0"/>
        <enum name="spread_inside" value="1"/>
        <enum name="packed" value="2"/>
    </attr>
    <attr format="float" name="flow_firstVerticalBias"/>
    <attr format="enum" name="flow_firstVerticalStyle">
        <enum name="spread" value="0"/>
        <enum name="spread_inside" value="1"/>
        <enum name="packed" value="2"/>
    </attr>
    <attr format="float" name="flow_horizontalAlign">
        <enum name="start" value="0"/>
        <enum name="end" value="1"/>
        <enum name="center" value="2"/>
    </attr>
    <attr format="float" name="flow_horizontalBias"/>
    <attr format="dimension" name="flow_horizontalGap"/>
    <attr format="enum" name="flow_horizontalStyle">
        <enum name="spread" value="0"/>
        <enum name="spread_inside" value="1"/>
        <enum name="packed" value="2"/>
    </attr>
    <attr format="float" name="flow_lastHorizontalBias"/>
    <attr format="enum" name="flow_lastHorizontalStyle">
        <enum name="spread" value="0"/>
        <enum name="spread_inside" value="1"/>
        <enum name="packed" value="2"/>
    </attr>
    <attr format="float" name="flow_lastVerticalBias"/>
    <attr format="enum" name="flow_lastVerticalStyle">
        <enum name="spread" value="0"/>
        <enum name="spread_inside" value="1"/>
        <enum name="packed" value="2"/>
    </attr>
    <attr format="integer" name="flow_maxElementsWrap"/>
    <attr format="dimension" name="flow_padding"/>
    <attr format="enum" name="flow_verticalAlign">
        <enum name="top" value="0"/>
        <enum name="bottom" value="1"/>
        <enum name="center" value="2"/>
        <enum name="baseline" value="3"/>
    </attr>
    <attr format="float" name="flow_verticalBias"/>
    <attr format="dimension" name="flow_verticalGap"/>
    <attr format="enum" name="flow_verticalStyle">
        <enum name="spread" value="0"/>
        <enum name="spread_inside" value="1"/>
        <enum name="packed" value="2"/>
    </attr>
    <attr format="enum" name="flow_wrapMode">
        <enum name="none" value="0"/>
        <enum name="chain" value="1"/>
        <enum name="aligned" value="2"/>
    </attr>
    <attr format="integer" name="framePosition"/>
    <attr format="dimension" name="height"/>
    <attr format="reference" name="hideMotionSpec"/>
    <attr format="boolean" name="isLightTheme"/>
    <attr format="boolean" name="isMaterialTheme"/>
    <attr format="reference" name="itemShapeAppearance"/>
    <attr format="reference" name="itemShapeAppearanceOverlay"/>
    <attr format="color" name="itemTextColor"/>
    <attr format="reference" name="layoutDescription"/>
    <attr format="enum" name="layoutDuringTransition">
        <enum name="ignoreRequest" value="0"/>
        <enum name="honorRequest" value="1"/>
    </attr>
    <attr format="boolean" name="layout_constrainedHeight"/>
    <attr format="boolean" name="layout_constrainedWidth"/>
    <attr format="integer" name="layout_constraintBaseline_creator"/>
    <attr format="reference|enum" name="layout_constraintBaseline_toBaselineOf">
        <enum name="parent" value="0"/>
    </attr>
    <attr format="integer" name="layout_constraintBottom_creator"/>
    <attr format="reference|enum" name="layout_constraintBottom_toBottomOf">
        <enum name="parent" value="0"/>
    </attr>
    <attr format="reference|enum" name="layout_constraintBottom_toTopOf">
        <enum name="parent" value="0"/>
    </attr>
    <attr format="reference" name="layout_constraintCircle"/>
    <attr format="integer" name="layout_constraintCircleAngle"/>
    <attr format="dimension" name="layout_constraintCircleRadius"/>
    <attr format="string" name="layout_constraintDimensionRatio"/>
    <attr format="reference|enum" name="layout_constraintEnd_toEndOf">
        <enum name="parent" value="0"/>
    </attr>
    <attr format="reference|enum" name="layout_constraintEnd_toStartOf">
        <enum name="parent" value="0"/>
    </attr>
    <attr format="dimension" name="layout_constraintGuide_begin"/>
    <attr format="dimension" name="layout_constraintGuide_end"/>
    <attr format="float" name="layout_constraintGuide_percent"/>
    <attr name="layout_constraintHeight_default">
        <enum name="spread" value="0"/>
        <enum name="wrap" value="1"/>
        <enum name="percent" value="2"/>
    </attr>
    <attr format="dimension|enum" name="layout_constraintHeight_max">
        <enum name="wrap" value="-2"/>
    </attr>
    <attr format="dimension|enum" name="layout_constraintHeight_min">
        <enum name="wrap" value="-2"/>
    </attr>
    <attr format="float" name="layout_constraintHeight_percent"/>
    <attr format="float" name="layout_constraintHorizontal_bias"/>
    <attr format="enum" name="layout_constraintHorizontal_chainStyle">
        <enum name="spread" value="0"/>
        <enum name="spread_inside" value="1"/>
        <enum name="packed" value="2"/>
    </attr>
    <attr format="float" name="layout_constraintHorizontal_weight"/>
    <attr format="integer" name="layout_constraintLeft_creator"/>
    <attr format="reference|enum" name="layout_constraintLeft_toLeftOf">
        <enum name="parent" value="0"/>
    </attr>
    <attr format="reference|enum" name="layout_constraintLeft_toRightOf">
        <enum name="parent" value="0"/>
    </attr>
    <attr format="integer" name="layout_constraintRight_creator"/>
    <attr format="reference|enum" name="layout_constraintRight_toLeftOf">
        <enum name="parent" value="0"/>
    </attr>
    <attr format="reference|enum" name="layout_constraintRight_toRightOf">
        <enum name="parent" value="0"/>
    </attr>
    <attr format="reference|enum" name="layout_constraintStart_toEndOf">
        <enum name="parent" value="0"/>
    </attr>
    <attr format="reference|enum" name="layout_constraintStart_toStartOf">
        <enum name="parent" value="0"/>
    </attr>
    <attr format="string" name="layout_constraintTag"/>
    <attr format="integer" name="layout_constraintTop_creator"/>
    <attr format="reference|enum" name="layout_constraintTop_toBottomOf">
        <enum name="parent" value="0"/>
    </attr>
    <attr format="reference|enum" name="layout_constraintTop_toTopOf">
        <enum name="parent" value="0"/>
    </attr>
    <attr format="float" name="layout_constraintVertical_bias"/>
    <attr format="enum" name="layout_constraintVertical_chainStyle">
        <enum name="spread" value="0"/>
        <enum name="spread_inside" value="1"/>
        <enum name="packed" value="2"/>
    </attr>
    <attr format="float" name="layout_constraintVertical_weight"/>
    <attr name="layout_constraintWidth_default">
        <enum name="spread" value="0"/>
        <enum name="wrap" value="1"/>
        <enum name="percent" value="2"/>
    </attr>
    <attr format="dimension|enum" name="layout_constraintWidth_max">
        <enum name="wrap" value="-2"/>
    </attr>
    <attr format="dimension|enum" name="layout_constraintWidth_min">
        <enum name="wrap" value="-2"/>
    </attr>
    <attr format="float" name="layout_constraintWidth_percent"/>
    <attr format="dimension" name="layout_editor_absoluteX"/>
    <attr format="dimension" name="layout_editor_absoluteY"/>
    <attr format="dimension" name="layout_goneMarginBottom"/>
    <attr format="dimension" name="layout_goneMarginEnd"/>
    <attr format="dimension" name="layout_goneMarginLeft"/>
    <attr format="dimension" name="layout_goneMarginRight"/>
    <attr format="dimension" name="layout_goneMarginStart"/>
    <attr format="dimension" name="layout_goneMarginTop"/>
    <attr name="layout_optimizationLevel">
        <flag name="none" value="0"/>
        <flag name="standard" value="7"/> 
        <flag name="direct" value="1"/>
        <flag name="barrier" value="2"/>
        <flag name="chains" value="4"/>
        <flag name="dimensions" value="8"/>
        <flag name="ratio" value="16"/>
        <flag name="groups" value="32"/>
        <flag name="graph" value="64"/>
        <flag name="graph_wrap" value="128"/>
    </attr>
    <attr format="reference" name="materialButtonOutlinedStyle"/>
    <attr format="reference" name="materialButtonStyle"/>
    <attr format="reference" name="materialButtonToggleGroupStyle"/>
    <attr format="reference" name="materialCalendarDay"/>
    <attr format="reference" name="materialCalendarFullscreenTheme"/>
    <attr format="reference" name="materialCalendarHeaderConfirmButton"/>
    <attr format="reference" name="materialCalendarHeaderDivider"/>
    <attr format="reference" name="materialCalendarHeaderLayout"/>
    <attr format="reference" name="materialCalendarHeaderSelection"/>
    <attr format="reference" name="materialCalendarHeaderTitle"/>
    <attr format="reference" name="materialCalendarHeaderToggleButton"/>
    <attr format="reference" name="materialCalendarStyle"/>
    <attr format="reference" name="materialCalendarTheme"/>
    <attr format="reference" name="materialCardViewStyle"/>
    <attr format="reference" name="materialThemeOverlay"/>
    <attr format="dimension" name="minTouchTargetSize"/>
    <attr format="float" name="motionPathRotate"/>
    <attr format="float" name="motionProgress"/>
    <attr format="float" name="motionStagger"/>
    <attr format="reference|string" name="motionTarget"/>
    <attr format="reference" name="navigationViewStyle"/>
    <attr format="enum" name="pathMotionArc">
        <enum name="none" value="0"/>
        <enum name="startVertical" value="1"/>
        <enum name="startHorizontal" value="2"/>
        <enum name="flip" value="3"/>
    </attr>
    <attr format="float" name="path_percent"/>
    <attr format="float" name="perpendicularPath_percent"/>
    <attr format="reference|enum" name="pivotAnchor">
        <enum name="parent" value="0"/>
    </attr>
    <attr name="placeholder_emptyVisibility">
        <enum name="visible" value="0"/>
        <enum name="invisible" value="4"/>
        <enum name="gone" value="8"/>
    </attr>
    <attr format="reference" name="popupMenuBackground"/>
    <attr format="reference" name="recyclerViewStyle"/>
    <attr format="color" name="rippleColor"/>
    <attr format="color|reference" name="scrimBackground"/>
    <attr format="boolean" name="selectionRequired"/>
    <attr format="reference" name="shapeAppearanceLargeComponent"/>
    <attr format="reference" name="shapeAppearanceMediumComponent"/>
    <attr format="reference" name="shapeAppearanceSmallComponent"/>
    <attr format="reference" name="showMotionSpec"/>
    <attr format="boolean" name="singleSelection"/>
    <attr format="float" name="sizePercent"/>
    <attr format="reference" name="sliderStyle"/>
    <attr format="color" name="strokeColor"/>
    <attr format="dimension" name="strokeWidth"/>
    <attr format="reference" name="tabStyle"/>
    <attr format="reference" name="targetId"/>
    <attr format="reference" name="textAppearanceBody1"/>
    <attr format="reference" name="textAppearanceBody2"/>
    <attr format="reference" name="textAppearanceButton"/>
    <attr format="reference" name="textAppearanceCaption"/>
    <attr format="reference" name="textAppearanceHeadline1"/>
    <attr format="reference" name="textAppearanceHeadline2"/>
    <attr format="reference" name="textAppearanceHeadline3"/>
    <attr format="reference" name="textAppearanceHeadline4"/>
    <attr format="reference" name="textAppearanceHeadline5"/>
    <attr format="reference" name="textAppearanceHeadline6"/>
    <attr format="boolean" name="textAppearanceLineHeightEnabled"/>
    <attr format="reference" name="textAppearanceOverline"/>
    <attr format="reference" name="textAppearanceSubtitle1"/>
    <attr format="reference" name="textAppearanceSubtitle2"/>
    <attr format="reference" name="textInputStyle"/>
    <attr format="dimension" name="themeLineHeight"/>
    <attr format="string" name="title"/>
    <attr format="reference" name="tooltipStyle"/>
    <attr format="reference" name="touchAnchorId"/>
    <attr format="enum" name="touchAnchorSide">
        <enum name="top" value="0"/>
        <enum name="left" value="1"/>
        <enum name="right" value="2"/>
        <enum name="bottom" value="3"/>
        <enum name="middle" value="4"/>
        <enum name="start" value="5"/>
        <enum name="end" value="6"/>
    </attr>
    <attr format="color" name="trackColor"/>
    <attr format="string|enum" name="transitionEasing">
        <enum name="standard" value="0"/>
        <enum name="accelerate" value="1"/>
        <enum name="decelerate" value="2"/>
        <enum name="linear" value="3"/>
    </attr>
    <attr format="float" name="transitionPathRotate"/>
    <attr format="reference" name="transitionShapeAppearance"/>
    <attr format="boolean" name="useMaterialThemeColors"/>
    <attr format="enum" name="visibilityMode">
        <enum name="normal" value="0"/>
        <enum name="ignore" value="1"/>
    </attr>
    <attr format="integer" name="waveDecay"/>
    <attr format="float|dimension" name="waveOffset"/>
    <attr format="float" name="wavePeriod"/>
    <attr format="enum" name="waveShape">
        <enum name="sin" value="0"/>
        <enum name="square" value="1"/>
        <enum name="triangle" value="2"/>
        <enum name="sawtooth" value="3"/>
        <enum name="reverseSawtooth" value="4"/>
        <enum name="cos" value="5"/>
        <enum name="bounce" value="6"/>
    </attr>
    <attr format="enum" name="waveVariesBy">
        <enum name="position" value="0"/>
        <enum name="path" value="1"/>
    </attr>
    <bool name="abc_action_bar_embed_tabs">true</bool>
    <bool name="abc_allow_stacked_button_bar">false</bool>
    <bool name="abc_config_actionMenuItemAllCaps">true</bool>
    <bool name="mtrl_btn_textappearance_all_caps">true</bool>
    <color name="Azure">#F0FFFF</color>
    <color name="Black">#000000</color>
    <color name="Blue">#3C8DC4</color>
    <color name="CadetBlue">#5F9EA0</color>
    <color name="Cyan">#00FFFF</color>
    <color name="DarkBlue">#00008B</color>
    <color name="DarkCyan">#008B8B</color>
    <color name="DarkOrange">#FF8C00</color>
    <color name="DarkTurquoise">#00CED1</color>
    <color name="DeepSkyBlue">#00BFFF</color>
    <color name="Gold">#FFD700</color>
    <color name="Gray">#808080</color>
    <color name="Green">#008000</color>
    <color name="Honeydew">#F0FFF0</color>
    <color name="Lavender">#E6E6FA</color>
    <color name="LightCyan">#E1FFFF</color>
    <color name="Olive">#808000</color>
    <color name="Red">#FF0000</color>
    <color name="SpringGreen">#3CB371</color>
    <color name="White">#FFFFFF</color>
    <color name="abc_decor_view_status_guard">#ff000000</color>
    <color name="abc_decor_view_status_guard_light">#ffffffff</color>
    <color name="abc_search_url_text_normal">#7fa87f</color>
    <color name="abc_search_url_text_pressed">@android:color/black</color>
    <color name="abc_search_url_text_selected">@android:color/black</color>
    <color name="accent_material_dark">@color/material_deep_teal_200</color>
    <color name="accent_material_light">@color/material_deep_teal_500</color>
    <color name="androidx_core_ripple_material_light">#1f000000</color>
    <color name="androidx_core_secondary_text_default_material_light">#8a000000</color>
    <color name="background_floating_material_dark">@color/material_grey_800</color>
    <color name="background_floating_material_light">@android:color/white</color>
    <color name="background_material_dark">@color/material_grey_850</color>
    <color name="background_material_light">@color/material_grey_50</color>
    <color name="black">#FF000000</color>
    <color name="bright_foreground_disabled_material_dark">#80ffffff</color>
    <color name="bright_foreground_disabled_material_light">#80000000</color>
    <color name="bright_foreground_inverse_material_dark">@color/bright_foreground_material_light</color>
    <color name="bright_foreground_inverse_material_light">@color/bright_foreground_material_dark</color>
    <color name="bright_foreground_material_dark">@android:color/white</color>
    <color name="bright_foreground_material_light">@android:color/black</color>
    <color name="button_material_dark">#ff5a595b</color>
    <color name="button_material_light">#ffd6d7d7</color>
    <color name="cardview_dark_background">#FF424242</color>
    <color name="cardview_light_background">#FFFFFFFF</color>
    <color name="cardview_shadow_end_color">#03000000</color>
    <color name="cardview_shadow_start_color">#37000000</color>
    <color name="design_bottom_navigation_shadow_color">#14000000</color>
    <color name="design_dark_default_color_background">#121212</color>
    <color name="design_dark_default_color_error">#CF6679</color>
    <color name="design_dark_default_color_on_background">#FFFFFF</color>
    <color name="design_dark_default_color_on_error">#000000</color>
    <color name="design_dark_default_color_on_primary">#000000</color>
    <color name="design_dark_default_color_on_secondary">#000000</color>
    <color name="design_dark_default_color_on_surface">#FFFFFF</color>
    <color name="design_dark_default_color_primary">#BA86FC</color>
    <color name="design_dark_default_color_primary_dark">#000000</color>
    <color name="design_dark_default_color_primary_variant">#3700B3</color>
    <color name="design_dark_default_color_secondary">#03DAC6</color>
    <color name="design_dark_default_color_secondary_variant">#03DAC6</color>
    <color name="design_dark_default_color_surface">#121212</color>
    <color name="design_default_color_background">#FFFFFF</color>
    <color name="design_default_color_error">#B00020</color>
    <color name="design_default_color_on_background">#000000</color>
    <color name="design_default_color_on_error">#FFFFFF</color>
    <color name="design_default_color_on_primary">#FFFFFF</color>
    <color name="design_default_color_on_secondary">#000000</color>
    <color name="design_default_color_on_surface">#000000</color>
    <color name="design_default_color_primary">#6200EE</color>
    <color name="design_default_color_primary_dark">#3700B3</color>
    <color name="design_default_color_primary_variant">#3700B3</color>
    <color name="design_default_color_secondary">#03DAC6</color>
    <color name="design_default_color_secondary_variant">#018786</color>
    <color name="design_default_color_surface">#FFFFFF</color>
    <color name="design_fab_shadow_end_color">@android:color/transparent</color>
    <color name="design_fab_shadow_mid_color">#14000000</color>
    <color name="design_fab_shadow_start_color">#44000000</color>
    <color name="design_fab_stroke_end_inner_color">#0A000000</color>
    <color name="design_fab_stroke_end_outer_color">#0F000000</color>
    <color name="design_fab_stroke_top_inner_color">#1AFFFFFF</color>
    <color name="design_fab_stroke_top_outer_color">#2EFFFFFF</color>
    <color name="design_snackbar_background_color">#323232</color>
    <color name="dim_foreground_disabled_material_dark">#80bebebe</color>
    <color name="dim_foreground_disabled_material_light">#80323232</color>
    <color name="dim_foreground_material_dark">#ffbebebe</color>
    <color name="dim_foreground_material_light">#ff323232</color>
    <color name="error_color_material_dark">#ff7043</color>
    <color name="error_color_material_light">#ff5722</color>
    <color name="foreground_material_dark">@android:color/white</color>
    <color name="foreground_material_light">@android:color/black</color>
    <color name="highlighted_text_material_dark">#6680cbc4</color>
    <color name="highlighted_text_material_light">#66009688</color>
    <color name="material_blue_grey_800">#ff37474f</color>
    <color name="material_blue_grey_900">#ff263238</color>
    <color name="material_blue_grey_950">#ff21272b</color>
    <color name="material_deep_teal_200">#ff80cbc4</color>
    <color name="material_deep_teal_500">#ff008577</color>
    <color name="material_grey_100">#fff5f5f5</color>
    <color name="material_grey_300">#ffe0e0e0</color>
    <color name="material_grey_50">#fffafafa</color>
    <color name="material_grey_600">#ff757575</color>
    <color name="material_grey_800">#ff424242</color>
    <color name="material_grey_850">#ff303030</color>
    <color name="material_grey_900">#ff212121</color>
    <color name="mtrl_btn_text_color_disabled">#61000000</color>
    <color name="mtrl_btn_transparent_bg_color">#00ffffff</color>
    <color name="mtrl_scrim_color">#52000000</color>
    <color name="mtrl_textinput_default_box_stroke_color">#6B000000</color>
    <color name="mtrl_textinput_disabled_color">#1F000000</color>
    <color name="mtrl_textinput_filled_box_default_background_color">#0A000000</color>
    <color name="mtrl_textinput_focused_box_stroke_color">#00000000</color>
    <color name="mtrl_textinput_hovered_box_stroke_color">#DE000000</color>
    <color name="notification_action_color_filter">#ffffffff</color>
    <color name="notification_icon_bg_color">#ff9e9e9e</color>
    <color name="primary_dark_material_dark">@android:color/black</color>
    <color name="primary_dark_material_light">@color/material_grey_600</color>
    <color name="primary_material_dark">@color/material_grey_900</color>
    <color name="primary_material_light">@color/material_grey_100</color>
    <color name="primary_text_default_material_dark">#ffffffff</color>
    <color name="primary_text_default_material_light">#de000000</color>
    <color name="primary_text_disabled_material_dark">#4Dffffff</color>
    <color name="primary_text_disabled_material_light">#39000000</color>
    <color name="purple_200">#FFBB86FC</color>
    <color name="purple_500">#FF6200EE</color>
    <color name="purple_700">#FF3700B3</color>
    <color name="ripple_material_dark">#33ffffff</color>
    <color name="ripple_material_light">#1f000000</color>
    <color name="secondary_text_default_material_dark">#b3ffffff</color>
    <color name="secondary_text_default_material_light">#8a000000</color>
    <color name="secondary_text_disabled_material_dark">#36ffffff</color>
    <color name="secondary_text_disabled_material_light">#24000000</color>
    <color name="switch_thumb_disabled_material_dark">#ff616161</color>
    <color name="switch_thumb_disabled_material_light">#ffbdbdbd</color>
    <color name="switch_thumb_normal_material_dark">#ffbdbdbd</color>
    <color name="switch_thumb_normal_material_light">#fff1f1f1</color>
    <color name="teal_200">#FF03DAC5</color>
    <color name="teal_700">#FF018786</color>
    <color name="tooltip_background_dark">#e6616161</color>
    <color name="tooltip_background_light">#e6FFFFFF</color>
    <color name="white">#FFFFFFFF</color>
    <dimen name="abc_action_bar_content_inset_material">16dp</dimen>
    <dimen name="abc_action_bar_content_inset_with_nav">72dp</dimen>
    <dimen name="abc_action_bar_default_height_material">56dp</dimen>
    <dimen name="abc_action_bar_default_padding_end_material">0dp</dimen>
    <dimen name="abc_action_bar_default_padding_start_material">0dp</dimen>
    <dimen name="abc_action_bar_elevation_material">4dp</dimen>
    <dimen name="abc_action_bar_icon_vertical_padding_material">16dp</dimen>
    <dimen name="abc_action_bar_overflow_padding_end_material">10dp</dimen>
    <dimen name="abc_action_bar_overflow_padding_start_material">6dp</dimen>
    <dimen name="abc_action_bar_stacked_max_height">48dp</dimen>
    <dimen name="abc_action_bar_stacked_tab_max_width">180dp</dimen>
    <dimen name="abc_action_bar_subtitle_bottom_margin_material">5dp</dimen>
    <dimen name="abc_action_bar_subtitle_top_margin_material">-3dp</dimen>
    <dimen name="abc_action_button_min_height_material">48dp</dimen>
    <dimen name="abc_action_button_min_width_material">48dp</dimen>
    <dimen name="abc_action_button_min_width_overflow_material">36dp</dimen>
    <dimen name="abc_alert_dialog_button_bar_height">48dp</dimen>
    <dimen name="abc_alert_dialog_button_dimen">48dp</dimen>
    <dimen name="abc_button_inset_horizontal_material">@dimen/abc_control_inset_material</dimen>
    <dimen name="abc_button_inset_vertical_material">6dp</dimen>
    <dimen name="abc_button_padding_horizontal_material">8dp</dimen>
    <dimen name="abc_button_padding_vertical_material">@dimen/abc_control_padding_material</dimen>
    <dimen name="abc_cascading_menus_min_smallest_width">720dp</dimen>
    <dimen name="abc_config_prefDialogWidth">320dp</dimen>
    <dimen name="abc_control_corner_material">2dp</dimen>
    <dimen name="abc_control_inset_material">4dp</dimen>
    <dimen name="abc_control_padding_material">4dp</dimen>
    <dimen name="abc_dialog_corner_radius_material">2dp</dimen>
    <item name="abc_dialog_fixed_height_major" type="dimen">80%</item>
    <item name="abc_dialog_fixed_height_minor" type="dimen">100%</item>
    <item name="abc_dialog_fixed_width_major" type="dimen">320dp</item>
    <item name="abc_dialog_fixed_width_minor" type="dimen">320dp</item>
    <dimen name="abc_dialog_list_padding_bottom_no_buttons">8dp</dimen>
    <dimen name="abc_dialog_list_padding_top_no_title">8dp</dimen>
    <item name="abc_dialog_min_width_major" type="dimen">65%</item>
    <item name="abc_dialog_min_width_minor" type="dimen">95%</item>
    <dimen name="abc_dialog_padding_material">24dp</dimen>
    <dimen name="abc_dialog_padding_top_material">18dp</dimen>
    <dimen name="abc_dialog_title_divider_material">8dp</dimen>
    <item format="float" name="abc_disabled_alpha_material_dark" type="dimen">0.30</item>
    <item format="float" name="abc_disabled_alpha_material_light" type="dimen">0.26</item>
    <dimen name="abc_dropdownitem_icon_width">32dip</dimen>
    <dimen name="abc_dropdownitem_text_padding_left">8dip</dimen>
    <dimen name="abc_dropdownitem_text_padding_right">8dip</dimen>
    <dimen name="abc_edit_text_inset_bottom_material">7dp</dimen>
    <dimen name="abc_edit_text_inset_horizontal_material">4dp</dimen>
    <dimen name="abc_edit_text_inset_top_material">10dp</dimen>
    <dimen name="abc_floating_window_z">16dp</dimen>
    <dimen name="abc_list_item_height_large_material">80dp</dimen>
    <dimen name="abc_list_item_height_material">64dp</dimen>
    <dimen name="abc_list_item_height_small_material">48dp</dimen>
    <dimen name="abc_list_item_padding_horizontal_material">@dimen/abc_action_bar_content_inset_material</dimen>
    <dimen name="abc_panel_menu_list_width">296dp</dimen>
    <dimen name="abc_progress_bar_height_material">4dp</dimen>
    <dimen name="abc_search_view_preferred_height">48dip</dimen>
    <dimen name="abc_search_view_preferred_width">320dip</dimen>
    <dimen name="abc_seekbar_track_background_height_material">2dp</dimen>
    <dimen name="abc_seekbar_track_progress_height_material">2dp</dimen>
    <dimen name="abc_select_dialog_padding_start_material">20dp</dimen>
    <dimen name="abc_switch_padding">3dp</dimen>
    <dimen name="abc_text_size_body_1_material">14sp</dimen>
    <dimen name="abc_text_size_body_2_material">14sp</dimen>
    <dimen name="abc_text_size_button_material">14sp</dimen>
    <dimen name="abc_text_size_caption_material">12sp</dimen>
    <dimen name="abc_text_size_display_1_material">34sp</dimen>
    <dimen name="abc_text_size_display_2_material">45sp</dimen>
    <dimen name="abc_text_size_display_3_material">56sp</dimen>
    <dimen name="abc_text_size_display_4_material">112sp</dimen>
    <dimen name="abc_text_size_headline_material">24sp</dimen>
    <dimen name="abc_text_size_large_material">22sp</dimen>
    <dimen name="abc_text_size_medium_material">18sp</dimen>
    <dimen name="abc_text_size_menu_header_material">14sp</dimen>
    <dimen name="abc_text_size_menu_material">16sp</dimen>
    <dimen name="abc_text_size_small_material">14sp</dimen>
    <dimen name="abc_text_size_subhead_material">16sp</dimen>
    <dimen name="abc_text_size_subtitle_material_toolbar">16dp</dimen>
    <dimen name="abc_text_size_title_material">20sp</dimen>
    <dimen name="abc_text_size_title_material_toolbar">20dp</dimen>
    <dimen name="action_bar_size">16dp</dimen>
    <dimen name="appcompat_dialog_background_inset">16dp</dimen>
    <dimen name="cardview_compat_inset_shadow">1dp</dimen>
    <dimen name="cardview_default_elevation">2dp</dimen>
    <dimen name="cardview_default_radius">2dp</dimen>
    <dimen name="compat_button_inset_horizontal_material">4dp</dimen>
    <dimen name="compat_button_inset_vertical_material">6dp</dimen>
    <dimen name="compat_button_padding_horizontal_material">8dp</dimen>
    <dimen name="compat_button_padding_vertical_material">4dp</dimen>
    <dimen name="compat_control_corner_material">2dp</dimen>
    <dimen name="compat_notification_large_icon_max_height">320dp</dimen>
    <dimen name="compat_notification_large_icon_max_width">320dp</dimen>
    <dimen name="default_dimension">100dp</dimen>
    <dimen name="design_appbar_elevation">4dp</dimen>
    <dimen name="design_bottom_navigation_active_item_max_width">168dp</dimen>
    <dimen name="design_bottom_navigation_active_item_min_width">96dp</dimen>
    <dimen name="design_bottom_navigation_active_text_size">14sp</dimen>
    <dimen name="design_bottom_navigation_elevation">8dp</dimen>
    <dimen name="design_bottom_navigation_height">56dp</dimen>
    <dimen name="design_bottom_navigation_icon_size">24dp</dimen>
    <dimen name="design_bottom_navigation_item_max_width">96dp</dimen>
    <dimen name="design_bottom_navigation_item_min_width">56dp</dimen>
    <dimen name="design_bottom_navigation_margin">8dp</dimen>
    <dimen name="design_bottom_navigation_shadow_height">1dp</dimen>
    <dimen name="design_bottom_navigation_text_size">12sp</dimen>
    <dimen name="design_bottom_sheet_elevation">8dp</dimen>
    <dimen name="design_bottom_sheet_modal_elevation">16dp</dimen>
    <dimen name="design_bottom_sheet_peek_height_min">64dp</dimen>
    <dimen name="design_fab_border_width">0.5dp</dimen>
    <dimen name="design_fab_elevation">6dp</dimen>
    <dimen name="design_fab_image_size">24dp</dimen>
    <dimen name="design_fab_size_mini">40dp</dimen>
    <dimen name="design_fab_size_normal">56dp</dimen>
    <dimen name="design_fab_translation_z_hovered_focused">6dp</dimen>
    <dimen name="design_fab_translation_z_pressed">6dp</dimen>
    <dimen name="design_navigation_elevation">16dp</dimen>
    <dimen name="design_navigation_icon_padding">32dp</dimen>
    <dimen name="design_navigation_icon_size">24dp</dimen>
    <dimen name="design_navigation_item_horizontal_padding">16dp</dimen>
    <dimen name="design_navigation_item_icon_padding">32dp</dimen>
    <dimen name="design_navigation_max_width">280dp</dimen>
    <dimen name="design_navigation_padding_bottom">8dp</dimen>
    <dimen name="design_navigation_separator_vertical_padding">8dp</dimen>
    <dimen name="design_snackbar_action_inline_max_width">128dp</dimen>
    <item format="float" name="design_snackbar_action_text_color_alpha" type="dimen">1.0</item>
    <dimen name="design_snackbar_background_corner_radius">0dp</dimen>
    <dimen name="design_snackbar_elevation">6dp</dimen>
    <dimen name="design_snackbar_extra_spacing_horizontal">0dp</dimen>
    <dimen name="design_snackbar_max_width">-1px</dimen>
    <dimen name="design_snackbar_min_width">-1px</dimen>
    <dimen name="design_snackbar_padding_horizontal">12dp</dimen>
    <dimen name="design_snackbar_padding_vertical">14dp</dimen>
    <dimen name="design_snackbar_padding_vertical_2lines">24dp</dimen>
    <dimen name="design_snackbar_text_size">14sp</dimen>
    <dimen name="design_tab_max_width">264dp</dimen>
    <dimen name="design_tab_scrollable_min_width">72dp</dimen>
    <dimen name="design_tab_text_size">14sp</dimen>
    <dimen name="design_tab_text_size_2line">12sp</dimen>
    <dimen name="design_textinput_caption_translate_y">5dp</dimen>
    <item format="float" name="disabled_alpha_material_dark" type="dimen">0.30</item>
    <item format="float" name="disabled_alpha_material_light" type="dimen">0.26</item>
    <dimen name="fastscroll_default_thickness">8dp</dimen>
    <dimen name="fastscroll_margin">0dp</dimen>
    <dimen name="fastscroll_minimum_range">50dp</dimen>
    <item format="float" name="highlight_alpha_material_colored" type="dimen">0.26</item>
    <item format="float" name="highlight_alpha_material_dark" type="dimen">0.20</item>
    <item format="float" name="highlight_alpha_material_light" type="dimen">0.12</item>
    <item format="float" name="hint_alpha_material_dark" type="dimen">0.50</item>
    <item format="float" name="hint_alpha_material_light" type="dimen">0.38</item>
    <item format="float" name="hint_pressed_alpha_material_dark" type="dimen">0.70</item>
    <item format="float" name="hint_pressed_alpha_material_light" type="dimen">0.54</item>
    <dimen name="item_touch_helper_max_drag_scroll_per_frame">20dp</dimen>
    <dimen name="item_touch_helper_swipe_escape_max_velocity">800dp</dimen>
    <dimen name="item_touch_helper_swipe_escape_velocity">120dp</dimen>
    <item format="float" name="material_emphasis_disabled" type="dimen">0.38</item>
    <item format="float" name="material_emphasis_high_type" type="dimen">0.87</item>
    <item format="float" name="material_emphasis_medium" type="dimen">0.6</item>
    <dimen name="material_text_view_test_line_height">200px</dimen>
    <dimen name="material_text_view_test_line_height_override">100px</dimen>
    <dimen name="mtrl_alert_dialog_background_inset_bottom">80dp</dimen>
    <dimen name="mtrl_alert_dialog_background_inset_end">24dp</dimen>
    <dimen name="mtrl_alert_dialog_background_inset_start">24dp</dimen>
    <dimen name="mtrl_alert_dialog_background_inset_top">80dp</dimen>
    <dimen name="mtrl_alert_dialog_picker_background_inset">24dp</dimen>
    <dimen name="mtrl_badge_horizontal_edge_offset">4dp</dimen>
    <dimen name="mtrl_badge_long_text_horizontal_padding">4dp</dimen>
    <dimen name="mtrl_badge_radius">4dp</dimen>
    <dimen name="mtrl_badge_text_horizontal_edge_offset">6dp</dimen>
    <dimen name="mtrl_badge_text_size">10sp</dimen>
    <dimen name="mtrl_badge_with_text_radius">8dp</dimen>
    <dimen name="mtrl_bottomappbar_fabOffsetEndMode">60dp</dimen>
    <dimen name="mtrl_bottomappbar_fab_bottom_margin">16dp</dimen>
    <dimen name="mtrl_bottomappbar_fab_cradle_margin">5dp</dimen>
    <dimen name="mtrl_bottomappbar_fab_cradle_rounded_corner_radius">8dp</dimen>
    <dimen name="mtrl_bottomappbar_fab_cradle_vertical_offset">0dp</dimen>
    <dimen name="mtrl_bottomappbar_height">56dp</dimen>
    <dimen name="mtrl_btn_corner_radius">4dp</dimen>
    <dimen name="mtrl_btn_dialog_btn_min_width">64dp</dimen>
    <dimen name="mtrl_btn_disabled_elevation">0dp</dimen>
    <dimen name="mtrl_btn_disabled_z">0dp</dimen>
    <dimen name="mtrl_btn_elevation">2dp</dimen>
    <dimen name="mtrl_btn_focused_z">2dp</dimen>
    <dimen name="mtrl_btn_hovered_z">2dp</dimen>
    <dimen name="mtrl_btn_icon_btn_padding_left">12dp</dimen>
    <dimen name="mtrl_btn_icon_padding">8dp</dimen>
    <dimen name="mtrl_btn_inset">6dp</dimen>
    <item format="float" name="mtrl_btn_letter_spacing" type="dimen">0.07</item>
    <dimen name="mtrl_btn_padding_bottom">4dp</dimen>
    <dimen name="mtrl_btn_padding_left">16dp</dimen>
    <dimen name="mtrl_btn_padding_right">16dp</dimen>
    <dimen name="mtrl_btn_padding_top">4dp</dimen>
    <dimen name="mtrl_btn_pressed_z">6dp</dimen>
    <dimen name="mtrl_btn_stroke_size">1dp</dimen>
    <dimen name="mtrl_btn_text_btn_icon_padding">4dp</dimen>
    <dimen name="mtrl_btn_text_btn_padding_left">8dp</dimen>
    <dimen name="mtrl_btn_text_btn_padding_right">8dp</dimen>
    <dimen name="mtrl_btn_text_size">14sp</dimen>
    <dimen name="mtrl_btn_z">0dp</dimen>
    <dimen name="mtrl_calendar_action_height">52dp</dimen>
    <dimen name="mtrl_calendar_action_padding">8dp</dimen>
    <dimen name="mtrl_calendar_bottom_padding">0dp</dimen>
    <dimen name="mtrl_calendar_content_padding">12dp</dimen>
    <dimen name="mtrl_calendar_day_corner">15dp</dimen>
    <dimen name="mtrl_calendar_day_height">32dp</dimen>
    <dimen name="mtrl_calendar_day_horizontal_padding">3dp</dimen>
    <dimen name="mtrl_calendar_day_today_stroke">1dp</dimen>
    <dimen name="mtrl_calendar_day_vertical_padding">1dp</dimen>
    <dimen name="mtrl_calendar_day_width">36dp</dimen>
    <dimen name="mtrl_calendar_days_of_week_height">24dp</dimen>
    <dimen name="mtrl_calendar_dialog_background_inset">16dp</dimen>
    <dimen name="mtrl_calendar_header_content_padding">12dp</dimen>
    <dimen name="mtrl_calendar_header_content_padding_fullscreen">4dp</dimen>
    <dimen name="mtrl_calendar_header_divider_thickness">1dp</dimen>
    <dimen name="mtrl_calendar_header_height">120dp</dimen>
    <dimen name="mtrl_calendar_header_height_fullscreen">128dp</dimen>
    <dimen name="mtrl_calendar_header_selection_line_height">32dp</dimen>
    <dimen name="mtrl_calendar_header_text_padding">12dp</dimen>
    <dimen name="mtrl_calendar_header_toggle_margin_bottom">8dp</dimen>
    <dimen name="mtrl_calendar_header_toggle_margin_top">24dp</dimen>
    <dimen name="mtrl_calendar_landscape_header_width">0dp</dimen>
    <dimen name="mtrl_calendar_maximum_default_fullscreen_minor_axis">480dp</dimen>
    <dimen name="mtrl_calendar_month_horizontal_padding">2dp</dimen>
    <dimen name="mtrl_calendar_month_vertical_padding">0dp</dimen>
    <dimen name="mtrl_calendar_navigation_bottom_padding">4dp</dimen>
    <dimen name="mtrl_calendar_navigation_height">48dp</dimen>
    <dimen name="mtrl_calendar_navigation_top_padding">4dp</dimen>
    <dimen name="mtrl_calendar_pre_l_text_clip_padding">8dp</dimen>
    <dimen name="mtrl_calendar_selection_baseline_to_top_fullscreen">104dp</dimen>
    <dimen name="mtrl_calendar_selection_text_baseline_to_bottom">20dp</dimen>
    <dimen name="mtrl_calendar_selection_text_baseline_to_bottom_fullscreen">24dp</dimen>
    <dimen name="mtrl_calendar_selection_text_baseline_to_top">100dp</dimen>
    <dimen name="mtrl_calendar_text_input_padding_top">16dp</dimen>
    <dimen name="mtrl_calendar_title_baseline_to_top">28dp</dimen>
    <dimen name="mtrl_calendar_title_baseline_to_top_fullscreen">68dp</dimen>
    <dimen name="mtrl_calendar_year_corner">18dp</dimen>
    <dimen name="mtrl_calendar_year_height">52dp</dimen>
    <dimen name="mtrl_calendar_year_horizontal_padding">8dp</dimen>
    <dimen name="mtrl_calendar_year_vertical_padding">8dp</dimen>
    <dimen name="mtrl_calendar_year_width">88dp</dimen>
    <dimen name="mtrl_card_checked_icon_margin">8dp</dimen>
    <dimen name="mtrl_card_checked_icon_size">24dp</dimen>
    <dimen name="mtrl_card_corner_radius">4dp</dimen>
    <dimen name="mtrl_card_dragged_z">5dp</dimen>
    <dimen name="mtrl_card_elevation">1dp</dimen>
    <dimen name="mtrl_card_spacing">8dp</dimen>
    <dimen name="mtrl_chip_pressed_translation_z">3dp</dimen>
    <dimen name="mtrl_chip_text_size">14sp</dimen>
    <dimen name="mtrl_edittext_rectangle_top_offset">12dp</dimen>
    <dimen name="mtrl_exposed_dropdown_menu_popup_elevation">8dp</dimen>
    <dimen name="mtrl_exposed_dropdown_menu_popup_vertical_offset">-8dp</dimen>
    <dimen name="mtrl_exposed_dropdown_menu_popup_vertical_padding">8dp</dimen>
    <dimen name="mtrl_extended_fab_bottom_padding">12dp</dimen>
    <dimen name="mtrl_extended_fab_corner_radius">24dp</dimen>
    <dimen name="mtrl_extended_fab_disabled_elevation">0dp</dimen>
    <dimen name="mtrl_extended_fab_disabled_translation_z">0dp</dimen>
    <dimen name="mtrl_extended_fab_elevation">6dp</dimen>
    <dimen name="mtrl_extended_fab_end_padding">20dp</dimen>
    <dimen name="mtrl_extended_fab_end_padding_icon">24dp</dimen>
    <dimen name="mtrl_extended_fab_icon_size">24dp</dimen>
    <dimen name="mtrl_extended_fab_icon_text_spacing">16dp</dimen>
    <dimen name="mtrl_extended_fab_min_height">48dp</dimen>
    <dimen name="mtrl_extended_fab_min_width">120dp</dimen>
    <dimen name="mtrl_extended_fab_start_padding">20dp</dimen>
    <dimen name="mtrl_extended_fab_start_padding_icon">16dp</dimen>
    <dimen name="mtrl_extended_fab_top_padding">12dp</dimen>
    <dimen name="mtrl_extended_fab_translation_z_base">0dp</dimen>
    <dimen name="mtrl_extended_fab_translation_z_hovered_focused">2dp</dimen>
    <dimen name="mtrl_extended_fab_translation_z_pressed">6dp</dimen>
    <dimen name="mtrl_fab_elevation">6dp</dimen>
    <dimen name="mtrl_fab_min_touch_target">48dp</dimen>
    <dimen name="mtrl_fab_translation_z_hovered_focused">2dp</dimen>
    <dimen name="mtrl_fab_translation_z_pressed">6dp</dimen>
    <item format="float" name="mtrl_high_ripple_default_alpha" type="dimen">0.00</item>
    <item format="float" name="mtrl_high_ripple_focused_alpha" type="dimen">0.24</item>
    <item format="float" name="mtrl_high_ripple_hovered_alpha" type="dimen">0.08</item>
    <item format="float" name="mtrl_high_ripple_pressed_alpha" type="dimen">0.24</item>
    <dimen name="mtrl_large_touch_target">100dp</dimen>
    <item format="float" name="mtrl_low_ripple_default_alpha" type="dimen">0.00</item>
    <item format="float" name="mtrl_low_ripple_focused_alpha" type="dimen">0.12</item>
    <item format="float" name="mtrl_low_ripple_hovered_alpha" type="dimen">0.04</item>
    <item format="float" name="mtrl_low_ripple_pressed_alpha" type="dimen">0.12</item>
    <dimen name="mtrl_min_touch_target_size">48dp</dimen>
    <dimen name="mtrl_navigation_elevation">0dp</dimen>
    <dimen name="mtrl_navigation_item_horizontal_padding">22dp</dimen>
    <dimen name="mtrl_navigation_item_icon_padding">14dp</dimen>
    <dimen name="mtrl_navigation_item_icon_size">24dp</dimen>
    <dimen name="mtrl_navigation_item_shape_horizontal_margin">8dp</dimen>
    <dimen name="mtrl_navigation_item_shape_vertical_margin">4dp</dimen>
    <dimen name="mtrl_shape_corner_size_large_component">0dp</dimen>
    <dimen name="mtrl_shape_corner_size_medium_component">4dp</dimen>
    <dimen name="mtrl_shape_corner_size_small_component">4dp</dimen>
    <dimen name="mtrl_slider_halo_radius">24dp</dimen>
    <dimen name="mtrl_slider_label_padding">4dp</dimen>
    <dimen name="mtrl_slider_label_radius">13dp</dimen>
    <dimen name="mtrl_slider_label_square_side">26dp</dimen>
    <dimen name="mtrl_slider_thumb_elevation">1dp</dimen>
    <dimen name="mtrl_slider_thumb_radius">10dp</dimen>
    <dimen name="mtrl_slider_track_height">4dp</dimen>
    <dimen name="mtrl_slider_track_side_padding">16dp</dimen>
    <dimen name="mtrl_slider_track_top">24dp</dimen>
    <dimen name="mtrl_slider_widget_height">48dp</dimen>
    <item format="float" name="mtrl_snackbar_action_text_color_alpha" type="dimen">0.5</item>
    <dimen name="mtrl_snackbar_background_corner_radius">4dp</dimen>
    <item format="float" name="mtrl_snackbar_background_overlay_color_alpha" type="dimen">0.8</item>
    <dimen name="mtrl_snackbar_margin">8dp</dimen>
    <dimen name="mtrl_switch_thumb_elevation">4dp</dimen>
    <dimen name="mtrl_textinput_box_corner_radius_medium">4dp</dimen>
    <dimen name="mtrl_textinput_box_corner_radius_small">0dp</dimen>
    <dimen name="mtrl_textinput_box_label_cutout_padding">4dp</dimen>
    <dimen name="mtrl_textinput_box_stroke_width_default">1dp</dimen>
    <dimen name="mtrl_textinput_box_stroke_width_focused">2dp</dimen>
    <dimen name="mtrl_textinput_counter_margin_start">16dp</dimen>
    <dimen name="mtrl_textinput_end_icon_margin_start">4dp</dimen>
    <dimen name="mtrl_textinput_outline_box_expanded_padding">16dp</dimen>
    <dimen name="mtrl_textinput_start_icon_margin_end">4dp</dimen>
    <dimen name="mtrl_toolbar_default_height">56dp</dimen>
    <dimen name="mtrl_tooltip_arrowSize">14dp</dimen>
    <dimen name="mtrl_tooltip_cornerSize">4dp</dimen>
    <dimen name="mtrl_tooltip_minHeight">32dp</dimen>
    <dimen name="mtrl_tooltip_minWidth">32dp</dimen>
    <dimen name="mtrl_tooltip_padding">12dp</dimen>
    <dimen name="mtrl_transition_shared_axis_slide_distance">30dp</dimen>
    <dimen name="notification_action_icon_size">32dp</dimen>
    <dimen name="notification_action_text_size">13sp</dimen>
    <dimen name="notification_big_circle_margin">12dp</dimen>
    <dimen name="notification_content_margin_start">8dp</dimen>
    <dimen name="notification_large_icon_height">64dp</dimen>
    <dimen name="notification_large_icon_width">64dp</dimen>
    <dimen name="notification_main_column_padding_top">10dp</dimen>
    <dimen name="notification_media_narrow_margin">@dimen/notification_content_margin_start</dimen>
    <dimen name="notification_right_icon_size">16dp</dimen>
    <dimen name="notification_right_side_padding_top">2dp</dimen>
    <dimen name="notification_small_icon_background_padding">3dp</dimen>
    <dimen name="notification_small_icon_size_as_large">24dp</dimen>
    <dimen name="notification_subtext_size">13sp</dimen>
    <dimen name="notification_top_pad">10dp</dimen>
    <dimen name="notification_top_pad_large_text">5dp</dimen>
    <dimen name="test_mtrl_calendar_day_cornerSize">52dp</dimen>
    <dimen name="tooltip_corner_radius">2dp</dimen>
    <dimen name="tooltip_horizontal_padding">16dp</dimen>
    <dimen name="tooltip_margin">8dp</dimen>
    <dimen name="tooltip_precise_anchor_extra_offset">8dp</dimen>
    <dimen name="tooltip_precise_anchor_threshold">96dp</dimen>
    <dimen name="tooltip_vertical_padding">6.5dp</dimen>
    <dimen name="tooltip_y_offset_non_touch">0dp</dimen>
    <dimen name="tooltip_y_offset_touch">16dp</dimen>
    <drawable name="notification_template_icon_bg">#3333B5E5</drawable>
    <drawable name="notification_template_icon_low_bg">#0cffffff</drawable>
    <item name="accessibility_action_clickable_span" type="id"/>
    <item name="accessibility_custom_action_0" type="id"/>
    <item name="accessibility_custom_action_1" type="id"/>
    <item name="accessibility_custom_action_10" type="id"/>
    <item name="accessibility_custom_action_11" type="id"/>
    <item name="accessibility_custom_action_12" type="id"/>
    <item name="accessibility_custom_action_13" type="id"/>
    <item name="accessibility_custom_action_14" type="id"/>
    <item name="accessibility_custom_action_15" type="id"/>
    <item name="accessibility_custom_action_16" type="id"/>
    <item name="accessibility_custom_action_17" type="id"/>
    <item name="accessibility_custom_action_18" type="id"/>
    <item name="accessibility_custom_action_19" type="id"/>
    <item name="accessibility_custom_action_2" type="id"/>
    <item name="accessibility_custom_action_20" type="id"/>
    <item name="accessibility_custom_action_21" type="id"/>
    <item name="accessibility_custom_action_22" type="id"/>
    <item name="accessibility_custom_action_23" type="id"/>
    <item name="accessibility_custom_action_24" type="id"/>
    <item name="accessibility_custom_action_25" type="id"/>
    <item name="accessibility_custom_action_26" type="id"/>
    <item name="accessibility_custom_action_27" type="id"/>
    <item name="accessibility_custom_action_28" type="id"/>
    <item name="accessibility_custom_action_29" type="id"/>
    <item name="accessibility_custom_action_3" type="id"/>
    <item name="accessibility_custom_action_30" type="id"/>
    <item name="accessibility_custom_action_31" type="id"/>
    <item name="accessibility_custom_action_4" type="id"/>
    <item name="accessibility_custom_action_5" type="id"/>
    <item name="accessibility_custom_action_6" type="id"/>
    <item name="accessibility_custom_action_7" type="id"/>
    <item name="accessibility_custom_action_8" type="id"/>
    <item name="accessibility_custom_action_9" type="id"/>
    <item name="action_bar_activity_content" type="id"/>
    <item name="action_bar_spinner" type="id"/>
    <item name="action_menu_divider" type="id"/>
    <item name="action_menu_presenter" type="id"/>
    <item name="fragment_container_view_tag" type="id"/>
    <item name="ghost_view" type="id"/>
    <item name="ghost_view_holder" type="id"/>
    <item name="glide_custom_view_target_tag" type="id"/>
    <item name="home" type="id"/>
    <item name="item_touch_helper_previous_elevation" type="id"/>
    <item name="line1" type="id"/>
    <item name="line3" type="id"/>
    <item name="motion_base" type="id"/>
    <item name="mtrl_card_checked_layer_id" type="id"/>
    <item name="mtrl_child_content_container" type="id"/>
    <item name="mtrl_internal_children_alpha_tag" type="id"/>
    <item name="mtrl_motion_snapshot_view" type="id"/>
    <item name="parent_matrix" type="id"/>
    <item name="progress_circular" type="id"/>
    <item name="progress_horizontal" type="id"/>
    <item name="row_index_key" type="id"/>
    <item name="save_non_transition_alpha" type="id"/>
    <item name="save_overlay_view" type="id"/>
    <item name="snackbar_action" type="id"/>
    <item name="snackbar_text" type="id"/>
    <item name="special_effects_controller_view_tag" type="id"/>
    <item name="split_action_bar" type="id"/>
    <item name="tag_accessibility_actions" type="id"/>
    <item name="tag_accessibility_clickable_spans" type="id"/>
    <item name="tag_accessibility_heading" type="id"/>
    <item name="tag_accessibility_pane_title" type="id"/>
    <item name="tag_screen_reader_focusable" type="id"/>
    <item name="tag_transition_group" type="id"/>
    <item name="tag_unhandled_key_event_manager" type="id"/>
    <item name="tag_unhandled_key_listeners" type="id"/>
    <item name="text" type="id"/>
    <item name="text2" type="id"/>
    <item name="textinput_counter" type="id"/>
    <item name="textinput_error" type="id"/>
    <item name="textinput_helper_text" type="id"/>
    <item name="textinput_placeholder" type="id"/>
    <item name="textinput_prefix_text" type="id"/>
    <item name="textinput_suffix_text" type="id"/>
    <item name="title" type="id"/>
    <item name="transition_current_scene" type="id"/>
    <item name="transition_layout_save" type="id"/>
    <item name="transition_position" type="id"/>
    <item name="transition_scene_layoutid_cache" type="id"/>
    <item name="transition_transform" type="id"/>
    <item name="up" type="id"/>
    <item name="view_offset_helper" type="id"/>
    <id name="view_tree_lifecycle_owner"/>
    <id name="view_tree_saved_state_registry_owner"/>
    <id name="view_tree_view_model_store_owner"/>
    <item name="visible_removing_fragment_view_tag" type="id"/>
    <integer name="abc_config_activityDefaultDur">220</integer>
    <integer name="abc_config_activityShortDur">150</integer>
    <integer name="app_bar_elevation_anim_duration">150</integer>
    <integer name="bottom_sheet_slide_duration">150</integer>
    <integer name="cancel_button_image_alpha">127</integer>
    <integer name="config_tooltipAnimTime">150</integer>
    <integer name="design_snackbar_text_max_lines">2</integer>
    <integer name="design_tab_indicator_anim_duration_ms">300</integer>
    <integer name="hide_password_duration">320</integer>
    <integer name="mtrl_badge_max_character_count">4</integer>
    <integer name="mtrl_btn_anim_delay_ms">100</integer>
    <integer name="mtrl_btn_anim_duration_ms">100</integer>
    <integer name="mtrl_calendar_header_orientation">1</integer>
    <integer name="mtrl_calendar_selection_text_lines">1</integer>
    <integer name="mtrl_calendar_year_selector_span">3</integer>
    <integer name="mtrl_card_anim_delay_ms">75</integer>
    <integer name="mtrl_card_anim_duration_ms">120</integer>
    <integer name="mtrl_chip_anim_duration">100</integer>
    <integer name="mtrl_tab_indicator_anim_duration_ms">250</integer>
    <integer name="show_password_duration">200</integer>
    <integer name="status_bar_notification_info_maxnum">999</integer>
    <plurals description="Plural form content description for number of new notifications [CHAR_LIMIT=NONE]" name="mtrl_badge_content_description">
    <item quantity="one"><ns1:g id="count">%d</ns1:g> new notification</item>
    <item quantity="other"><ns1:g id="count">%d</ns1:g> new notifications</item>
  </plurals>
    <string name="abc_action_bar_home_description">Navigate home</string>
    <string name="abc_action_bar_up_description">Navigate up</string>
    <string name="abc_action_menu_overflow_description">More options</string>
    <string name="abc_action_mode_done">Done</string>
    <string name="abc_activity_chooser_view_see_all">See all</string>
    <string name="abc_activitychooserview_choose_application">Choose an app</string>
    <string name="abc_capital_off">OFF</string>
    <string name="abc_capital_on">ON</string>
    <string name="abc_menu_alt_shortcut_label">Alt+</string>
    <string name="abc_menu_ctrl_shortcut_label">Ctrl+</string>
    <string name="abc_menu_delete_shortcut_label">delete</string>
    <string name="abc_menu_enter_shortcut_label">enter</string>
    <string name="abc_menu_function_shortcut_label">Function+</string>
    <string name="abc_menu_meta_shortcut_label">Meta+</string>
    <string name="abc_menu_shift_shortcut_label">Shift+</string>
    <string name="abc_menu_space_shortcut_label">space</string>
    <string name="abc_menu_sym_shortcut_label">Sym+</string>
    <string name="abc_prepend_shortcut_label">Menu+</string>
    <string name="abc_search_hint">Search…</string>
    <string name="abc_searchview_description_clear">Clear query</string>
    <string name="abc_searchview_description_query">Search query</string>
    <string name="abc_searchview_description_search">Search</string>
    <string name="abc_searchview_description_submit">Submit query</string>
    <string name="abc_searchview_description_voice">Voice search</string>
    <string name="abc_shareactionprovider_share_with">Share with</string>
    <string name="abc_shareactionprovider_share_with_application">Share with <ns1:g example="Mail" id="application_name">%s</ns1:g></string>
    <string name="abc_toolbar_collapse_description">Collapse</string>
    <string name="app_name">OrderSystem</string>
    <string name="appbar_scrolling_view_behavior" translatable="false">com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior</string>
    <string name="bottom_sheet_behavior" translatable="false">com.google.android.material.bottomsheet.BottomSheetBehavior</string>
    <string name="character_counter_content_description">Characters entered %1$d of %2$d</string>
    <string name="character_counter_overflowed_content_description">Character limit exceeded %1$d of %2$d</string>
    <string name="character_counter_pattern" translatable="false">%1$d/%2$d</string>
    <string name="chip_text">Chip text</string>
    <string name="clear_text_end_icon_content_description">Clear text</string>
    <string name="error_icon_content_description">Error</string>
    <string name="exposed_dropdown_menu_content_description">Show dropdown menu</string>
    <string name="fab_transformation_scrim_behavior" translatable="false">com.google.android.material.transformation.FabTransformationScrimBehavior</string>
    <string name="fab_transformation_sheet_behavior" translatable="false">com.google.android.material.transformation.FabTransformationSheetBehavior</string>
    <string name="hide_bottom_view_on_scroll_behavior" translatable="false">com.google.android.material.behavior.HideBottomViewOnScrollBehavior</string>
    <string description="Content description for an icon that appears in the title area of a dialog" name="icon_content_description">Dialog Icon</string>
    <string name="item_view_role_description">Tab</string>
    <string name="login">用户登录</string>
    <string description="Content description for the thumb with the maximum value in a Range slider. [CHAR_LIMIT=NONE]" name="material_slider_range_end">Range end, </string>
    <string description="Content description for the thumb with the minimum value in a Range slider. [CHAR_LIMIT=NONE]" name="material_slider_range_start">Range start, </string>
    <string description="Content description for new notification (no number count) [CHAR_LIMIT=NONE]" name="mtrl_badge_numberless_content_description">New notification</string>
    <string description="Content description for a close icon that lets the user remove content from the screen" name="mtrl_chip_close_icon_content_description">Remove %1$s</string>
    <string description="Plural form content description for when the number of new notifications exceeds a maximum count[CHAR_LIMIT=NONE]" name="mtrl_exceed_max_badge_number_content_description" ns2:ignore="PluralsCandidate">More than <ns1:g example="999" id="maximum number">%1$d</ns1:g> new notifications</string>
    <string name="mtrl_exceed_max_badge_number_suffix" translatable="false"><ns1:g example="999" id="maximum number">%1$d</ns1:g><ns1:g example="+" id="suffix">%2$s</ns1:g></string>
    <string description="a11y string to indicate this button moves the calendar to the next month [CHAR_LIMIT=NONE]" name="mtrl_picker_a11y_next_month">Change to next month</string>
    <string description="a11y string to indicate this button moves the calendar to the previous month [CHAR_LIMIT=NONE]" name="mtrl_picker_a11y_prev_month">Change to previous month</string>
    <string description="a11y string read on selection change to indicate the new selection [CHAR_LIMIT=NONE]" name="mtrl_picker_announce_current_selection">Current selection: %1$s</string>
    <string description="Button text to indicate that the widget will ignore the user&apos;s selection [CHAR_LIMIT=16]" name="mtrl_picker_cancel" translatable="false">@android:string/cancel</string>
    <string description="Button text to indicate that the widget will save the user&apos;s selection [CHAR_LIMIT=16]" name="mtrl_picker_confirm">@android:string/ok</string>
    <string description="A single date [CHAR_LIMIT=60]" name="mtrl_picker_date_header_selected">%1$s</string>
    <string description="Indicates that the user must take the action of picking a date within the calendar [CHAR_LIMIT=60]" name="mtrl_picker_date_header_title">Select a Date</string>
    <string description="Placeholder for a single date [CHAR_LIMIT=60]" name="mtrl_picker_date_header_unselected">Selected date</string>
    <string description="a11y string to indicate this is a header for a column of days for one day of the week (e.g., Monday) [CHAR_LIMIT=NONE]" name="mtrl_picker_day_of_week_column_header">Column of days: %1$s</string>
    <string description="Indicates that the user entered date cannot be parsed because its format is wrong. [CHAR_LIMIT=36]" name="mtrl_picker_invalid_format">Invalid format.</string>
    <string description="Tells a user what an example valid entry looks like. [CHAR_LIMIT=18]" name="mtrl_picker_invalid_format_example">Example: %1$s</string>
    <string description="Tells a user what format is expected for their date entry. [CHAR_LIMIT=18]" name="mtrl_picker_invalid_format_use">Use: %1$s</string>
    <string description="Notifies the user that the two entered dates do not represent a valid range of dates [CHAR_LIMIT=36]" name="mtrl_picker_invalid_range">Invalid range.</string>
    <string description="a11y string that informs the user that tapping this button will switch the year [CHAR_LIMIT=NONE]" name="mtrl_picker_navigate_to_year_description">Navigate to year %1$s</string>
    <string description="Notifies the user that the entered date is outside the allowed range [CHAR_LIMIT=36]" name="mtrl_picker_out_of_range">Out of range: %1$s</string>
    <string description="Placeholders for two dates separated by a dash representing a range where end date has been selected [CHAR_LIMIT=60]" name="mtrl_picker_range_header_only_end_selected">Start date – %1$s</string>
    <string description="Placeholders for two dates separated by a dash representing a range where start date has been selected [CHAR_LIMIT=60]" name="mtrl_picker_range_header_only_start_selected">%1$s – End date</string>
    <string description="Two dates separated by a dash representing a range [CHAR_LIMIT=60]" name="mtrl_picker_range_header_selected">%1$s – %2$s</string>
    <string description="Indicates that the user must take the action of picking dates within the calendar to form a range [CHAR_LIMIT=60]" name="mtrl_picker_range_header_title">Select a Date Range</string>
    <string description="Placeholders for two dates separated by a dash representing a range [CHAR_LIMIT=60]" name="mtrl_picker_range_header_unselected">Start date – End date</string>
    <string description="Confirms the selection [CHAR_LIMIT=12]" name="mtrl_picker_save">Save</string>
    <string description="Label for a single date selected by the user [CHAR_LIMIT=60]" name="mtrl_picker_text_input_date_hint">Date</string>
    <string description="Label for the end date in a range selected by the user [CHAR_LIMIT=60]" name="mtrl_picker_text_input_date_range_end_hint">End date</string>
    <string description="Label for the start date in a range selected by the user [CHAR_LIMIT=60]" name="mtrl_picker_text_input_date_range_start_hint">Start date</string>
    <string description="A 1 character abbreviation for day. It will be part of a string such as dd/mm/yyyy or mm/dd/yyyy or y.mm.dd. [CHAR_LIMIT=2]" name="mtrl_picker_text_input_day_abbr">d</string>
    <string description="A 1 character abbreviation for month. It will be part of a string such as dd/mm/yyyy or mm/dd/yyyy or y.mm.dd. [CHAR_LIMIT=2]" name="mtrl_picker_text_input_month_abbr">m</string>
    <string description="A 1 character abbreviation for year. It will be part of a string such as dd/mm/yyyy or mm/dd/yyyy or y.mm.dd. [CHAR_LIMIT=2]" name="mtrl_picker_text_input_year_abbr">y</string>
    <string description="a11y string to indicate this button changes the input mode to a calendar [CHAR_LIMIT=NONE]" name="mtrl_picker_toggle_to_calendar_input_mode">Switch to calendar input mode</string>
    <string description="a11y string to indicate this button switches the user to choosing a day [CHAR_LIMIT=NONE]" name="mtrl_picker_toggle_to_day_selection">Tap to switch to selecting a day</string>
    <string description="a11y string to indicate this button changes the input mode to a text field [CHAR_LIMIT=NONE]" name="mtrl_picker_toggle_to_text_input_mode">Switch to text input mode</string>
    <string description="a11y string to indicate this button switches the user to choosing a year [CHAR_LIMIT=NONE]" name="mtrl_picker_toggle_to_year_selection">Tap to switch to selecting a year</string>
    <string name="order">历史订单</string>
    <string name="order_info">订单信息</string>
    <string name="password_toggle_content_description">Show password</string>
    <string name="path_password_eye" translatable="false">M12,4.5C7,4.5 2.73,7.61 1,12c1.73,4.39 6,7.5 11,7.5s9.27,-3.11 11,-7.5c-1.73,-4.39 -6,-7.5 -11,-7.5zM12,17c-2.76,0 -5,-2.24 -5,-5s2.24,-5 5,-5 5,2.24 5,5 -2.24,5 -5,5zM12,9c-1.66,0 -3,1.34 -3,3s1.34,3 3,3 3,-1.34 3,-3 -1.34,-3 -3,-3z</string>
    <string name="path_password_eye_mask_strike_through" translatable="false">M2,4.27 L19.73,22 L22.27,19.46 L4.54,1.73 L4.54,1 L23,1 L23,23 L1,23 L1,4.27 Z</string>
    <string name="path_password_eye_mask_visible" translatable="false">M2,4.27 L2,4.27 L4.54,1.73 L4.54,1.73 L4.54,1 L23,1 L23,23 L1,23 L1,4.27 Z</string>
    <string name="path_password_strike_through" translatable="false">M3.27,4.27 L19.74,20.74</string>
    <string name="person">个人信息</string>
    <string name="register">用户注册</string>
    <string name="search_menu_title">Search</string>
    <string name="share">分享软件</string>
    <string name="status_bar_notification_info_overflow">999+</string>
    <style name="AlertDialog.AppCompat" parent="Base.AlertDialog.AppCompat"/>
    <style name="AlertDialog.AppCompat.Light" parent="Base.AlertDialog.AppCompat.Light"/>
    <style name="AndroidThemeColorAccentYellow" ns2:ignore="NewApi">
    <item name="android:colorAccent">#FFFFFF00</item>
  </style>
    <style name="Animation.AppCompat.Dialog" parent="Base.Animation.AppCompat.Dialog"/>
    <style name="Animation.AppCompat.DropDownUp" parent="Base.Animation.AppCompat.DropDownUp"/>
    <style name="Animation.AppCompat.Tooltip" parent="Base.Animation.AppCompat.Tooltip"/>
    <style name="Animation.Design.BottomSheetDialog" parent="Animation.AppCompat.Dialog">
    <item name="android:windowEnterAnimation">@anim/design_bottom_sheet_slide_in</item>
    <item name="android:windowExitAnimation">@anim/design_bottom_sheet_slide_out</item>
  </style>
    <style name="Animation.MaterialComponents.BottomSheetDialog" parent="Animation.AppCompat.Dialog">
    <item name="android:windowEnterAnimation">@anim/mtrl_bottom_sheet_slide_in</item>
    <item name="android:windowExitAnimation">@anim/mtrl_bottom_sheet_slide_out</item>
  </style>
    <style name="AppTheme" parent="Theme.AppCompat.Light.NoActionBar">
        
        <item name="colorPrimary">@color/DarkCyan</item>
        <item name="colorPrimaryDark">@color/black</item>
        <item name="colorAccent">@color/Red</item>
    </style>
    <style name="Base.AlertDialog.AppCompat" parent="android:Widget">
        <item name="android:layout">@layout/abc_alert_dialog_material</item>
        <item name="listLayout">@layout/abc_select_dialog_material</item>
        <item name="listItemLayout">@layout/select_dialog_item_material</item>
        <item name="multiChoiceItemLayout">@layout/select_dialog_multichoice_material</item>
        <item name="singleChoiceItemLayout">@layout/select_dialog_singlechoice_material</item>
        <item name="buttonIconDimen">@dimen/abc_alert_dialog_button_dimen</item>
    </style>
    <style name="Base.AlertDialog.AppCompat.Light" parent="Base.AlertDialog.AppCompat"/>
    <style name="Base.Animation.AppCompat.Dialog" parent="android:Animation">
        <item name="android:windowEnterAnimation">@anim/abc_popup_enter</item>
        <item name="android:windowExitAnimation">@anim/abc_popup_exit</item>
    </style>
    <style name="Base.Animation.AppCompat.DropDownUp" parent="android:Animation">
        <item name="android:windowEnterAnimation">@anim/abc_grow_fade_in_from_bottom</item>
        <item name="android:windowExitAnimation">@anim/abc_shrink_fade_out_from_bottom</item>
    </style>
    <style name="Base.Animation.AppCompat.Tooltip" parent="android:Animation">
        <item name="android:windowEnterAnimation">@anim/abc_tooltip_enter</item>
        <item name="android:windowExitAnimation">@anim/abc_tooltip_exit</item>
    </style>
    <style name="Base.CardView" parent="android:Widget">
        <item name="cardCornerRadius">@dimen/cardview_default_radius</item>
        <item name="cardElevation">@dimen/cardview_default_elevation</item>
        <item name="cardMaxElevation">@dimen/cardview_default_elevation</item>
        <item name="cardUseCompatPadding">false</item>
        <item name="cardPreventCornerOverlap">true</item>
    </style>
    <style name="Base.DialogWindowTitle.AppCompat" parent="android:Widget">
        <item name="android:maxLines">1</item>
        <item name="android:scrollHorizontally">true</item>
        <item name="android:textAppearance">@style/TextAppearance.AppCompat.Title</item>
    </style>
    <style name="Base.DialogWindowTitleBackground.AppCompat" parent="android:Widget">
        <item name="android:background">@null</item>
        <item name="android:paddingLeft">?attr/dialogPreferredPadding</item>
        <item name="android:paddingRight">?attr/dialogPreferredPadding</item>
        <item name="android:paddingTop">@dimen/abc_dialog_padding_top_material</item>
    </style>
    <style name="Base.MaterialAlertDialog.MaterialComponents.Title.Icon" parent="android:Widget">
    <item name="android:layout_width">32dip</item>
    <item name="android:layout_height">32dip</item>
    <item name="android:scaleType">fitCenter</item>
    <item name="android:src">@null</item>
    <item name="android:contentDescription">@string/icon_content_description</item>
  </style>
    <style name="Base.MaterialAlertDialog.MaterialComponents.Title.Panel" parent="android:Widget">
    <item name="android:layout_width">match_parent</item>
    <item name="android:layout_height">wrap_content</item>
    <item name="android:paddingLeft">?attr/dialogPreferredPadding</item>
    <item name="android:paddingRight">?attr/dialogPreferredPadding</item>
    <item name="android:paddingTop">@dimen/abc_dialog_padding_top_material</item>
  </style>
    <style name="Base.MaterialAlertDialog.MaterialComponents.Title.Text" parent="RtlOverlay.DialogWindowTitle.AppCompat">
    <item name="android:textAppearance">?attr/textAppearanceSubtitle1</item>
    <item name="android:textColor">@color/material_on_surface_emphasis_high_type</item>
    <item name="android:layout_width">wrap_content</item>
    <item name="android:layout_height">wrap_content</item>
    <item name="android:ellipsize">end</item>
    <item name="android:singleLine">true</item>
  </style>
    <style name="Base.TextAppearance.AppCompat" parent="android:TextAppearance">
        <item name="android:textColor">?android:textColorPrimary</item>
        <item name="android:textColorHint">?android:textColorHint</item>
        <item name="android:textColorHighlight">?android:textColorHighlight</item>
        <item name="android:textColorLink">?android:textColorLink</item>
        <item name="android:textSize">@dimen/abc_text_size_body_1_material</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Body1">
        <item name="android:textSize">@dimen/abc_text_size_body_1_material</item>
        <item name="android:textColor">?android:textColorPrimary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Body2">
        <item name="android:textSize">@dimen/abc_text_size_body_2_material</item>
        <item name="android:textColor">?android:textColorPrimary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Button">
        <item name="android:textSize">@dimen/abc_text_size_button_material</item>
        <item name="android:textAllCaps">true</item>
        <item name="android:textColor">?android:textColorPrimary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Caption">
        <item name="android:textSize">@dimen/abc_text_size_caption_material</item>
        <item name="android:textColor">?android:textColorSecondary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Display1">
        <item name="android:textSize">@dimen/abc_text_size_display_1_material</item>
        <item name="android:textColor">?android:textColorSecondary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Display2">
        <item name="android:textSize">@dimen/abc_text_size_display_2_material</item>
        <item name="android:textColor">?android:textColorSecondary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Display3">
        <item name="android:textSize">@dimen/abc_text_size_display_3_material</item>
        <item name="android:textColor">?android:textColorSecondary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Display4">
        <item name="android:textSize">@dimen/abc_text_size_display_4_material</item>
        <item name="android:textColor">?android:textColorSecondary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Headline">
        <item name="android:textSize">@dimen/abc_text_size_headline_material</item>
        <item name="android:textColor">?android:textColorPrimary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Inverse">
        <item name="android:textColor">?android:attr/textColorPrimaryInverse</item>
        <item name="android:textColorHint">?android:attr/textColorHintInverse</item>
        <item name="android:textColorHighlight">?android:attr/textColorHighlightInverse</item>
        <item name="android:textColorLink">?android:attr/textColorLinkInverse</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Large">
        <item name="android:textSize">@dimen/abc_text_size_large_material</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Large.Inverse">
        <item name="android:textColor">?android:attr/textColorPrimaryInverse</item>
        <item name="android:textColorHint">?android:attr/textColorHintInverse</item>
        <item name="android:textColorHighlight">?android:attr/textColorHighlightInverse</item>
        <item name="android:textColorLink">?android:attr/textColorLinkInverse</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Medium">
        <item name="android:textSize">@dimen/abc_text_size_medium_material</item>
        <item name="android:textColor">?android:attr/textColorSecondary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Medium.Inverse">
        <item name="android:textColor">?android:attr/textColorSecondaryInverse</item>
        <item name="android:textColorHint">?android:attr/textColorHintInverse</item>
        <item name="android:textColorHighlight">?android:attr/textColorHighlightInverse</item>
        <item name="android:textColorLink">?android:attr/textColorLinkInverse</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Menu">
        <item name="android:textSize">@dimen/abc_text_size_menu_material</item>
        <item name="android:textColor">?android:textColorPrimary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.SearchResult" parent="">
        <item name="android:textStyle">normal</item>
        <item name="android:textColor">?android:textColorPrimary</item>
        <item name="android:textColorHint">?android:textColorHint</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.SearchResult.Subtitle">
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">?android:textColorSecondary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.SearchResult.Title">
        <item name="android:textSize">18sp</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Small">
        <item name="android:textSize">@dimen/abc_text_size_small_material</item>
        <item name="android:textColor">?android:attr/textColorTertiary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Small.Inverse">
        <item name="android:textColor">?android:attr/textColorTertiaryInverse</item>
        <item name="android:textColorHint">?android:attr/textColorHintInverse</item>
        <item name="android:textColorHighlight">?android:attr/textColorHighlightInverse</item>
        <item name="android:textColorLink">?android:attr/textColorLinkInverse</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Subhead">
        <item name="android:textSize">@dimen/abc_text_size_subhead_material</item>
        <item name="android:textColor">?android:textColorPrimary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Subhead.Inverse">
        <item name="android:textColor">?android:attr/textColorPrimaryInverse</item>
        <item name="android:textColorHint">?android:attr/textColorHintInverse</item>
        <item name="android:textColorHighlight">?android:attr/textColorHighlightInverse</item>
        <item name="android:textColorLink">?android:attr/textColorLinkInverse</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Title">
        <item name="android:textSize">@dimen/abc_text_size_title_material</item>
        <item name="android:textColor">?android:textColorPrimary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Title.Inverse">
        <item name="android:textColor">?android:attr/textColorPrimaryInverse</item>
        <item name="android:textColorHint">?android:attr/textColorHintInverse</item>
        <item name="android:textColorHighlight">?android:attr/textColorHighlightInverse</item>
        <item name="android:textColorLink">?android:attr/textColorLinkInverse</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Tooltip">
        <item name="android:textSize">14sp</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Menu" parent="TextAppearance.AppCompat.Button">
        <item name="android:textColor">?attr/actionMenuTextColor</item>
        <item name="textAllCaps">@bool/abc_config_actionMenuItemAllCaps</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle" parent="TextAppearance.AppCompat.Subhead">
        <item name="android:textSize">@dimen/abc_text_size_subtitle_material_toolbar</item>
        <item name="android:textColor">?android:attr/textColorSecondary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse" parent="TextAppearance.AppCompat.Subhead.Inverse">
        <item name="android:textSize">@dimen/abc_text_size_subtitle_material_toolbar</item>
        <item name="android:textColor">?android:attr/textColorSecondaryInverse</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Title" parent="TextAppearance.AppCompat.Title">
        <item name="android:textSize">@dimen/abc_text_size_title_material_toolbar</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse" parent="TextAppearance.AppCompat.Title.Inverse">
        <item name="android:textSize">@dimen/abc_text_size_title_material_toolbar</item>
        <item name="android:textColor">?android:attr/textColorPrimaryInverse</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionMode.Subtitle" parent="TextAppearance.AppCompat.Widget.ActionBar.Subtitle"/>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionMode.Title" parent="TextAppearance.AppCompat.Widget.ActionBar.Title"/>
    <style name="Base.TextAppearance.AppCompat.Widget.Button" parent="TextAppearance.AppCompat.Button"/>
    <style name="Base.TextAppearance.AppCompat.Widget.Button.Borderless.Colored" parent="Base.TextAppearance.AppCompat.Widget.Button">
        <item name="android:textColor">@color/abc_btn_colored_borderless_text_material</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.Button.Colored">
        <item name="android:textColor">@color/abc_btn_colored_text_material</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.Button.Inverse" parent="TextAppearance.AppCompat.Button">
        <item name="android:textColor">?android:textColorPrimaryInverse</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.DropDownItem" parent="android:TextAppearance.Small">
        <item name="android:textColor">?android:attr/textColorPrimaryDisableOnly</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.PopupMenu.Header" parent="TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/abc_text_size_menu_header_material</item>
        <item name="android:textColor">?attr/colorAccent</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.PopupMenu.Large" parent="TextAppearance.AppCompat.Menu"/>
    <style name="Base.TextAppearance.AppCompat.Widget.PopupMenu.Small" parent="TextAppearance.AppCompat.Menu"/>
    <style name="Base.TextAppearance.AppCompat.Widget.Switch" parent="TextAppearance.AppCompat.Button"/>
    <style name="Base.TextAppearance.AppCompat.Widget.TextView.SpinnerItem" parent="TextAppearance.AppCompat.Menu"/>
    <style name="Base.TextAppearance.MaterialComponents.Badge" parent="TextAppearance.AppCompat">
    
    <item name="fontFamily">sans-serif-medium</item>
    <item name="android:fontFamily">sans-serif-medium</item>
    <item name="android:textStyle">bold</item>
    <item name="android:textAllCaps">false</item>
    <item name="android:textSize">@dimen/mtrl_badge_text_size</item>
    <item name="android:letterSpacing">0.0892857143</item>
    <item name="android:textColor">?attr/colorOnError</item>
  </style>
    <style name="Base.TextAppearance.MaterialComponents.Button" parent="TextAppearance.AppCompat.Button">
    
    <item name="fontFamily">sans-serif-medium</item>
    <item name="android:fontFamily">sans-serif-medium</item>
    <item name="android:textStyle">bold</item>
    <item name="android:textAllCaps">true</item>
    <item name="android:textSize">14sp</item>
    <item name="android:letterSpacing">0.0892857143</item>
  </style>
    <style name="Base.TextAppearance.MaterialComponents.Headline6" parent="TextAppearance.AppCompat.Title">
    
    <item name="fontFamily">sans-serif-medium</item>
    <item name="android:fontFamily">sans-serif-medium</item>
    <item name="android:textStyle">bold</item>
    <item name="android:textAllCaps">false</item>
    <item name="android:textSize">20sp</item>
    <item name="android:letterSpacing">0.0125</item>
  </style>
    <style name="Base.TextAppearance.MaterialComponents.Subtitle2" parent="TextAppearance.AppCompat.Subhead">
    
    <item name="fontFamily">sans-serif-medium</item>
    <item name="android:fontFamily">sans-serif-medium</item>
    <item name="android:textStyle">bold</item>
    <item name="android:textAllCaps">false</item>
    <item name="android:textSize">14sp</item>
    <item name="android:letterSpacing">0.00714285714</item>
  </style>
    <style name="Base.TextAppearance.Widget.AppCompat.ExpandedMenu.Item" parent="android:TextAppearance.Medium">
        <item name="android:textColor">?android:attr/textColorPrimaryDisableOnly</item>
    </style>
    <style name="Base.TextAppearance.Widget.AppCompat.Toolbar.Subtitle" parent="TextAppearance.AppCompat.Widget.ActionBar.Subtitle">
    </style>
    <style name="Base.TextAppearance.Widget.AppCompat.Toolbar.Title" parent="TextAppearance.AppCompat.Widget.ActionBar.Title">
    </style>
    <style name="Base.Theme.AppCompat" parent="Base.V7.Theme.AppCompat">
    </style>
    <style name="Base.Theme.AppCompat.CompactMenu" parent="">
        <item name="android:itemTextAppearance">?android:attr/textAppearanceMedium</item>
        <item name="android:listViewStyle">@style/Widget.AppCompat.ListView.Menu</item>
        <item name="android:windowAnimationStyle">@style/Animation.AppCompat.DropDownUp</item>
    </style>
    <style name="Base.Theme.AppCompat.Dialog" parent="Base.V7.Theme.AppCompat.Dialog"/>
    <style name="Base.Theme.AppCompat.Dialog.Alert">
        <item name="android:windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="android:windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Base.Theme.AppCompat.Dialog.FixedSize">
        <item name="windowFixedWidthMajor">@dimen/abc_dialog_fixed_width_major</item>
        <item name="windowFixedWidthMinor">@dimen/abc_dialog_fixed_width_minor</item>
        <item name="windowFixedHeightMajor">@dimen/abc_dialog_fixed_height_major</item>
        <item name="windowFixedHeightMinor">@dimen/abc_dialog_fixed_height_minor</item>
    </style>
    <style name="Base.Theme.AppCompat.Dialog.MinWidth">
        <item name="android:windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="android:windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Base.Theme.AppCompat.DialogWhenLarge" parent="Theme.AppCompat"/>
    <style name="Base.Theme.AppCompat.Light" parent="Base.V7.Theme.AppCompat.Light">
    </style>
    <style name="Base.Theme.AppCompat.Light.DarkActionBar" parent="Base.Theme.AppCompat.Light">
        <item name="actionBarPopupTheme">@style/ThemeOverlay.AppCompat.Light</item>
        <item name="actionBarWidgetTheme">@null</item>
        <item name="actionBarTheme">@style/ThemeOverlay.AppCompat.Dark.ActionBar</item>

        
        <item name="listChoiceBackgroundIndicator">@drawable/abc_list_selector_holo_dark</item>

        <item name="colorPrimaryDark">@color/primary_dark_material_dark</item>
        <item name="colorPrimary">@color/primary_material_dark</item>
    </style>
    <style name="Base.Theme.AppCompat.Light.Dialog" parent="Base.V7.Theme.AppCompat.Light.Dialog"/>
    <style name="Base.Theme.AppCompat.Light.Dialog.Alert">
        <item name="android:windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="android:windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Base.Theme.AppCompat.Light.Dialog.FixedSize">
        <item name="windowFixedWidthMajor">@dimen/abc_dialog_fixed_width_major</item>
        <item name="windowFixedWidthMinor">@dimen/abc_dialog_fixed_width_minor</item>
        <item name="windowFixedHeightMajor">@dimen/abc_dialog_fixed_height_major</item>
        <item name="windowFixedHeightMinor">@dimen/abc_dialog_fixed_height_minor</item>
    </style>
    <style name="Base.Theme.AppCompat.Light.Dialog.MinWidth">
        <item name="android:windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="android:windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Base.Theme.AppCompat.Light.DialogWhenLarge" parent="Theme.AppCompat.Light"/>
    <style name="Base.Theme.MaterialComponents" parent="Base.V14.Theme.MaterialComponents"/>
    <style name="Base.Theme.MaterialComponents.Bridge" parent="Base.V14.Theme.MaterialComponents.Bridge"/>
    <style name="Base.Theme.MaterialComponents.CompactMenu" parent="">
    <item name="android:itemTextAppearance">?android:attr/textAppearanceMedium</item>
    <item name="android:listViewStyle">@style/Widget.AppCompat.ListView.Menu</item>
    <item name="android:windowAnimationStyle">@style/Animation.AppCompat.DropDownUp</item>
  </style>
    <style name="Base.Theme.MaterialComponents.Dialog" parent="Base.V14.Theme.MaterialComponents.Dialog"/>
    <style name="Base.Theme.MaterialComponents.Dialog.Alert">
    <item name="android:windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
    <item name="android:windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
  </style>
    <style name="Base.Theme.MaterialComponents.Dialog.Bridge" parent="Base.V14.Theme.MaterialComponents.Dialog.Bridge"/>
    <style name="Base.Theme.MaterialComponents.Dialog.FixedSize">
    <item name="windowFixedWidthMajor">@dimen/abc_dialog_fixed_width_major</item>
    <item name="windowFixedWidthMinor">@dimen/abc_dialog_fixed_width_minor</item>
    <item name="windowFixedHeightMajor">@dimen/abc_dialog_fixed_height_major</item>
    <item name="windowFixedHeightMinor">@dimen/abc_dialog_fixed_height_minor</item>
  </style>
    <style name="Base.Theme.MaterialComponents.Dialog.MinWidth">
    <item name="android:windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
    <item name="android:windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
  </style>
    <style name="Base.Theme.MaterialComponents.DialogWhenLarge" parent="Theme.MaterialComponents"/>
    <style name="Base.Theme.MaterialComponents.Light" parent="Base.V14.Theme.MaterialComponents.Light"/>
    <style name="Base.Theme.MaterialComponents.Light.Bridge" parent="Base.V14.Theme.MaterialComponents.Light.Bridge"/>
    <style name="Base.Theme.MaterialComponents.Light.DarkActionBar" parent="Base.Theme.MaterialComponents.Light">
    <item name="actionBarWidgetTheme">@null</item>
    <item name="actionBarTheme">@style/ThemeOverlay.MaterialComponents.Dark.ActionBar</item>

    
    <item name="listChoiceBackgroundIndicator">@drawable/abc_list_selector_holo_dark</item>

    <item name="colorPrimaryDark">@color/primary_dark_material_dark</item>
    <item name="colorPrimary">@color/primary_material_dark</item>
  </style>
    <style name="Base.Theme.MaterialComponents.Light.DarkActionBar.Bridge" parent="Base.V14.Theme.MaterialComponents.Light.DarkActionBar.Bridge"/>
    <style name="Base.Theme.MaterialComponents.Light.Dialog" parent="Base.V14.Theme.MaterialComponents.Light.Dialog"/>
    <style name="Base.Theme.MaterialComponents.Light.Dialog.Alert">
    <item name="android:windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
    <item name="android:windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
  </style>
    <style name="Base.Theme.MaterialComponents.Light.Dialog.Bridge" parent="Base.V14.Theme.MaterialComponents.Light.Dialog.Bridge"/>
    <style name="Base.Theme.MaterialComponents.Light.Dialog.FixedSize">
    <item name="windowFixedWidthMajor">@dimen/abc_dialog_fixed_width_major</item>
    <item name="windowFixedWidthMinor">@dimen/abc_dialog_fixed_width_minor</item>
    <item name="windowFixedHeightMajor">@dimen/abc_dialog_fixed_height_major</item>
    <item name="windowFixedHeightMinor">@dimen/abc_dialog_fixed_height_minor</item>
  </style>
    <style name="Base.Theme.MaterialComponents.Light.Dialog.MinWidth">
    <item name="android:windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
    <item name="android:windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
  </style>
    <style name="Base.Theme.MaterialComponents.Light.DialogWhenLarge" parent="Theme.MaterialComponents.Light"/>
    <style name="Base.ThemeOverlay.AppCompat" parent="Platform.ThemeOverlay.AppCompat"/>
    <style name="Base.ThemeOverlay.AppCompat.ActionBar">
        <item name="colorControlNormal">?android:attr/textColorPrimary</item>
        <item name="searchViewStyle">@style/Widget.AppCompat.SearchView.ActionBar</item>
    </style>
    <style name="Base.ThemeOverlay.AppCompat.Dark" parent="Platform.ThemeOverlay.AppCompat.Dark">
        <item name="android:windowBackground">@color/background_material_dark</item>
        <item name="android:colorForeground">@color/foreground_material_dark</item>
        <item name="android:colorForegroundInverse">@color/foreground_material_light</item>
        <item name="android:colorBackground">@color/background_material_dark</item>
        <item name="android:colorBackgroundCacheHint">@color/abc_background_cache_hint_selector_material_dark</item>
        <item name="colorBackgroundFloating">@color/background_floating_material_dark</item>

        <item name="android:textColorPrimary">@color/abc_primary_text_material_dark</item>
        <item name="android:textColorPrimaryInverse">@color/abc_primary_text_material_light</item>
        <item name="android:textColorPrimaryDisableOnly">@color/abc_primary_text_disable_only_material_dark</item>
        <item name="android:textColorSecondary">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorSecondaryInverse">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorTertiary">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorTertiaryInverse">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorHint">@color/abc_hint_foreground_material_dark</item>
        <item name="android:textColorHintInverse">@color/abc_hint_foreground_material_light</item>
        <item name="android:textColorHighlight">@color/highlighted_text_material_dark</item>

        <item name="colorControlNormal">?android:attr/textColorSecondary</item>
        <item name="colorControlHighlight">@color/ripple_material_dark</item>
        <item name="colorButtonNormal">@color/button_material_dark</item>
        <item name="colorSwitchThumbNormal">@color/switch_thumb_material_dark</item>

        
        <item name="isLightTheme">false</item>
    </style>
    <style name="Base.ThemeOverlay.AppCompat.Dark.ActionBar">
        <item name="colorControlNormal">?android:attr/textColorPrimary</item>
        <item name="searchViewStyle">@style/Widget.AppCompat.SearchView.ActionBar</item>
    </style>
    <style name="Base.ThemeOverlay.AppCompat.Dialog" parent="Base.V7.ThemeOverlay.AppCompat.Dialog"/>
    <style name="Base.ThemeOverlay.AppCompat.Dialog.Alert">
        <item name="android:windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="android:windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Base.ThemeOverlay.AppCompat.Light" parent="Platform.ThemeOverlay.AppCompat.Light">
        <item name="android:windowBackground">@color/background_material_light</item>
        <item name="android:colorForeground">@color/foreground_material_light</item>
        <item name="android:colorForegroundInverse">@color/foreground_material_dark</item>
        <item name="android:colorBackground">@color/background_material_light</item>
        <item name="android:colorBackgroundCacheHint">@color/abc_background_cache_hint_selector_material_light</item>
        <item name="colorBackgroundFloating">@color/background_floating_material_light</item>

        <item name="android:textColorPrimary">@color/abc_primary_text_material_light</item>
        <item name="android:textColorPrimaryInverse">@color/abc_primary_text_material_dark</item>
        <item name="android:textColorSecondary">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorSecondaryInverse">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorTertiary">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorTertiaryInverse">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorPrimaryDisableOnly">@color/abc_primary_text_disable_only_material_light</item>
        <item name="android:textColorPrimaryInverseDisableOnly">@color/abc_primary_text_disable_only_material_dark</item>
        <item name="android:textColorHint">@color/abc_hint_foreground_material_light</item>
        <item name="android:textColorHintInverse">@color/abc_hint_foreground_material_dark</item>
        <item name="android:textColorHighlight">@color/highlighted_text_material_light</item>

        <item name="colorControlNormal">?android:attr/textColorSecondary</item>
        <item name="colorControlHighlight">@color/ripple_material_light</item>
        <item name="colorButtonNormal">@color/button_material_light</item>
        <item name="colorSwitchThumbNormal">@color/switch_thumb_material_light</item>

        
        <item name="isLightTheme">true</item>
    </style>
    <style name="Base.ThemeOverlay.MaterialComponents.Dialog" parent="Base.V14.ThemeOverlay.MaterialComponents.Dialog"/>
    <style name="Base.ThemeOverlay.MaterialComponents.Dialog.Alert" parent="Base.V14.ThemeOverlay.MaterialComponents.Dialog.Alert"/>
    <style name="Base.ThemeOverlay.MaterialComponents.MaterialAlertDialog" parent="Base.V14.ThemeOverlay.MaterialComponents.MaterialAlertDialog"/>
    <style name="Base.V14.Theme.MaterialComponents" parent="Base.V14.Theme.MaterialComponents.Bridge">
    <item name="viewInflaterClass">com.google.android.material.theme.MaterialComponentsViewInflater</item>

    
    <item name="colorPrimary">@color/design_dark_default_color_primary</item>
    <item name="colorPrimaryDark">@color/design_dark_default_color_primary_dark</item>
    <item name="colorAccent">?attr/colorSecondary</item>

    <item name="android:colorBackground">@color/design_dark_default_color_background</item>
    <item name="colorError">@color/design_dark_default_color_error</item>

    
    <item name="actionBarSize">@dimen/mtrl_toolbar_default_height</item>
    <item name="actionBarPopupTheme">@style/ThemeOverlay.MaterialComponents.Dark</item>
    <item name="actionBarStyle">@style/Widget.MaterialComponents.ActionBar.Surface</item>
    <item name="actionBarTheme">@style/ThemeOverlay.MaterialComponents.ActionBar.Surface</item>
    <item name="actionOverflowMenuStyle">@style/Widget.MaterialComponents.PopupMenu.Overflow</item>

    
    <item name="appBarLayoutStyle">@style/Widget.MaterialComponents.AppBarLayout.Surface</item>
    <item name="borderlessButtonStyle">@style/Widget.MaterialComponents.Button.TextButton</item>
    <item name="bottomNavigationStyle">@style/Widget.MaterialComponents.BottomNavigationView</item>
    <item name="buttonBarButtonStyle">@style/Widget.MaterialComponents.Button.TextButton</item>
    <item name="android:buttonBarButtonStyle">?attr/buttonBarButtonStyle</item>
    <item name="checkboxStyle">@style/Widget.MaterialComponents.CompoundButton.CheckBox</item>
    <item name="android:contextPopupMenuStyle" ns2:targetApi="n">@style/Widget.MaterialComponents.PopupMenu.ContextMenu</item>
    <item name="floatingActionButtonStyle">@style/Widget.MaterialComponents.FloatingActionButton</item>
    <item name="listPopupWindowStyle">@style/Widget.MaterialComponents.PopupMenu.ListPopupWindow</item>
    <item name="navigationViewStyle">@style/Widget.MaterialComponents.NavigationView</item>
    <item name="popupMenuStyle">@style/Widget.MaterialComponents.PopupMenu</item>
    <item name="radioButtonStyle">@style/Widget.MaterialComponents.CompoundButton.RadioButton</item>
    <item name="snackbarStyle">@style/Widget.MaterialComponents.Snackbar</item>
    <item name="snackbarButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Snackbar</item>
    <item name="snackbarTextViewStyle">@style/Widget.MaterialComponents.Snackbar.TextView</item>
    <item name="switchStyle">@style/Widget.MaterialComponents.CompoundButton.Switch</item>
    <item name="tabStyle">@style/Widget.MaterialComponents.TabLayout</item>
    <item name="textInputStyle">@style/Widget.MaterialComponents.TextInputLayout.FilledBox</item>
    <item name="toolbarStyle">@style/Widget.MaterialComponents.Toolbar</item>

    
    <item name="bottomSheetDialogTheme">@style/ThemeOverlay.MaterialComponents.BottomSheetDialog</item>
    <item name="alertDialogTheme">@style/ThemeOverlay.MaterialComponents.Dialog.Alert</item>
    <item name="materialAlertDialogTheme">@style/ThemeOverlay.MaterialComponents.MaterialAlertDialog</item>
    <item name="android:datePickerDialogTheme" ns2:ignore="NewApi">@style/ThemeOverlay.MaterialComponents.Dialog</item>
    <item name="android:timePickerDialogTheme" ns2:ignore="NewApi">@style/ThemeOverlay.MaterialComponents.Dialog</item>

    
    <item name="textAppearanceLargePopupMenu">?attr/textAppearanceSubtitle1</item>
    <item name="textAppearanceSmallPopupMenu">?attr/textAppearanceSubtitle1</item>

    
    <item name="materialCalendarStyle">@style/Widget.MaterialComponents.MaterialCalendar</item>
    <item name="materialCalendarFullscreenTheme">@style/ThemeOverlay.MaterialComponents.MaterialCalendar.Fullscreen</item>
    <item name="materialCalendarTheme">@style/ThemeOverlay.MaterialComponents.MaterialCalendar</item>
  </style>
    <style name="Base.V14.Theme.MaterialComponents.Bridge" parent="Platform.MaterialComponents">
    <item name="isMaterialTheme">true</item>

    <item name="colorPrimaryVariant">@color/design_dark_default_color_primary_variant</item>
    <item name="colorSecondary">@color/design_dark_default_color_secondary</item>
    <item name="colorSecondaryVariant">@color/design_dark_default_color_secondary_variant</item>
    <item name="colorSurface">@color/design_dark_default_color_surface</item>
    <item name="colorPrimarySurface">?attr/colorSurface</item>
    <item name="colorOnPrimary">@color/design_dark_default_color_on_primary</item>
    <item name="colorOnSecondary">@color/design_dark_default_color_on_secondary</item>
    <item name="colorOnBackground">@color/design_dark_default_color_on_background</item>
    <item name="colorOnError">@color/design_dark_default_color_on_error</item>
    <item name="colorOnSurface">@color/design_dark_default_color_on_surface</item>
    <item name="colorOnPrimarySurface">?attr/colorOnSurface</item>

    <item name="scrimBackground">@color/mtrl_scrim_color</item>
    <item name="popupMenuBackground">@drawable/mtrl_popupmenu_background_dark</item>

    <item name="minTouchTargetSize">@dimen/mtrl_min_touch_target_size</item>

    
    <item name="badgeStyle">@style/Widget.MaterialComponents.Badge</item>
    <item name="bottomAppBarStyle">@style/Widget.MaterialComponents.BottomAppBar</item>
    <item name="chipStyle">@style/Widget.MaterialComponents.Chip.Action</item>
    <item name="chipGroupStyle">@style/Widget.MaterialComponents.ChipGroup</item>
    <item name="chipStandaloneStyle">@style/Widget.MaterialComponents.Chip.Entry</item>
    <item name="extendedFloatingActionButtonStyle">@style/Widget.MaterialComponents.ExtendedFloatingActionButton.Icon</item>
    <item name="materialButtonStyle">@style/Widget.MaterialComponents.Button</item>
    <item name="materialButtonOutlinedStyle">@style/Widget.MaterialComponents.Button.OutlinedButton</item>
    <item name="materialButtonToggleGroupStyle">@style/Widget.MaterialComponents.MaterialButtonToggleGroup</item>
    <item name="materialCardViewStyle">@style/Widget.MaterialComponents.CardView</item>
    <item name="sliderStyle">@style/Widget.MaterialComponents.Slider</item>

    
    <item name="textAppearanceHeadline1">@style/TextAppearance.MaterialComponents.Headline1</item>
    <item name="textAppearanceHeadline2">@style/TextAppearance.MaterialComponents.Headline2</item>
    <item name="textAppearanceHeadline3">@style/TextAppearance.MaterialComponents.Headline3</item>
    <item name="textAppearanceHeadline4">@style/TextAppearance.MaterialComponents.Headline4</item>
    <item name="textAppearanceHeadline5">@style/TextAppearance.MaterialComponents.Headline5</item>
    <item name="textAppearanceHeadline6">@style/TextAppearance.MaterialComponents.Headline6</item>
    <item name="textAppearanceSubtitle1">@style/TextAppearance.MaterialComponents.Subtitle1</item>
    <item name="textAppearanceSubtitle2">@style/TextAppearance.MaterialComponents.Subtitle2</item>
    <item name="textAppearanceBody1">@style/TextAppearance.MaterialComponents.Body1</item>
    <item name="textAppearanceBody2">@style/TextAppearance.MaterialComponents.Body2</item>
    <item name="textAppearanceCaption">@style/TextAppearance.MaterialComponents.Caption</item>
    <item name="textAppearanceButton">@style/TextAppearance.MaterialComponents.Button</item>
    <item name="textAppearanceOverline">@style/TextAppearance.MaterialComponents.Overline</item>

    
    <item name="shapeAppearanceSmallComponent">
      @style/ShapeAppearance.MaterialComponents.SmallComponent
    </item>
    <item name="shapeAppearanceMediumComponent">
      @style/ShapeAppearance.MaterialComponents.MediumComponent
    </item>
    <item name="shapeAppearanceLargeComponent">
      @style/ShapeAppearance.MaterialComponents.LargeComponent
    </item>

    
    <item name="elevationOverlayEnabled">true</item>
    <item name="elevationOverlayColor">?attr/colorOnSurface</item>
  </style>
    <style name="Base.V14.Theme.MaterialComponents.Dialog" parent="Base.V14.Theme.MaterialComponents.Dialog.Bridge">
    <item name="viewInflaterClass">com.google.android.material.theme.MaterialComponentsViewInflater</item>

    <item name="colorPrimary">@color/design_dark_default_color_primary</item>
    <item name="colorPrimaryDark">@color/design_dark_default_color_primary_dark</item>
    <item name="colorSecondary">@color/design_dark_default_color_secondary</item>
    <item name="colorAccent">?attr/colorSecondary</item>

    <item name="android:colorBackground">@color/design_dark_default_color_background</item>
    <item name="colorError">@color/design_dark_default_color_error</item>

    
    <item name="actionBarSize">@dimen/mtrl_toolbar_default_height</item>
    <item name="actionBarPopupTheme">@style/ThemeOverlay.MaterialComponents.Dark</item>
    <item name="actionBarStyle">@style/Widget.MaterialComponents.ActionBar.Surface</item>
    <item name="actionBarTheme">@style/ThemeOverlay.MaterialComponents.ActionBar.Surface</item>
    <item name="actionOverflowMenuStyle">@style/Widget.MaterialComponents.PopupMenu.Overflow</item>

    
    <item name="appBarLayoutStyle">@style/Widget.MaterialComponents.AppBarLayout.Surface</item>
    <item name="borderlessButtonStyle">@style/Widget.MaterialComponents.Button.TextButton</item>
    <item name="bottomNavigationStyle">@style/Widget.MaterialComponents.BottomNavigationView</item>
    <item name="buttonBarButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog.Flush</item>
    <item name="checkboxStyle">@style/Widget.MaterialComponents.CompoundButton.CheckBox</item>
    <item name="android:contextPopupMenuStyle" ns2:targetApi="n">@style/Widget.MaterialComponents.PopupMenu.ContextMenu</item>
    <item name="floatingActionButtonStyle">@style/Widget.MaterialComponents.FloatingActionButton</item>
    <item name="listPopupWindowStyle">@style/Widget.MaterialComponents.PopupMenu.ListPopupWindow</item>
    <item name="navigationViewStyle">@style/Widget.MaterialComponents.NavigationView</item>
    <item name="popupMenuStyle">@style/Widget.MaterialComponents.PopupMenu</item>
    <item name="radioButtonStyle">@style/Widget.MaterialComponents.CompoundButton.RadioButton</item>
    <item name="snackbarStyle">@style/Widget.MaterialComponents.Snackbar</item>
    <item name="snackbarButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Snackbar</item>
    <item name="snackbarTextViewStyle">@style/Widget.MaterialComponents.Snackbar.TextView</item>
    <item name="switchStyle">@style/Widget.MaterialComponents.CompoundButton.Switch</item>
    <item name="tabStyle">@style/Widget.MaterialComponents.TabLayout</item>
    <item name="textInputStyle">@style/Widget.MaterialComponents.TextInputLayout.FilledBox</item>
    <item name="toolbarStyle">@style/Widget.MaterialComponents.Toolbar</item>

    <item name="alertDialogTheme">@style/ThemeOverlay.MaterialComponents.Dialog.Alert</item>
    <item name="materialAlertDialogTheme">@style/ThemeOverlay.MaterialComponents.MaterialAlertDialog</item>

    
    <item name="textAppearanceLargePopupMenu">?attr/textAppearanceSubtitle1</item>
    <item name="textAppearanceSmallPopupMenu">?attr/textAppearanceSubtitle1</item>
  </style>
    <style name="Base.V14.Theme.MaterialComponents.Dialog.Bridge" parent="Platform.MaterialComponents.Dialog">
    <item name="isMaterialTheme">true</item>

    <item name="colorPrimaryVariant">@color/design_dark_default_color_primary_variant</item>
    <item name="colorSecondary">@color/design_dark_default_color_secondary</item>
    <item name="colorSecondaryVariant">@color/design_dark_default_color_secondary_variant</item>
    <item name="colorSurface">@color/design_dark_default_color_surface</item>
    <item name="colorPrimarySurface">?attr/colorSurface</item>
    <item name="colorOnPrimary">@color/design_dark_default_color_on_primary</item>
    <item name="colorOnSecondary">@color/design_dark_default_color_on_secondary</item>
    <item name="colorOnBackground">@color/design_dark_default_color_on_background</item>
    <item name="colorOnError">@color/design_dark_default_color_on_error</item>
    <item name="colorOnSurface">@color/design_dark_default_color_on_surface</item>
    <item name="colorOnPrimarySurface">?attr/colorOnSurface</item>

    <item name="scrimBackground">@color/mtrl_scrim_color</item>
    <item name="popupMenuBackground">@drawable/mtrl_popupmenu_background_dark</item>

    <item name="minTouchTargetSize">@dimen/mtrl_min_touch_target_size</item>

    
    <item name="badgeStyle">@style/Widget.MaterialComponents.Badge</item>
    <item name="bottomAppBarStyle">@style/Widget.MaterialComponents.BottomAppBar</item>
    <item name="chipStyle">@style/Widget.MaterialComponents.Chip.Action</item>
    <item name="chipGroupStyle">@style/Widget.MaterialComponents.ChipGroup</item>
    <item name="chipStandaloneStyle">@style/Widget.MaterialComponents.Chip.Entry</item>
    <item name="extendedFloatingActionButtonStyle">@style/Widget.MaterialComponents.ExtendedFloatingActionButton.Icon</item>
    <item name="materialButtonStyle">@style/Widget.MaterialComponents.Button</item>
    <item name="materialButtonOutlinedStyle">@style/Widget.MaterialComponents.Button.OutlinedButton</item>
    <item name="materialButtonToggleGroupStyle">@style/Widget.MaterialComponents.MaterialButtonToggleGroup</item>
    <item name="materialCardViewStyle">@style/Widget.MaterialComponents.CardView</item>
    <item name="sliderStyle">@style/Widget.MaterialComponents.Slider</item>

    
    <item name="textAppearanceHeadline1">@style/TextAppearance.MaterialComponents.Headline1</item>
    <item name="textAppearanceHeadline2">@style/TextAppearance.MaterialComponents.Headline2</item>
    <item name="textAppearanceHeadline3">@style/TextAppearance.MaterialComponents.Headline3</item>
    <item name="textAppearanceHeadline4">@style/TextAppearance.MaterialComponents.Headline4</item>
    <item name="textAppearanceHeadline5">@style/TextAppearance.MaterialComponents.Headline5</item>
    <item name="textAppearanceHeadline6">@style/TextAppearance.MaterialComponents.Headline6</item>
    <item name="textAppearanceSubtitle1">@style/TextAppearance.MaterialComponents.Subtitle1</item>
    <item name="textAppearanceSubtitle2">@style/TextAppearance.MaterialComponents.Subtitle2</item>
    <item name="textAppearanceBody1">@style/TextAppearance.MaterialComponents.Body1</item>
    <item name="textAppearanceBody2">@style/TextAppearance.MaterialComponents.Body2</item>
    <item name="textAppearanceCaption">@style/TextAppearance.MaterialComponents.Caption</item>
    <item name="textAppearanceButton">@style/TextAppearance.MaterialComponents.Button</item>
    <item name="textAppearanceOverline">@style/TextAppearance.MaterialComponents.Overline</item>

    
    <item name="shapeAppearanceSmallComponent">
      @style/ShapeAppearance.MaterialComponents.SmallComponent
    </item>
    <item name="shapeAppearanceMediumComponent">
      @style/ShapeAppearance.MaterialComponents.MediumComponent
    </item>
    <item name="shapeAppearanceLargeComponent">
      @style/ShapeAppearance.MaterialComponents.LargeComponent
    </item>

    
    <item name="elevationOverlayEnabled">true</item>
    <item name="elevationOverlayColor">?attr/colorOnSurface</item>
  </style>
    <style name="Base.V14.Theme.MaterialComponents.Light" parent="Base.V14.Theme.MaterialComponents.Light.Bridge">
    <item name="viewInflaterClass">com.google.android.material.theme.MaterialComponentsViewInflater</item>

    
    <item name="colorPrimary">@color/design_default_color_primary</item>
    <item name="colorPrimaryDark">@color/design_default_color_primary_dark</item>
    <item name="colorAccent">?attr/colorSecondary</item>

    <item name="android:colorBackground">@color/design_default_color_background</item>
    <item name="colorError">@color/design_default_color_error</item>

    
    <item name="actionBarSize">@dimen/mtrl_toolbar_default_height</item>
    <item name="actionBarPopupTheme">@style/ThemeOverlay.MaterialComponents.Light</item>
    <item name="actionBarStyle">@style/Widget.MaterialComponents.Light.ActionBar.Solid</item>
    <item name="actionOverflowMenuStyle">@style/Widget.MaterialComponents.PopupMenu.Overflow</item>

    
    <item name="appBarLayoutStyle">@style/Widget.MaterialComponents.AppBarLayout.Primary</item>
    <item name="borderlessButtonStyle">@style/Widget.MaterialComponents.Button.TextButton</item>
    <item name="bottomNavigationStyle">@style/Widget.MaterialComponents.BottomNavigationView</item>
    <item name="buttonBarButtonStyle">@style/Widget.MaterialComponents.Button.TextButton</item>
    <item name="android:buttonBarButtonStyle">?attr/buttonBarButtonStyle</item>
    <item name="checkboxStyle">@style/Widget.MaterialComponents.CompoundButton.CheckBox</item>
    <item name="android:contextPopupMenuStyle" ns2:targetApi="n">@style/Widget.MaterialComponents.PopupMenu.ContextMenu</item>
    <item name="floatingActionButtonStyle">@style/Widget.MaterialComponents.FloatingActionButton</item>
    <item name="listPopupWindowStyle">@style/Widget.MaterialComponents.PopupMenu.ListPopupWindow</item>
    <item name="navigationViewStyle">@style/Widget.MaterialComponents.NavigationView</item>
    <item name="popupMenuStyle">@style/Widget.MaterialComponents.PopupMenu</item>
    <item name="radioButtonStyle">@style/Widget.MaterialComponents.CompoundButton.RadioButton</item>
    <item name="snackbarStyle">@style/Widget.MaterialComponents.Snackbar</item>
    <item name="snackbarButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Snackbar</item>
    <item name="snackbarTextViewStyle">@style/Widget.MaterialComponents.Snackbar.TextView</item>
    <item name="switchStyle">@style/Widget.MaterialComponents.CompoundButton.Switch</item>
    <item name="tabStyle">@style/Widget.MaterialComponents.TabLayout</item>
    <item name="textInputStyle">@style/Widget.MaterialComponents.TextInputLayout.FilledBox</item>
    <item name="toolbarStyle">@style/Widget.MaterialComponents.Toolbar</item>

    
    <item name="bottomSheetDialogTheme">@style/ThemeOverlay.MaterialComponents.Light.BottomSheetDialog</item>
    <item name="alertDialogTheme">@style/ThemeOverlay.MaterialComponents.Dialog.Alert</item>
    <item name="materialAlertDialogTheme">@style/ThemeOverlay.MaterialComponents.MaterialAlertDialog</item>
    <item name="android:datePickerDialogTheme" ns2:ignore="NewApi">@style/ThemeOverlay.MaterialComponents.Dialog</item>
    <item name="android:timePickerDialogTheme" ns2:ignore="NewApi">@style/ThemeOverlay.MaterialComponents.Dialog</item>

    
    <item name="textAppearanceLargePopupMenu">?attr/textAppearanceSubtitle1</item>
    <item name="textAppearanceSmallPopupMenu">?attr/textAppearanceSubtitle1</item>

    
    <item name="materialCalendarStyle">@style/Widget.MaterialComponents.MaterialCalendar</item>
    <item name="materialCalendarFullscreenTheme">@style/ThemeOverlay.MaterialComponents.MaterialCalendar.Fullscreen</item>
    <item name="materialCalendarTheme">@style/ThemeOverlay.MaterialComponents.MaterialCalendar</item>
  </style>
    <style name="Base.V14.Theme.MaterialComponents.Light.Bridge" parent="Platform.MaterialComponents.Light">
    <item name="isMaterialTheme">true</item>

    <item name="colorPrimaryVariant">@color/design_default_color_primary_variant</item>
    <item name="colorSecondary">@color/design_default_color_secondary</item>
    <item name="colorSecondaryVariant">@color/design_default_color_secondary_variant</item>
    <item name="colorSurface">@color/design_default_color_surface</item>
    <item name="colorPrimarySurface">?attr/colorPrimary</item>
    <item name="colorOnPrimary">@color/design_default_color_on_primary</item>
    <item name="colorOnSecondary">@color/design_default_color_on_secondary</item>
    <item name="colorOnBackground">@color/design_default_color_on_background</item>
    <item name="colorOnError">@color/design_default_color_on_error</item>
    <item name="colorOnSurface">@color/design_default_color_on_surface</item>
    <item name="colorOnPrimarySurface">?attr/colorOnPrimary</item>

    <item name="scrimBackground">@color/mtrl_scrim_color</item>
    <item name="popupMenuBackground">@drawable/mtrl_popupmenu_background</item>

    <item name="minTouchTargetSize">@dimen/mtrl_min_touch_target_size</item>

    
    <item name="badgeStyle">@style/Widget.MaterialComponents.Badge</item>
    <item name="bottomAppBarStyle">@style/Widget.MaterialComponents.BottomAppBar</item>
    <item name="chipStyle">@style/Widget.MaterialComponents.Chip.Action</item>
    <item name="chipGroupStyle">@style/Widget.MaterialComponents.ChipGroup</item>
    <item name="chipStandaloneStyle">@style/Widget.MaterialComponents.Chip.Entry</item>
    <item name="extendedFloatingActionButtonStyle">@style/Widget.MaterialComponents.ExtendedFloatingActionButton.Icon</item>
    <item name="materialButtonStyle">@style/Widget.MaterialComponents.Button</item>
    <item name="materialButtonOutlinedStyle">@style/Widget.MaterialComponents.Button.OutlinedButton</item>
    <item name="materialButtonToggleGroupStyle">@style/Widget.MaterialComponents.MaterialButtonToggleGroup</item>
    <item name="materialCardViewStyle">@style/Widget.MaterialComponents.CardView</item>
    <item name="sliderStyle">@style/Widget.MaterialComponents.Slider</item>

    
    <item name="textAppearanceHeadline1">@style/TextAppearance.MaterialComponents.Headline1</item>
    <item name="textAppearanceHeadline2">@style/TextAppearance.MaterialComponents.Headline2</item>
    <item name="textAppearanceHeadline3">@style/TextAppearance.MaterialComponents.Headline3</item>
    <item name="textAppearanceHeadline4">@style/TextAppearance.MaterialComponents.Headline4</item>
    <item name="textAppearanceHeadline5">@style/TextAppearance.MaterialComponents.Headline5</item>
    <item name="textAppearanceHeadline6">@style/TextAppearance.MaterialComponents.Headline6</item>
    <item name="textAppearanceSubtitle1">@style/TextAppearance.MaterialComponents.Subtitle1</item>
    <item name="textAppearanceSubtitle2">@style/TextAppearance.MaterialComponents.Subtitle2</item>
    <item name="textAppearanceBody1">@style/TextAppearance.MaterialComponents.Body1</item>
    <item name="textAppearanceBody2">@style/TextAppearance.MaterialComponents.Body2</item>
    <item name="textAppearanceCaption">@style/TextAppearance.MaterialComponents.Caption</item>
    <item name="textAppearanceButton">@style/TextAppearance.MaterialComponents.Button</item>
    <item name="textAppearanceOverline">@style/TextAppearance.MaterialComponents.Overline</item>

    
    <item name="shapeAppearanceSmallComponent">
      @style/ShapeAppearance.MaterialComponents.SmallComponent
    </item>
    <item name="shapeAppearanceMediumComponent">
      @style/ShapeAppearance.MaterialComponents.MediumComponent
    </item>
    <item name="shapeAppearanceLargeComponent">
      @style/ShapeAppearance.MaterialComponents.LargeComponent
    </item>

    
    <item name="elevationOverlayEnabled">false</item>
    <item name="elevationOverlayColor">?attr/colorOnSurface</item>
  </style>
    <style name="Base.V14.Theme.MaterialComponents.Light.DarkActionBar.Bridge" parent="Theme.AppCompat.Light.DarkActionBar">
    <item name="isMaterialTheme">true</item>

    <item name="colorPrimaryVariant">@color/design_default_color_primary_variant</item>
    <item name="colorSecondary">@color/design_default_color_secondary</item>
    <item name="colorSecondaryVariant">@color/design_default_color_secondary_variant</item>
    <item name="colorSurface">@color/design_default_color_surface</item>
    <item name="colorPrimarySurface">?attr/colorPrimary</item>
    <item name="colorOnPrimary">@color/design_default_color_on_primary</item>
    <item name="colorOnSecondary">@color/design_default_color_on_secondary</item>
    <item name="colorOnBackground">@color/design_default_color_on_background</item>
    <item name="colorOnError">@color/design_default_color_on_error</item>
    <item name="colorOnSurface">@color/design_default_color_on_surface</item>
    <item name="colorOnPrimarySurface">?attr/colorOnPrimary</item>

    <item name="scrimBackground">@color/mtrl_scrim_color</item>
    <item name="popupMenuBackground">@drawable/mtrl_popupmenu_background</item>

    
    <item name="textAppearanceHeadline1">@style/TextAppearance.MaterialComponents.Headline1</item>
    <item name="textAppearanceHeadline2">@style/TextAppearance.MaterialComponents.Headline2</item>
    <item name="textAppearanceHeadline3">@style/TextAppearance.MaterialComponents.Headline3</item>
    <item name="textAppearanceHeadline4">@style/TextAppearance.MaterialComponents.Headline4</item>
    <item name="textAppearanceHeadline5">@style/TextAppearance.MaterialComponents.Headline5</item>
    <item name="textAppearanceHeadline6">@style/TextAppearance.MaterialComponents.Headline6</item>
    <item name="textAppearanceSubtitle1">@style/TextAppearance.MaterialComponents.Subtitle1</item>
    <item name="textAppearanceSubtitle2">@style/TextAppearance.MaterialComponents.Subtitle2</item>
    <item name="textAppearanceBody1">@style/TextAppearance.MaterialComponents.Body1</item>
    <item name="textAppearanceBody2">@style/TextAppearance.MaterialComponents.Body2</item>
    <item name="textAppearanceCaption">@style/TextAppearance.MaterialComponents.Caption</item>
    <item name="textAppearanceButton">@style/TextAppearance.MaterialComponents.Button</item>
    <item name="textAppearanceOverline">@style/TextAppearance.MaterialComponents.Overline</item>

    
    <item name="shapeAppearanceSmallComponent">
      @style/ShapeAppearance.MaterialComponents.SmallComponent
    </item>
    <item name="shapeAppearanceMediumComponent">
      @style/ShapeAppearance.MaterialComponents.MediumComponent
    </item>
    <item name="shapeAppearanceLargeComponent">
      @style/ShapeAppearance.MaterialComponents.LargeComponent
    </item>

    
    <item name="elevationOverlayEnabled">false</item>
    <item name="elevationOverlayColor">?attr/colorOnSurface</item>
  </style>
    <style name="Base.V14.Theme.MaterialComponents.Light.Dialog" parent="Base.V14.Theme.MaterialComponents.Light.Dialog.Bridge">
    <item name="viewInflaterClass">com.google.android.material.theme.MaterialComponentsViewInflater</item>

    
    <item name="colorPrimary">@color/design_default_color_primary</item>
    <item name="colorPrimaryDark">@color/design_default_color_primary_dark</item>
    <item name="colorSecondary">@color/design_default_color_secondary</item>
    <item name="colorAccent">?attr/colorSecondary</item>

    <item name="android:colorBackground">@color/design_default_color_background</item>
    <item name="colorError">@color/design_default_color_error</item>

    
    <item name="actionBarSize">@dimen/mtrl_toolbar_default_height</item>
    <item name="actionBarPopupTheme">@style/ThemeOverlay.MaterialComponents.Light</item>
    <item name="actionBarStyle">@style/Widget.MaterialComponents.Light.ActionBar.Solid</item>
    <item name="actionOverflowMenuStyle">@style/Widget.MaterialComponents.PopupMenu.Overflow</item>

    
    <item name="appBarLayoutStyle">@style/Widget.MaterialComponents.AppBarLayout.Primary</item>
    <item name="borderlessButtonStyle">@style/Widget.MaterialComponents.Button.TextButton</item>
    <item name="bottomNavigationStyle">@style/Widget.MaterialComponents.BottomNavigationView</item>
    <item name="buttonBarButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog.Flush</item>
    <item name="checkboxStyle">@style/Widget.MaterialComponents.CompoundButton.CheckBox</item>
    <item name="android:contextPopupMenuStyle" ns2:targetApi="n">@style/Widget.MaterialComponents.PopupMenu.ContextMenu</item>
    <item name="floatingActionButtonStyle">@style/Widget.MaterialComponents.FloatingActionButton</item>
    <item name="listPopupWindowStyle">@style/Widget.MaterialComponents.PopupMenu.ListPopupWindow</item>
    <item name="navigationViewStyle">@style/Widget.MaterialComponents.NavigationView</item>
    <item name="popupMenuStyle">@style/Widget.MaterialComponents.PopupMenu</item>
    <item name="radioButtonStyle">@style/Widget.MaterialComponents.CompoundButton.RadioButton</item>
    <item name="snackbarStyle">@style/Widget.MaterialComponents.Snackbar</item>
    <item name="snackbarButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Snackbar</item>
    <item name="snackbarTextViewStyle">@style/Widget.MaterialComponents.Snackbar.TextView</item>
    <item name="switchStyle">@style/Widget.MaterialComponents.CompoundButton.Switch</item>
    <item name="tabStyle">@style/Widget.MaterialComponents.TabLayout</item>
    <item name="textInputStyle">@style/Widget.MaterialComponents.TextInputLayout.FilledBox</item>
    <item name="toolbarStyle">@style/Widget.MaterialComponents.Toolbar</item>

    <item name="alertDialogTheme">@style/ThemeOverlay.MaterialComponents.Dialog.Alert</item>
    <item name="materialAlertDialogTheme">@style/ThemeOverlay.MaterialComponents.MaterialAlertDialog</item>

    
    <item name="textAppearanceLargePopupMenu">?attr/textAppearanceSubtitle1</item>
    <item name="textAppearanceSmallPopupMenu">?attr/textAppearanceSubtitle1</item>
  </style>
    <style name="Base.V14.Theme.MaterialComponents.Light.Dialog.Bridge" parent="Platform.MaterialComponents.Light.Dialog">
    <item name="isMaterialTheme">true</item>

    <item name="colorPrimaryVariant">@color/design_default_color_primary_variant</item>
    <item name="colorSecondary">@color/design_default_color_secondary</item>
    <item name="colorSecondaryVariant">@color/design_default_color_secondary_variant</item>
    <item name="colorSurface">@color/design_default_color_surface</item>
    <item name="colorPrimarySurface">?attr/colorPrimary</item>
    <item name="colorOnPrimary">@color/design_default_color_on_primary</item>
    <item name="colorOnSecondary">@color/design_default_color_on_secondary</item>
    <item name="colorOnBackground">@color/design_default_color_on_background</item>
    <item name="colorOnError">@color/design_default_color_on_error</item>
    <item name="colorOnSurface">@color/design_default_color_on_surface</item>
    <item name="colorOnPrimarySurface">?attr/colorOnPrimary</item>

    <item name="scrimBackground">@color/mtrl_scrim_color</item>
    <item name="popupMenuBackground">@drawable/mtrl_popupmenu_background</item>

    <item name="minTouchTargetSize">@dimen/mtrl_min_touch_target_size</item>

    
    <item name="badgeStyle">@style/Widget.MaterialComponents.Badge</item>
    <item name="bottomAppBarStyle">@style/Widget.MaterialComponents.BottomAppBar</item>
    <item name="chipStyle">@style/Widget.MaterialComponents.Chip.Action</item>
    <item name="chipGroupStyle">@style/Widget.MaterialComponents.ChipGroup</item>
    <item name="chipStandaloneStyle">@style/Widget.MaterialComponents.Chip.Entry</item>
    <item name="extendedFloatingActionButtonStyle">@style/Widget.MaterialComponents.ExtendedFloatingActionButton.Icon</item>
    <item name="materialButtonStyle">@style/Widget.MaterialComponents.Button</item>
    <item name="materialButtonOutlinedStyle">@style/Widget.MaterialComponents.Button.OutlinedButton</item>
    <item name="materialButtonToggleGroupStyle">@style/Widget.MaterialComponents.MaterialButtonToggleGroup</item>
    <item name="materialCardViewStyle">@style/Widget.MaterialComponents.CardView</item>
    <item name="sliderStyle">@style/Widget.MaterialComponents.Slider</item>

    
    <item name="textAppearanceHeadline1">@style/TextAppearance.MaterialComponents.Headline1</item>
    <item name="textAppearanceHeadline2">@style/TextAppearance.MaterialComponents.Headline2</item>
    <item name="textAppearanceHeadline3">@style/TextAppearance.MaterialComponents.Headline3</item>
    <item name="textAppearanceHeadline4">@style/TextAppearance.MaterialComponents.Headline4</item>
    <item name="textAppearanceHeadline5">@style/TextAppearance.MaterialComponents.Headline5</item>
    <item name="textAppearanceHeadline6">@style/TextAppearance.MaterialComponents.Headline6</item>
    <item name="textAppearanceSubtitle1">@style/TextAppearance.MaterialComponents.Subtitle1</item>
    <item name="textAppearanceSubtitle2">@style/TextAppearance.MaterialComponents.Subtitle2</item>
    <item name="textAppearanceBody1">@style/TextAppearance.MaterialComponents.Body1</item>
    <item name="textAppearanceBody2">@style/TextAppearance.MaterialComponents.Body2</item>
    <item name="textAppearanceCaption">@style/TextAppearance.MaterialComponents.Caption</item>
    <item name="textAppearanceButton">@style/TextAppearance.MaterialComponents.Button</item>
    <item name="textAppearanceOverline">@style/TextAppearance.MaterialComponents.Overline</item>

    
    <item name="shapeAppearanceSmallComponent">
      @style/ShapeAppearance.MaterialComponents.SmallComponent
    </item>
    <item name="shapeAppearanceMediumComponent">
      @style/ShapeAppearance.MaterialComponents.MediumComponent
    </item>
    <item name="shapeAppearanceLargeComponent">
      @style/ShapeAppearance.MaterialComponents.LargeComponent
    </item>

    
    <item name="elevationOverlayEnabled">false</item>
    <item name="elevationOverlayColor">?attr/colorOnSurface</item>
  </style>
    <style name="Base.V14.ThemeOverlay.MaterialComponents.Dialog" parent="ThemeOverlay.AppCompat.Dialog">
    
    <item name="materialButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog.Flush</item>
  </style>
    <style name="Base.V14.ThemeOverlay.MaterialComponents.Dialog.Alert" parent="ThemeOverlay.AppCompat.Dialog.Alert">
    
    <item name="buttonBarButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog.Flush</item>
  </style>
    <style name="Base.V14.ThemeOverlay.MaterialComponents.MaterialAlertDialog" parent="ThemeOverlay.AppCompat.Dialog.Alert">
    
    <item name="alertDialogStyle">@style/MaterialAlertDialog.MaterialComponents</item>
    <item name="android:checkedTextViewStyle" ns2:ignore="NewApi">@style/Widget.MaterialComponents.CheckedTextView</item>
    <item name="android:dialogCornerRadius" ns2:ignore="newApi">@null</item>
    <item name="buttonBarButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog</item>
    <item name="buttonBarPositiveButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog</item>
    <item name="buttonBarNegativeButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog</item>
    <item name="buttonBarNeutralButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog.Flush</item>
    <item name="android:backgroundDimAmount">0.32</item>
    <item name="materialAlertDialogBodyTextStyle">@style/MaterialAlertDialog.MaterialComponents.Body.Text</item>
  </style>
    <style name="Base.V7.Theme.AppCompat" parent="Platform.AppCompat">
        <item name="windowNoTitle">false</item>
        <item name="windowActionBar">true</item>
        <item name="windowActionBarOverlay">false</item>
        <item name="windowActionModeOverlay">false</item>
        <item name="actionBarPopupTheme">@null</item>

        <item name="colorBackgroundFloating">@color/background_floating_material_dark</item>

        
        <item name="isLightTheme">false</item>

        <item name="selectableItemBackground">@drawable/abc_item_background_holo_dark</item>
        <item name="selectableItemBackgroundBorderless">?attr/selectableItemBackground</item>
        <item name="borderlessButtonStyle">@style/Widget.AppCompat.Button.Borderless</item>
        <item name="homeAsUpIndicator">@drawable/abc_ic_ab_back_material</item>

        <item name="dividerVertical">@drawable/abc_list_divider_mtrl_alpha</item>
        <item name="dividerHorizontal">@drawable/abc_list_divider_mtrl_alpha</item>

        
        <item name="actionBarTabStyle">@style/Widget.AppCompat.ActionBar.TabView</item>
        <item name="actionBarTabBarStyle">@style/Widget.AppCompat.ActionBar.TabBar</item>
        <item name="actionBarTabTextStyle">@style/Widget.AppCompat.ActionBar.TabText</item>
        <item name="actionButtonStyle">@style/Widget.AppCompat.ActionButton</item>
        <item name="actionOverflowButtonStyle">@style/Widget.AppCompat.ActionButton.Overflow</item>
        <item name="actionOverflowMenuStyle">@style/Widget.AppCompat.PopupMenu.Overflow</item>
        <item name="actionBarStyle">@style/Widget.AppCompat.ActionBar.Solid</item>
        <item name="actionBarSplitStyle">?attr/actionBarStyle</item>
        <item name="actionBarWidgetTheme">@null</item>
        <item name="actionBarTheme">@style/ThemeOverlay.AppCompat.ActionBar</item>
        <item name="actionBarSize">@dimen/abc_action_bar_default_height_material</item>
        <item name="actionBarDivider">?attr/dividerVertical</item>
        <item name="actionBarItemBackground">?attr/selectableItemBackgroundBorderless</item>
        <item name="actionMenuTextAppearance">@style/TextAppearance.AppCompat.Widget.ActionBar.Menu</item>
        <item name="actionMenuTextColor">?android:attr/textColorPrimaryDisableOnly</item>

        
        <item name="actionDropDownStyle">@style/Widget.AppCompat.Spinner.DropDown.ActionBar</item>

        
        <item name="actionModeStyle">@style/Widget.AppCompat.ActionMode</item>
        <item name="actionModeBackground">@drawable/abc_cab_background_top_material</item>
        <item name="actionModeSplitBackground">?attr/colorPrimaryDark</item>
        <item name="actionModeCloseDrawable">@drawable/abc_ic_ab_back_material</item>
        <item name="actionModeCloseButtonStyle">@style/Widget.AppCompat.ActionButton.CloseMode</item>

        <item name="actionModeCutDrawable">@drawable/abc_ic_menu_cut_mtrl_alpha</item>
        <item name="actionModeCopyDrawable">@drawable/abc_ic_menu_copy_mtrl_am_alpha</item>
        <item name="actionModePasteDrawable">@drawable/abc_ic_menu_paste_mtrl_am_alpha</item>
        <item name="actionModeSelectAllDrawable">@drawable/abc_ic_menu_selectall_mtrl_alpha</item>
        <item name="actionModeShareDrawable">@drawable/abc_ic_menu_share_mtrl_alpha</item>

        
        <item name="panelMenuListWidth">@dimen/abc_panel_menu_list_width</item>
        <item name="panelMenuListTheme">@style/Theme.AppCompat.CompactMenu</item>
        <item name="panelBackground">@drawable/abc_menu_hardkey_panel_mtrl_mult</item>
        <item name="android:panelBackground">@android:color/transparent</item>
        <item name="listChoiceBackgroundIndicator">@drawable/abc_list_selector_holo_dark</item>

        
        <item name="textAppearanceListItem">@style/TextAppearance.AppCompat.Subhead</item>
        <item name="textAppearanceListItemSmall">@style/TextAppearance.AppCompat.Subhead</item>
        <item name="textAppearanceListItemSecondary">@style/TextAppearance.AppCompat.Body1</item>
        <item name="listPreferredItemHeight">@dimen/abc_list_item_height_material</item>
        <item name="listPreferredItemHeightSmall">@dimen/abc_list_item_height_small_material</item>
        <item name="listPreferredItemHeightLarge">@dimen/abc_list_item_height_large_material</item>
        <item name="listPreferredItemPaddingLeft">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="listPreferredItemPaddingRight">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="listPreferredItemPaddingStart">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="listPreferredItemPaddingEnd">@dimen/abc_list_item_padding_horizontal_material</item>

        
        <item name="spinnerStyle">@style/Widget.AppCompat.Spinner</item>
        <item name="android:spinnerItemStyle">@style/Widget.AppCompat.TextView.SpinnerItem</item>
        <item name="android:dropDownListViewStyle">@style/Widget.AppCompat.ListView.DropDown</item>

        
        <item name="spinnerDropDownItemStyle">@style/Widget.AppCompat.DropDownItem.Spinner</item>
        <item name="dropdownListPreferredItemHeight">?attr/listPreferredItemHeightSmall</item>

        
        <item name="popupMenuStyle">@style/Widget.AppCompat.PopupMenu</item>
        <item name="textAppearanceLargePopupMenu">@style/TextAppearance.AppCompat.Widget.PopupMenu.Large</item>
        <item name="textAppearanceSmallPopupMenu">@style/TextAppearance.AppCompat.Widget.PopupMenu.Small</item>
        <item name="textAppearancePopupMenuHeader">@style/TextAppearance.AppCompat.Widget.PopupMenu.Header</item>
        <item name="listPopupWindowStyle">@style/Widget.AppCompat.ListPopupWindow</item>
        <item name="dropDownListViewStyle">?android:attr/dropDownListViewStyle</item>
        <item name="listMenuViewStyle">@style/Widget.AppCompat.ListMenuView</item>

        
        <item name="searchViewStyle">@style/Widget.AppCompat.SearchView</item>
        <item name="android:dropDownItemStyle">@style/Widget.AppCompat.DropDownItem.Spinner</item>
        <item name="textColorSearchUrl">@color/abc_search_url_text</item>
        <item name="textAppearanceSearchResultTitle">@style/TextAppearance.AppCompat.SearchResult.Title</item>
        <item name="textAppearanceSearchResultSubtitle">@style/TextAppearance.AppCompat.SearchResult.Subtitle</item>

        
        <item name="activityChooserViewStyle">@style/Widget.AppCompat.ActivityChooserView</item>

        
        <item name="toolbarStyle">@style/Widget.AppCompat.Toolbar</item>
        <item name="toolbarNavigationButtonStyle">@style/Widget.AppCompat.Toolbar.Button.Navigation</item>

        <item name="editTextStyle">@style/Widget.AppCompat.EditText</item>
        <item name="editTextBackground">@drawable/abc_edit_text_material</item>
        <item name="editTextColor">?android:attr/textColorPrimary</item>
        <item name="autoCompleteTextViewStyle">@style/Widget.AppCompat.AutoCompleteTextView</item>
        <item name="android:textViewStyle">@style/Widget.AppCompat.TextView</item>

        
        <item name="colorPrimaryDark">@color/primary_dark_material_dark</item>
        <item name="colorPrimary">@color/primary_material_dark</item>
        <item name="colorAccent">@color/accent_material_dark</item>

        <item name="colorControlNormal">?android:attr/textColorSecondary</item>
        <item name="colorControlActivated">?attr/colorAccent</item>
        <item name="colorControlHighlight">@color/ripple_material_dark</item>
        <item name="colorButtonNormal">@color/button_material_dark</item>
        <item name="colorSwitchThumbNormal">@color/switch_thumb_material_dark</item>
        <item name="controlBackground">?attr/selectableItemBackgroundBorderless</item>

        <item name="drawerArrowStyle">@style/Widget.AppCompat.DrawerArrowToggle</item>

        <item name="checkboxStyle">@style/Widget.AppCompat.CompoundButton.CheckBox</item>
        <item name="radioButtonStyle">@style/Widget.AppCompat.CompoundButton.RadioButton</item>
        <item name="switchStyle">@style/Widget.AppCompat.CompoundButton.Switch</item>

        <item name="ratingBarStyle">@style/Widget.AppCompat.RatingBar</item>
        <item name="ratingBarStyleIndicator">@style/Widget.AppCompat.RatingBar.Indicator</item>
        <item name="ratingBarStyleSmall">@style/Widget.AppCompat.RatingBar.Small</item>
        <item name="seekBarStyle">@style/Widget.AppCompat.SeekBar</item>

        
        <item name="buttonStyle">@style/Widget.AppCompat.Button</item>
        <item name="buttonStyleSmall">@style/Widget.AppCompat.Button.Small</item>
        <item name="android:textAppearanceButton">@style/TextAppearance.AppCompat.Widget.Button</item>

        <item name="imageButtonStyle">@style/Widget.AppCompat.ImageButton</item>

        <item name="buttonBarStyle">@style/Widget.AppCompat.ButtonBar</item>
        <item name="buttonBarButtonStyle">@style/Widget.AppCompat.Button.ButtonBar.AlertDialog</item>
        <item name="buttonBarPositiveButtonStyle">?attr/buttonBarButtonStyle</item>
        <item name="buttonBarNegativeButtonStyle">?attr/buttonBarButtonStyle</item>
        <item name="buttonBarNeutralButtonStyle">?attr/buttonBarButtonStyle</item>

        
        <item name="dialogTheme">@style/ThemeOverlay.AppCompat.Dialog</item>
        <item name="dialogPreferredPadding">@dimen/abc_dialog_padding_material</item>
        <item name="dialogCornerRadius">@dimen/abc_dialog_corner_radius_material</item>

        <item name="alertDialogTheme">@style/ThemeOverlay.AppCompat.Dialog.Alert</item>
        <item name="alertDialogStyle">@style/AlertDialog.AppCompat</item>
        <item name="alertDialogCenterButtons">false</item>
        <item name="textColorAlertDialogListItem">@color/abc_primary_text_material_dark</item>
        <item name="listDividerAlertDialog">@null</item>

        
        <item name="windowFixedWidthMajor">@null</item>
        <item name="windowFixedWidthMinor">@null</item>
        <item name="windowFixedHeightMajor">@null</item>
        <item name="windowFixedHeightMinor">@null</item>

        
        <item name="tooltipFrameBackground">@drawable/tooltip_frame_light</item>
        <item name="tooltipForegroundColor">@color/foreground_material_light</item>

        <item name="colorError">@color/error_color_material_dark</item>
    </style>
    <style name="Base.V7.Theme.AppCompat.Dialog" parent="Base.Theme.AppCompat">
        <item name="android:colorBackground">?attr/colorBackgroundFloating</item>
        <item name="android:colorBackgroundCacheHint">@null</item>

        <item name="android:windowFrame">@null</item>
        <item name="android:windowTitleStyle">@style/RtlOverlay.DialogWindowTitle.AppCompat</item>
        <item name="android:windowTitleBackgroundStyle">@style/Base.DialogWindowTitleBackground.AppCompat</item>
        <item name="android:windowBackground">@drawable/abc_dialog_material_background</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowAnimationStyle">@style/Animation.AppCompat.Dialog</item>
        <item name="android:windowSoftInputMode">stateUnspecified|adjustPan</item>

        <item name="windowActionBar">false</item>
        <item name="windowActionModeOverlay">true</item>

        <item name="listPreferredItemPaddingLeft">24dip</item>
        <item name="listPreferredItemPaddingRight">24dip</item>

        <item name="android:listDivider">@null</item>

        <item name="android:buttonBarStyle">@style/Widget.AppCompat.ButtonBar.AlertDialog</item>
        <item name="android:borderlessButtonStyle">@style/Widget.AppCompat.Button.Borderless</item>
        <item name="android:windowCloseOnTouchOutside">true</item>
    </style>
    <style name="Base.V7.Theme.AppCompat.Light" parent="Platform.AppCompat.Light">
        <item name="windowNoTitle">false</item>
        <item name="windowActionBar">true</item>
        <item name="windowActionBarOverlay">false</item>
        <item name="windowActionModeOverlay">false</item>
        <item name="actionBarPopupTheme">@null</item>

        <item name="colorBackgroundFloating">@color/background_floating_material_light</item>

        
        <item name="isLightTheme">true</item>

        <item name="selectableItemBackground">@drawable/abc_item_background_holo_light</item>
        <item name="selectableItemBackgroundBorderless">?attr/selectableItemBackground</item>
        <item name="borderlessButtonStyle">@style/Widget.AppCompat.Button.Borderless</item>
        <item name="homeAsUpIndicator">@drawable/abc_ic_ab_back_material</item>

        <item name="dividerVertical">@drawable/abc_list_divider_mtrl_alpha</item>
        <item name="dividerHorizontal">@drawable/abc_list_divider_mtrl_alpha</item>

        
        <item name="actionBarTabStyle">@style/Widget.AppCompat.Light.ActionBar.TabView</item>
        <item name="actionBarTabBarStyle">@style/Widget.AppCompat.Light.ActionBar.TabBar</item>
        <item name="actionBarTabTextStyle">@style/Widget.AppCompat.Light.ActionBar.TabText</item>
        <item name="actionButtonStyle">@style/Widget.AppCompat.Light.ActionButton</item>
        <item name="actionOverflowButtonStyle">@style/Widget.AppCompat.Light.ActionButton.Overflow</item>
        <item name="actionOverflowMenuStyle">@style/Widget.AppCompat.Light.PopupMenu.Overflow</item>
        <item name="actionBarStyle">@style/Widget.AppCompat.Light.ActionBar.Solid</item>
        <item name="actionBarSplitStyle">?attr/actionBarStyle</item>
        <item name="actionBarWidgetTheme">@null</item>
        <item name="actionBarTheme">@style/ThemeOverlay.AppCompat.ActionBar</item>
        <item name="actionBarSize">@dimen/abc_action_bar_default_height_material</item>
        <item name="actionBarDivider">?attr/dividerVertical</item>
        <item name="actionBarItemBackground">?attr/selectableItemBackgroundBorderless</item>
        <item name="actionMenuTextAppearance">@style/TextAppearance.AppCompat.Widget.ActionBar.Menu</item>
        <item name="actionMenuTextColor">?android:attr/textColorPrimaryDisableOnly</item>

        
        <item name="actionModeStyle">@style/Widget.AppCompat.ActionMode</item>
        <item name="actionModeBackground">@drawable/abc_cab_background_top_material</item>
        <item name="actionModeSplitBackground">?attr/colorPrimaryDark</item>
        <item name="actionModeCloseDrawable">@drawable/abc_ic_ab_back_material</item>
        <item name="actionModeCloseButtonStyle">@style/Widget.AppCompat.ActionButton.CloseMode</item>

        <item name="actionModeCutDrawable">@drawable/abc_ic_menu_cut_mtrl_alpha</item>
        <item name="actionModeCopyDrawable">@drawable/abc_ic_menu_copy_mtrl_am_alpha</item>
        <item name="actionModePasteDrawable">@drawable/abc_ic_menu_paste_mtrl_am_alpha</item>
        <item name="actionModeSelectAllDrawable">@drawable/abc_ic_menu_selectall_mtrl_alpha</item>
        <item name="actionModeShareDrawable">@drawable/abc_ic_menu_share_mtrl_alpha</item>

        
        <item name="actionDropDownStyle">@style/Widget.AppCompat.Light.Spinner.DropDown.ActionBar</item>

        
        <item name="panelMenuListWidth">@dimen/abc_panel_menu_list_width</item>
        <item name="panelMenuListTheme">@style/Theme.AppCompat.CompactMenu</item>
        <item name="panelBackground">@drawable/abc_menu_hardkey_panel_mtrl_mult</item>
        <item name="android:panelBackground">@android:color/transparent</item>
        <item name="listChoiceBackgroundIndicator">@drawable/abc_list_selector_holo_light</item>

        
        <item name="textAppearanceListItem">@style/TextAppearance.AppCompat.Subhead</item>
        <item name="textAppearanceListItemSmall">@style/TextAppearance.AppCompat.Subhead</item>
        <item name="textAppearanceListItemSecondary">@style/TextAppearance.AppCompat.Body1</item>
        <item name="listPreferredItemHeight">@dimen/abc_list_item_height_material</item>
        <item name="listPreferredItemHeightSmall">@dimen/abc_list_item_height_small_material</item>
        <item name="listPreferredItemHeightLarge">@dimen/abc_list_item_height_large_material</item>
        <item name="listPreferredItemPaddingLeft">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="listPreferredItemPaddingRight">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="listPreferredItemPaddingStart">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="listPreferredItemPaddingEnd">@dimen/abc_list_item_padding_horizontal_material</item>

        
        <item name="spinnerStyle">@style/Widget.AppCompat.Spinner</item>
        <item name="android:spinnerItemStyle">@style/Widget.AppCompat.TextView.SpinnerItem</item>
        <item name="android:dropDownListViewStyle">@style/Widget.AppCompat.ListView.DropDown</item>

        
        <item name="spinnerDropDownItemStyle">@style/Widget.AppCompat.DropDownItem.Spinner</item>
        <item name="dropdownListPreferredItemHeight">?attr/listPreferredItemHeightSmall</item>

        
        <item name="popupMenuStyle">@style/Widget.AppCompat.Light.PopupMenu</item>
        <item name="textAppearanceLargePopupMenu">@style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Large</item>
        <item name="textAppearanceSmallPopupMenu">@style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Small</item>
        <item name="textAppearancePopupMenuHeader">@style/TextAppearance.AppCompat.Widget.PopupMenu.Header</item>
        <item name="listPopupWindowStyle">@style/Widget.AppCompat.ListPopupWindow</item>
        <item name="dropDownListViewStyle">?android:attr/dropDownListViewStyle</item>
        <item name="listMenuViewStyle">@style/Widget.AppCompat.ListMenuView</item>

        
        <item name="searchViewStyle">@style/Widget.AppCompat.Light.SearchView</item>
        <item name="android:dropDownItemStyle">@style/Widget.AppCompat.DropDownItem.Spinner</item>
        <item name="textColorSearchUrl">@color/abc_search_url_text</item>
        <item name="textAppearanceSearchResultTitle">@style/TextAppearance.AppCompat.SearchResult.Title</item>
        <item name="textAppearanceSearchResultSubtitle">@style/TextAppearance.AppCompat.SearchResult.Subtitle</item>

        
        <item name="activityChooserViewStyle">@style/Widget.AppCompat.ActivityChooserView</item>

        
        <item name="toolbarStyle">@style/Widget.AppCompat.Toolbar</item>
        <item name="toolbarNavigationButtonStyle">@style/Widget.AppCompat.Toolbar.Button.Navigation</item>

        <item name="editTextStyle">@style/Widget.AppCompat.EditText</item>
        <item name="editTextBackground">@drawable/abc_edit_text_material</item>
        <item name="editTextColor">?android:attr/textColorPrimary</item>
        <item name="autoCompleteTextViewStyle">@style/Widget.AppCompat.AutoCompleteTextView</item>
        <item name="android:textViewStyle">@style/Widget.AppCompat.TextView</item>

        
        <item name="colorPrimaryDark">@color/primary_dark_material_light</item>
        <item name="colorPrimary">@color/primary_material_light</item>
        <item name="colorAccent">@color/accent_material_light</item>

        <item name="colorControlNormal">?android:attr/textColorSecondary</item>
        <item name="colorControlActivated">?attr/colorAccent</item>
        <item name="colorControlHighlight">@color/ripple_material_light</item>
        <item name="colorButtonNormal">@color/button_material_light</item>
        <item name="colorSwitchThumbNormal">@color/switch_thumb_material_light</item>
        <item name="controlBackground">?attr/selectableItemBackgroundBorderless</item>

        <item name="drawerArrowStyle">@style/Widget.AppCompat.DrawerArrowToggle</item>

        <item name="checkboxStyle">@style/Widget.AppCompat.CompoundButton.CheckBox</item>
        <item name="radioButtonStyle">@style/Widget.AppCompat.CompoundButton.RadioButton</item>
        <item name="switchStyle">@style/Widget.AppCompat.CompoundButton.Switch</item>

        <item name="ratingBarStyle">@style/Widget.AppCompat.RatingBar</item>
        <item name="ratingBarStyleIndicator">@style/Widget.AppCompat.RatingBar.Indicator</item>
        <item name="ratingBarStyleSmall">@style/Widget.AppCompat.RatingBar.Small</item>
        <item name="seekBarStyle">@style/Widget.AppCompat.SeekBar</item>

        
        <item name="buttonStyle">@style/Widget.AppCompat.Button</item>
        <item name="buttonStyleSmall">@style/Widget.AppCompat.Button.Small</item>
        <item name="android:textAppearanceButton">@style/TextAppearance.AppCompat.Widget.Button</item>

        <item name="imageButtonStyle">@style/Widget.AppCompat.ImageButton</item>

        <item name="buttonBarStyle">@style/Widget.AppCompat.ButtonBar</item>
        <item name="buttonBarButtonStyle">@style/Widget.AppCompat.Button.ButtonBar.AlertDialog</item>
        <item name="buttonBarPositiveButtonStyle">?attr/buttonBarButtonStyle</item>
        <item name="buttonBarNegativeButtonStyle">?attr/buttonBarButtonStyle</item>
        <item name="buttonBarNeutralButtonStyle">?attr/buttonBarButtonStyle</item>

        
        <item name="dialogTheme">@style/ThemeOverlay.AppCompat.Dialog</item>
        <item name="dialogPreferredPadding">@dimen/abc_dialog_padding_material</item>
        <item name="dialogCornerRadius">@dimen/abc_dialog_corner_radius_material</item>

        <item name="alertDialogTheme">@style/ThemeOverlay.AppCompat.Dialog.Alert</item>
        <item name="alertDialogStyle">@style/AlertDialog.AppCompat.Light</item>
        <item name="alertDialogCenterButtons">false</item>
        <item name="textColorAlertDialogListItem">@color/abc_primary_text_material_light</item>
        <item name="listDividerAlertDialog">@null</item>

        
        <item name="windowFixedWidthMajor">@null</item>
        <item name="windowFixedWidthMinor">@null</item>
        <item name="windowFixedHeightMajor">@null</item>
        <item name="windowFixedHeightMinor">@null</item>

        
        <item name="tooltipFrameBackground">@drawable/tooltip_frame_dark</item>
        <item name="tooltipForegroundColor">@color/foreground_material_dark</item>

        <item name="colorError">@color/error_color_material_light</item>
    </style>
    <style name="Base.V7.Theme.AppCompat.Light.Dialog" parent="Base.Theme.AppCompat.Light">
        <item name="android:colorBackground">?attr/colorBackgroundFloating</item>
        <item name="android:colorBackgroundCacheHint">@null</item>

        <item name="android:windowFrame">@null</item>
        <item name="android:windowTitleStyle">@style/RtlOverlay.DialogWindowTitle.AppCompat</item>
        <item name="android:windowTitleBackgroundStyle">@style/Base.DialogWindowTitleBackground.AppCompat</item>
        <item name="android:windowBackground">@drawable/abc_dialog_material_background</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowAnimationStyle">@style/Animation.AppCompat.Dialog</item>
        <item name="android:windowSoftInputMode">stateUnspecified|adjustPan</item>

        <item name="windowActionBar">false</item>
        <item name="windowActionModeOverlay">true</item>

        <item name="listPreferredItemPaddingLeft">24dip</item>
        <item name="listPreferredItemPaddingRight">24dip</item>

        <item name="android:listDivider">@null</item>

        <item name="android:buttonBarStyle">@style/Widget.AppCompat.ButtonBar.AlertDialog</item>
        <item name="android:borderlessButtonStyle">@style/Widget.AppCompat.Button.Borderless</item>
        <item name="android:windowCloseOnTouchOutside">true</item>
    </style>
    <style name="Base.V7.ThemeOverlay.AppCompat.Dialog" parent="Base.ThemeOverlay.AppCompat">
        <item name="android:colorBackgroundCacheHint">@null</item>
        <item name="android:colorBackground">?attr/colorBackgroundFloating</item>

        <item name="android:windowFrame">@null</item>
        <item name="android:windowTitleStyle">@style/RtlOverlay.DialogWindowTitle.AppCompat</item>
        <item name="android:windowTitleBackgroundStyle">@style/Base.DialogWindowTitleBackground.AppCompat</item>
        <item name="android:windowBackground">@drawable/abc_dialog_material_background</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowAnimationStyle">@style/Animation.AppCompat.Dialog</item>
        <item name="android:windowSoftInputMode">stateUnspecified|adjustPan</item>

        <item name="windowActionBar">false</item>
        <item name="windowActionModeOverlay">true</item>

        <item name="listPreferredItemPaddingLeft">24dip</item>
        <item name="listPreferredItemPaddingRight">24dip</item>

        <item name="android:listDivider">@null</item>

        <item name="windowFixedWidthMajor">@null</item>
        <item name="windowFixedWidthMinor">@null</item>
        <item name="windowFixedHeightMajor">@null</item>
        <item name="windowFixedHeightMinor">@null</item>

        <item name="android:buttonBarStyle">@style/Widget.AppCompat.ButtonBar.AlertDialog</item>
        <item name="android:borderlessButtonStyle">@style/Widget.AppCompat.Button.Borderless</item>
        <item name="android:windowCloseOnTouchOutside">true</item>
    </style>
    <style name="Base.V7.Widget.AppCompat.AutoCompleteTextView" parent="android:Widget.AutoCompleteTextView">
        <item name="android:dropDownSelector">?attr/listChoiceBackgroundIndicator</item>
        <item name="android:popupBackground">@drawable/abc_popup_background_mtrl_mult</item>
        <item name="android:background">?attr/editTextBackground</item>
        <item name="android:textColor">?attr/editTextColor</item>
        <item name="android:textAppearance">?android:attr/textAppearanceMediumInverse</item>
        <item name="android:textCursorDrawable">@drawable/abc_text_cursor_material</item>
    </style>
    <style name="Base.V7.Widget.AppCompat.EditText" parent="android:Widget.EditText">
        <item name="android:background">?attr/editTextBackground</item>
        <item name="android:textColor">?attr/editTextColor</item>
        <item name="android:textAppearance">?android:attr/textAppearanceMediumInverse</item>
        <item name="android:textCursorDrawable">@drawable/abc_text_cursor_material</item>
    </style>
    <style name="Base.V7.Widget.AppCompat.Toolbar" parent="android:Widget">
        <item name="titleTextAppearance">@style/TextAppearance.Widget.AppCompat.Toolbar.Title</item>
        <item name="subtitleTextAppearance">@style/TextAppearance.Widget.AppCompat.Toolbar.Subtitle</item>
        <item name="android:minHeight">?attr/actionBarSize</item>
        <item name="titleMargin">4dp</item>
        <item name="maxButtonHeight">@dimen/abc_action_bar_default_height_material</item>
        <item name="buttonGravity">top</item>
        <item name="collapseIcon">?attr/homeAsUpIndicator</item>
        <item name="collapseContentDescription">@string/abc_toolbar_collapse_description</item>
        <item name="contentInsetStart">16dp</item>
        <item name="contentInsetStartWithNavigation">@dimen/abc_action_bar_content_inset_with_nav</item>
        <item name="android:paddingLeft">@dimen/abc_action_bar_default_padding_start_material</item>
        <item name="android:paddingRight">@dimen/abc_action_bar_default_padding_end_material</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionBar" parent="">
        <item name="displayOptions">showTitle</item>
        <item name="divider">?attr/dividerVertical</item>
        <item name="height">?attr/actionBarSize</item>

        <item name="titleTextStyle">@style/TextAppearance.AppCompat.Widget.ActionBar.Title</item>
        <item name="subtitleTextStyle">@style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle</item>

        <item name="background">@null</item>
        <item name="backgroundStacked">@null</item>
        <item name="backgroundSplit">@null</item>

        <item name="actionButtonStyle">@style/Widget.AppCompat.ActionButton</item>
        <item name="actionOverflowButtonStyle">@style/Widget.AppCompat.ActionButton.Overflow</item>

        <item name="android:gravity">center_vertical</item>
        <item name="contentInsetStart">@dimen/abc_action_bar_content_inset_material</item>
        <item name="contentInsetStartWithNavigation">@dimen/abc_action_bar_content_inset_with_nav</item>
        <item name="contentInsetEnd">@dimen/abc_action_bar_content_inset_material</item>
        <item name="elevation">@dimen/abc_action_bar_elevation_material</item>
        <item name="popupTheme">?attr/actionBarPopupTheme</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionBar.Solid">
        <item name="background">?attr/colorPrimary</item>
        <item name="backgroundStacked">?attr/colorPrimary</item>
        <item name="backgroundSplit">?attr/colorPrimary</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionBar.TabBar" parent="">
        <item name="divider">?attr/actionBarDivider</item>
        <item name="showDividers">middle</item>
        <item name="dividerPadding">8dip</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionBar.TabText" parent="">
        <item name="android:textAppearance">@style/TextAppearance.AppCompat.Medium</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
        <item name="android:textSize">12sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:ellipsize">marquee</item>
        <item name="android:maxLines">2</item>
        <item name="android:maxWidth">180dp</item>
        <item name="textAllCaps">true</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionBar.TabView" parent="">
        <item name="android:background">@drawable/abc_tab_indicator_material</item>
        <item name="android:gravity">center_horizontal</item>
        <item name="android:paddingLeft">16dip</item>
        <item name="android:paddingRight">16dip</item>
        <item name="android:layout_width">0dip</item>
        <item name="android:layout_weight">1</item>
        <item name="android:minWidth">80dip</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionButton" parent="RtlUnderlay.Widget.AppCompat.ActionButton">
        <item name="android:background">?attr/actionBarItemBackground</item>
        <item name="android:minWidth">@dimen/abc_action_button_min_width_material</item>
        <item name="android:minHeight">@dimen/abc_action_button_min_height_material</item>
        <item name="android:scaleType">center</item>
        <item name="android:gravity">center</item>
        <item name="android:maxLines">2</item>
        <item name="textAllCaps">@bool/abc_config_actionMenuItemAllCaps</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionButton.CloseMode">
        <item name="android:background">?attr/controlBackground</item>
        <item name="android:minWidth">56dp</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionButton.Overflow" parent="RtlUnderlay.Widget.AppCompat.ActionButton.Overflow">
        <item name="srcCompat">@drawable/abc_ic_menu_overflow_material</item>
        <item name="android:background">?attr/actionBarItemBackground</item>
        <item name="android:contentDescription">@string/abc_action_menu_overflow_description</item>
        <item name="android:minWidth">@dimen/abc_action_button_min_width_overflow_material</item>
        <item name="android:minHeight">@dimen/abc_action_button_min_height_material</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionMode" parent="">
        <item name="background">?attr/actionModeBackground</item>
        <item name="backgroundSplit">?attr/actionModeSplitBackground</item>
        <item name="height">?attr/actionBarSize</item>
        <item name="titleTextStyle">@style/TextAppearance.AppCompat.Widget.ActionMode.Title</item>
        <item name="subtitleTextStyle">@style/TextAppearance.AppCompat.Widget.ActionMode.Subtitle</item>
        <item name="closeItemLayout">@layout/abc_action_mode_close_item_material</item>
    </style>
    <style name="Base.Widget.AppCompat.ActivityChooserView" parent="">
        <item name="android:gravity">center</item>
        <item name="android:background">@drawable/abc_ab_share_pack_mtrl_alpha</item>
        <item name="divider">?attr/dividerVertical</item>
        <item name="showDividers">middle</item>
        <item name="dividerPadding">6dip</item>
    </style>
    <style name="Base.Widget.AppCompat.AutoCompleteTextView" parent="Base.V7.Widget.AppCompat.AutoCompleteTextView"/>
    <style name="Base.Widget.AppCompat.Button" parent="android:Widget">
        <item name="android:background">@drawable/abc_btn_default_mtrl_shape</item>
        <item name="android:textAppearance">?android:attr/textAppearanceButton</item>
        <item name="android:minHeight">48dip</item>
        <item name="android:minWidth">88dip</item>
        <item name="android:focusable">true</item>
        <item name="android:clickable">true</item>
        <item name="android:gravity">center_vertical|center_horizontal</item>
    </style>
    <style name="Base.Widget.AppCompat.Button.Borderless">
        <item name="android:background">@drawable/abc_btn_borderless_material</item>
    </style>
    <style name="Base.Widget.AppCompat.Button.Borderless.Colored">
        <item name="android:textAppearance">@style/TextAppearance.AppCompat.Widget.Button.Borderless.Colored</item>
    </style>
    <style name="Base.Widget.AppCompat.Button.ButtonBar.AlertDialog" parent="Widget.AppCompat.Button.Borderless.Colored">
        <item name="android:minWidth">64dp</item>
        <item name="android:minHeight">@dimen/abc_alert_dialog_button_bar_height</item>
    </style>
    <style name="Base.Widget.AppCompat.Button.Colored">
        <item name="android:background">@drawable/abc_btn_colored_material</item>
        <item name="android:textAppearance">@style/TextAppearance.AppCompat.Widget.Button.Colored</item>
    </style>
    <style name="Base.Widget.AppCompat.Button.Small">
        <item name="android:minHeight">48dip</item>
        <item name="android:minWidth">48dip</item>
    </style>
    <style name="Base.Widget.AppCompat.ButtonBar" parent="android:Widget">
        <item name="android:background">@null</item>
    </style>
    <style name="Base.Widget.AppCompat.ButtonBar.AlertDialog"/>
    <style name="Base.Widget.AppCompat.CompoundButton.CheckBox" parent="android:Widget.CompoundButton.CheckBox">
        <item name="android:button">?android:attr/listChoiceIndicatorMultiple</item>
        <item name="buttonCompat">?attr/listChoiceIndicatorMultipleAnimated</item>
        <item name="android:background">?attr/controlBackground</item>
    </style>
    <style name="Base.Widget.AppCompat.CompoundButton.RadioButton" parent="android:Widget.CompoundButton.RadioButton">
        <item name="android:button">?android:attr/listChoiceIndicatorSingle</item>
        <item name="buttonCompat">?attr/listChoiceIndicatorSingleAnimated</item>
        <item name="android:background">?attr/controlBackground</item>
    </style>
    <style name="Base.Widget.AppCompat.CompoundButton.Switch" parent="android:Widget.CompoundButton">
        <item name="track">@drawable/abc_switch_track_mtrl_alpha</item>
        <item name="android:thumb">@drawable/abc_switch_thumb_material</item>
        <item name="switchTextAppearance">@style/TextAppearance.AppCompat.Widget.Switch</item>
        <item name="android:background">?attr/controlBackground</item>
        <item name="showText">false</item>
        <item name="switchPadding">@dimen/abc_switch_padding</item>
        <item name="android:textOn">@string/abc_capital_on</item>
        <item name="android:textOff">@string/abc_capital_off</item>
    </style>
    <style name="Base.Widget.AppCompat.DrawerArrowToggle" parent="Base.Widget.AppCompat.DrawerArrowToggle.Common">
        <item name="barLength">18dp</item>
        <item name="gapBetweenBars">3dp</item>
        <item name="drawableSize">24dp</item>
    </style>
    <style name="Base.Widget.AppCompat.DrawerArrowToggle.Common" parent="">
        <item name="color">?android:attr/textColorSecondary</item>
        <item name="spinBars">true</item>
        <item name="thickness">2dp</item>
        <item name="arrowShaftLength">16dp</item>
        <item name="arrowHeadLength">8dp</item>
    </style>
    <style name="Base.Widget.AppCompat.DropDownItem.Spinner" parent="">
        <item name="android:textAppearance">@style/TextAppearance.AppCompat.Widget.DropDownItem</item>
        <item name="android:paddingLeft">8dp</item>
        <item name="android:paddingRight">8dp</item>
        <item name="android:gravity">center_vertical</item>
    </style>
    <style name="Base.Widget.AppCompat.EditText" parent="Base.V7.Widget.AppCompat.EditText"/>
    <style name="Base.Widget.AppCompat.ImageButton" parent="android:Widget.ImageButton">
        <item name="android:background">@drawable/abc_btn_default_mtrl_shape</item>
    </style>
    <style name="Base.Widget.AppCompat.Light.ActionBar" parent="Base.Widget.AppCompat.ActionBar">
        <item name="actionButtonStyle">@style/Widget.AppCompat.Light.ActionButton</item>
        <item name="actionOverflowButtonStyle">@style/Widget.AppCompat.Light.ActionButton.Overflow</item>
    </style>
    <style name="Base.Widget.AppCompat.Light.ActionBar.Solid">
        <item name="background">?attr/colorPrimary</item>
        <item name="backgroundStacked">?attr/colorPrimary</item>
        <item name="backgroundSplit">?attr/colorPrimary</item>
    </style>
    <style name="Base.Widget.AppCompat.Light.ActionBar.TabBar" parent="Base.Widget.AppCompat.ActionBar.TabBar">
    </style>
    <style name="Base.Widget.AppCompat.Light.ActionBar.TabText" parent="Base.Widget.AppCompat.ActionBar.TabText">
    </style>
    <style name="Base.Widget.AppCompat.Light.ActionBar.TabText.Inverse" parent="Base.Widget.AppCompat.Light.ActionBar.TabText">
        <item name="android:textAppearance">@style/TextAppearance.AppCompat.Medium.Inverse</item>
    </style>
    <style name="Base.Widget.AppCompat.Light.ActionBar.TabView" parent="Base.Widget.AppCompat.ActionBar.TabView">
        <item name="android:background">@drawable/abc_tab_indicator_material</item>
    </style>
    <style name="Base.Widget.AppCompat.Light.PopupMenu" parent="@style/Widget.AppCompat.ListPopupWindow">
    </style>
    <style name="Base.Widget.AppCompat.Light.PopupMenu.Overflow">
        <item name="overlapAnchor">true</item>
        <item name="android:dropDownHorizontalOffset">-4dip</item>
    </style>
    <style name="Base.Widget.AppCompat.ListMenuView" parent="android:Widget">
        <item name="subMenuArrow">@drawable/abc_ic_arrow_drop_right_black_24dp</item>
    </style>
    <style name="Base.Widget.AppCompat.ListPopupWindow" parent="">
        <item name="android:dropDownSelector">?attr/listChoiceBackgroundIndicator</item>
        <item name="android:popupBackground">@drawable/abc_popup_background_mtrl_mult</item>
        <item name="android:dropDownVerticalOffset">0dip</item>
        <item name="android:dropDownHorizontalOffset">0dip</item>
        <item name="android:dropDownWidth">wrap_content</item>
    </style>
    <style name="Base.Widget.AppCompat.ListView" parent="android:Widget.ListView">
        <item name="android:listSelector">?attr/listChoiceBackgroundIndicator</item>
    </style>
    <style name="Base.Widget.AppCompat.ListView.DropDown">
        <item name="android:divider">@null</item>
    </style>
    <style name="Base.Widget.AppCompat.ListView.Menu" parent="android:Widget.ListView.Menu">
        <item name="android:listSelector">?attr/listChoiceBackgroundIndicator</item>
        <item name="android:divider">?attr/dividerHorizontal</item>
    </style>
    <style name="Base.Widget.AppCompat.PopupMenu" parent="@style/Widget.AppCompat.ListPopupWindow">
    </style>
    <style name="Base.Widget.AppCompat.PopupMenu.Overflow">
        <item name="overlapAnchor">true</item>
        <item name="android:dropDownHorizontalOffset">-4dip</item>
    </style>
    <style name="Base.Widget.AppCompat.PopupWindow" parent="android:Widget.PopupWindow">
    </style>
    <style name="Base.Widget.AppCompat.ProgressBar" parent="android:Widget.Holo.ProgressBar">
    </style>
    <style name="Base.Widget.AppCompat.ProgressBar.Horizontal" parent="android:Widget.Holo.ProgressBar.Horizontal">
    </style>
    <style name="Base.Widget.AppCompat.RatingBar" parent="android:Widget.RatingBar">
        <item name="android:progressDrawable">@drawable/abc_ratingbar_material</item>
        <item name="android:indeterminateDrawable">@drawable/abc_ratingbar_material</item>
    </style>
    <style name="Base.Widget.AppCompat.RatingBar.Indicator" parent="android:Widget.RatingBar">
        <item name="android:progressDrawable">@drawable/abc_ratingbar_indicator_material</item>
        <item name="android:indeterminateDrawable">@drawable/abc_ratingbar_indicator_material</item>
        <item name="android:minHeight">36dp</item>
        <item name="android:maxHeight">36dp</item>
        <item name="android:isIndicator">true</item>
        <item name="android:thumb">@null</item>
    </style>
    <style name="Base.Widget.AppCompat.RatingBar.Small" parent="android:Widget.RatingBar">
        <item name="android:progressDrawable">@drawable/abc_ratingbar_small_material</item>
        <item name="android:indeterminateDrawable">@drawable/abc_ratingbar_small_material</item>
        <item name="android:minHeight">16dp</item>
        <item name="android:maxHeight">16dp</item>
        <item name="android:isIndicator">true</item>
        <item name="android:thumb">@null</item>
    </style>
    <style name="Base.Widget.AppCompat.SearchView" parent="android:Widget">
        <item name="layout">@layout/abc_search_view</item>
        <item name="queryBackground">@drawable/abc_textfield_search_material</item>
        <item name="submitBackground">@drawable/abc_textfield_search_material</item>
        <item name="closeIcon">@drawable/abc_ic_clear_material</item>
        <item name="searchIcon">@drawable/abc_ic_search_api_material</item>
        <item name="searchHintIcon">@drawable/abc_ic_search_api_material</item>
        <item name="goIcon">@drawable/abc_ic_go_search_api_material</item>
        <item name="voiceIcon">@drawable/abc_ic_voice_search_api_material</item>
        <item name="commitIcon">@drawable/abc_ic_commit_search_api_mtrl_alpha</item>
        <item name="suggestionRowLayout">@layout/abc_search_dropdown_item_icons_2line</item>
    </style>
    <style name="Base.Widget.AppCompat.SearchView.ActionBar">
        <item name="queryBackground">@null</item>
        <item name="submitBackground">@null</item>
        <item name="searchHintIcon">@null</item>
        <item name="defaultQueryHint">@string/abc_search_hint</item>
    </style>
    <style name="Base.Widget.AppCompat.SeekBar" parent="android:Widget">
        <item name="android:indeterminateOnly">false</item>
        <item name="android:progressDrawable">@drawable/abc_seekbar_track_material</item>
        <item name="android:indeterminateDrawable">@drawable/abc_seekbar_track_material</item>
        <item name="android:thumb">@drawable/abc_seekbar_thumb_material</item>
        <item name="android:focusable">true</item>
        <item name="android:paddingLeft">16dip</item>
        <item name="android:paddingRight">16dip</item>
    </style>
    <style name="Base.Widget.AppCompat.SeekBar.Discrete">
        <item name="tickMark">@drawable/abc_seekbar_tick_mark_material</item>
    </style>
    <style name="Base.Widget.AppCompat.Spinner" parent="Platform.Widget.AppCompat.Spinner">
        <item name="android:background">@drawable/abc_spinner_mtrl_am_alpha</item>
        <item name="android:popupBackground">@drawable/abc_popup_background_mtrl_mult</item>
        <item name="android:dropDownSelector">?attr/listChoiceBackgroundIndicator</item>
        <item name="android:dropDownVerticalOffset">0dip</item>
        <item name="android:dropDownHorizontalOffset">0dip</item>
        <item name="android:dropDownWidth">wrap_content</item>
        <item name="android:clickable">true</item>
        <item name="android:gravity">left|start|center_vertical</item>
        <item name="overlapAnchor">true</item>
    </style>
    <style name="Base.Widget.AppCompat.Spinner.Underlined">
        <item name="android:background">@drawable/abc_spinner_textfield_background_material</item>
    </style>
    <style name="Base.Widget.AppCompat.TextView" parent="android:Widget.TextView"/>
    <style name="Base.Widget.AppCompat.TextView.SpinnerItem" parent="android:Widget.TextView.SpinnerItem">
        <item name="android:textAppearance">@style/TextAppearance.AppCompat.Widget.TextView.SpinnerItem</item>
        <item name="android:paddingLeft">8dp</item>
        <item name="android:paddingRight">8dp</item>
    </style>
    <style name="Base.Widget.AppCompat.Toolbar" parent="Base.V7.Widget.AppCompat.Toolbar"/>
    <style name="Base.Widget.AppCompat.Toolbar.Button.Navigation" parent="android:Widget">
        <item name="android:background">?attr/controlBackground</item>
        <item name="android:minWidth">56dp</item>
        <item name="android:scaleType">center</item>
    </style>
    <style name="Base.Widget.Design.TabLayout" parent="android:Widget">
    <item name="android:background">@null</item>
    <item name="tabIconTint">@null</item>
    <item name="tabMaxWidth">@dimen/design_tab_max_width</item>
    <item name="tabIndicatorAnimationDuration">@integer/design_tab_indicator_anim_duration_ms</item>
    <item name="tabIndicatorColor">?attr/colorAccent</item>
    <item name="tabIndicatorGravity">bottom</item>
    <item name="tabIndicator">@drawable/mtrl_tabs_default_indicator</item>
    <item name="tabPaddingStart">12dp</item>
    <item name="tabPaddingEnd">12dp</item>
    <item name="tabTextAppearance">@style/TextAppearance.Design.Tab</item>
    <item name="tabTextColor">@null</item>
    <item name="tabRippleColor">?attr/colorControlHighlight</item>
    <item name="tabUnboundedRipple">false</item>
  </style>
    <style name="Base.Widget.MaterialComponents.AutoCompleteTextView" parent="Widget.AppCompat.AutoCompleteTextView">
    <item name="enforceMaterialTheme">true</item>
    <item name="enforceTextAppearance">true</item>
    <item name="android:background">@null</item>
    <item name="android:paddingStart" ns2:ignore="NewApi">12dp</item>
    <item name="android:paddingEnd" ns2:ignore="NewApi">12dp</item>
    <item name="android:paddingLeft">12dp</item>
    <item name="android:paddingRight">12dp</item>
    <item name="android:paddingTop">16dp</item>
    <item name="android:paddingBottom">16dp</item>
    <item name="android:textAppearance">?attr/textAppearanceSubtitle1</item>
    <item name="android:dropDownVerticalOffset">@dimen/mtrl_exposed_dropdown_menu_popup_vertical_offset</item>
    <item name="android:popupElevation" ns2:ignore="NewApi">@dimen/mtrl_exposed_dropdown_menu_popup_elevation</item>
  </style>
    <style name="Base.Widget.MaterialComponents.CheckedTextView" parent="android:Widget"/>
    <style name="Base.Widget.MaterialComponents.Chip" parent="android:Widget">
    <item name="android:focusable">true</item>
    <item name="android:clickable">true</item>
    <item name="android:checkable">false</item>
    <item name="android:stateListAnimator" ns2:ignore="NewApi">
      @animator/mtrl_chip_state_list_anim
    </item>

    <item name="chipIconVisible">true</item>
    <item name="checkedIconVisible">true</item>
    <item name="closeIconVisible">true</item>

    <item name="chipIcon">@null</item>
    <item name="checkedIcon">@drawable/ic_mtrl_chip_checked_circle</item>
    <item name="closeIcon">@drawable/ic_mtrl_chip_close_circle</item>

    <item name="android:text">@null</item>
    <item name="android:includeFontPadding">false</item>
    <item name="enforceMaterialTheme">true</item>
    <item name="enforceTextAppearance">true</item>
    <item name="android:textAppearance">?attr/textAppearanceBody2</item>
    <item name="android:textColor">@color/mtrl_chip_text_color</item>
    <item name="closeIconTint">@color/mtrl_chip_close_icon_tint</item>

    <item name="chipSurfaceColor">@color/mtrl_chip_surface_color</item>
    <item name="chipBackgroundColor">@color/mtrl_chip_background_color</item>
    <item name="chipStrokeColor">?attr/colorOnSurface</item>
    <item name="chipStrokeWidth">0dp</item>
    <item name="rippleColor">@color/mtrl_chip_ripple_color</item>

    <item name="chipMinTouchTargetSize">48dp</item>
    <item name="ensureMinTouchTargetSize">true</item>
    <item name="chipMinHeight">32dp</item>
    <item name="chipIconSize">24dp</item>
    <item name="closeIconSize">18dp</item>

    <item name="chipStartPadding">4dp</item>
    <item name="iconStartPadding">0dp</item>
    <item name="iconEndPadding">0dp</item>
    <item name="textStartPadding">8dp</item>
    <item name="textEndPadding">6dp</item>
    <item name="closeIconStartPadding">2dp</item>
    <item name="closeIconEndPadding">2dp</item>
    <item name="chipEndPadding">6dp</item>

    <item name="shapeAppearance">?attr/shapeAppearanceSmallComponent</item>
    <item name="shapeAppearanceOverlay">@style/ShapeAppearanceOverlay.MaterialComponents.Chip</item>
  </style>
    <style name="Base.Widget.MaterialComponents.PopupMenu" parent="Widget.AppCompat.PopupMenu">
    <item name="overlapAnchor">false</item>
    <item name="android:dropDownVerticalOffset">1px</item>
  </style>
    <style name="Base.Widget.MaterialComponents.PopupMenu.ContextMenu" parent="Widget.AppCompat.PopupMenu"/>
    <style name="Base.Widget.MaterialComponents.PopupMenu.ListPopupWindow" parent="Widget.AppCompat.ListPopupWindow"/>
    <style name="Base.Widget.MaterialComponents.PopupMenu.Overflow" parent="Widget.AppCompat.PopupMenu.Overflow">
    <item name="android:dropDownVerticalOffset">1px</item>
  </style>
    <style name="Base.Widget.MaterialComponents.Slider" parent="android:Widget">
    <item name="haloColor">@color/material_slider_halo_color</item>
    <item name="haloRadius">@dimen/mtrl_slider_halo_radius</item>
    <item name="labelStyle">@style/Widget.MaterialComponents.Tooltip</item>
    <item name="thumbColor">@color/material_slider_thumb_color</item>
    <item name="thumbElevation">@dimen/mtrl_slider_thumb_elevation</item>
    <item name="thumbRadius">@dimen/mtrl_slider_thumb_radius</item>
    <item name="tickColorActive">@color/material_slider_active_tick_marks_color</item>
    <item name="tickColorInactive">@color/material_slider_inactive_tick_marks_color</item>
    <item name="trackColorActive">@color/material_slider_active_track_color</item>
    <item name="trackColorInactive">@color/material_slider_inactive_track_color</item>
    <item name="trackHeight">@dimen/mtrl_slider_track_height</item>
  </style>
    <style name="Base.Widget.MaterialComponents.TextInputEditText" parent="Widget.Design.TextInputEditText">
    <item name="enforceMaterialTheme">true</item>
    <item name="enforceTextAppearance">true</item>
    <item name="android:background">@null</item>
    <item name="android:paddingStart" ns2:ignore="NewApi">12dp</item>
    <item name="android:paddingEnd" ns2:ignore="NewApi">12dp</item>
    <item name="android:paddingLeft">12dp</item>
    <item name="android:paddingRight">12dp</item>
    <item name="android:paddingTop">16dp</item>
    <item name="android:paddingBottom">16dp</item>
    <item name="android:textAppearance">?attr/textAppearanceSubtitle1</item>
    <item name="textInputLayoutFocusedRectEnabled">true</item>
  </style>
    <style name="Base.Widget.MaterialComponents.TextInputLayout" parent="Widget.Design.TextInputLayout">
    <item name="enforceMaterialTheme">true</item>
    <item name="enforceTextAppearance">true</item>

    <item name="boxBackgroundMode">outline</item>
    <item name="boxBackgroundColor">@null</item>
    <item name="errorIconDrawable">@drawable/mtrl_ic_error</item>
    <item name="errorIconTint">@color/mtrl_error</item>
    <item name="endIconTint">@color/mtrl_outlined_icon_tint</item>
    <item name="startIconTint">@color/mtrl_outlined_icon_tint</item>
    <item name="boxCollapsedPaddingTop">0dp</item>
    <item name="boxStrokeColor">@color/mtrl_outlined_stroke_color</item>
    <item name="boxStrokeErrorColor">@color/mtrl_error</item>
    <item name="boxStrokeWidth">@dimen/mtrl_textinput_box_stroke_width_default</item>
    <item name="boxStrokeWidthFocused">@dimen/mtrl_textinput_box_stroke_width_focused</item>

    <item name="counterTextAppearance">?attr/textAppearanceCaption</item>
    <item name="counterOverflowTextAppearance">?attr/textAppearanceCaption</item>
    <item name="errorTextAppearance">?attr/textAppearanceCaption</item>
    <item name="helperTextTextAppearance">?attr/textAppearanceCaption</item>
    <item name="hintTextAppearance">?attr/textAppearanceCaption</item>
    <item name="placeholderTextAppearance">?attr/textAppearanceSubtitle1</item>
    <item name="prefixTextAppearance">?attr/textAppearanceSubtitle1</item>
    <item name="suffixTextAppearance">?attr/textAppearanceSubtitle1</item>

    <item name="counterTextColor">@color/mtrl_indicator_text_color</item>
    <item name="counterOverflowTextColor">@color/mtrl_error</item>
    <item name="errorTextColor">@color/mtrl_error</item>
    <item name="helperTextTextColor">@color/mtrl_indicator_text_color</item>
    
    <item name="hintTextColor">?attr/colorPrimary</item>
    <item name="placeholderTextColor">@color/mtrl_indicator_text_color</item>
    <item name="prefixTextColor">@color/mtrl_indicator_text_color</item>
    <item name="suffixTextColor">@color/mtrl_indicator_text_color</item>
    
    <item name="android:textColorHint">@color/mtrl_indicator_text_color</item>

    <item name="shapeAppearance">?attr/shapeAppearanceSmallComponent</item>
    <item name="shapeAppearanceOverlay">@null</item>
  </style>
    <style name="Base.Widget.MaterialComponents.TextView" parent="Widget.AppCompat.TextView"/>
    <style name="CardView" parent="Base.CardView">
    </style>
    <style name="CardView.Dark">
        <item name="cardBackgroundColor">@color/cardview_dark_background</item>
    </style>
    <style name="CardView.Light">
        <item name="cardBackgroundColor">@color/cardview_light_background</item>
    </style>
    <style name="EmptyTheme"/>
    <style name="FoodActivityTheme" parent="AppTheme">
    </style>
    <style name="MaterialAlertDialog.MaterialComponents" parent="AlertDialog.AppCompat">
    <item name="android:layout">@layout/mtrl_alert_dialog</item>
    <item name="listItemLayout">@layout/mtrl_alert_select_dialog_item</item>
    <item name="multiChoiceItemLayout">@layout/mtrl_alert_select_dialog_multichoice</item>
    <item name="singleChoiceItemLayout">@layout/mtrl_alert_select_dialog_singlechoice</item>

    <item name="backgroundInsetStart">@dimen/mtrl_alert_dialog_background_inset_start</item>
    <item name="backgroundInsetTop">@dimen/mtrl_alert_dialog_background_inset_top</item>
    <item name="backgroundInsetEnd">@dimen/mtrl_alert_dialog_background_inset_end</item>
    <item name="backgroundInsetBottom">@dimen/mtrl_alert_dialog_background_inset_bottom</item>
    <item name="shapeAppearance">?attr/shapeAppearanceMediumComponent</item>
  </style>
    <style name="MaterialAlertDialog.MaterialComponents.Body.Text" parent="TextAppearance.MaterialComponents.Body2">
    <item name="android:textAppearance">?attr/textAppearanceBody2</item>
    <item name="android:textColor">@color/material_on_surface_emphasis_medium</item>
  </style>
    <style name="MaterialAlertDialog.MaterialComponents.Picker.Date.Calendar" parent="android:Widget.DeviceDefault.DatePicker">
    <item name="backgroundInsetTop">@dimen/mtrl_alert_dialog_picker_background_inset</item>
    <item name="backgroundInsetBottom">@dimen/mtrl_alert_dialog_picker_background_inset</item>
    <item name="shapeAppearance">?attr/shapeAppearanceMediumComponent</item>
  </style>
    <style name="MaterialAlertDialog.MaterialComponents.Picker.Date.Spinner" parent="android:Widget.DeviceDefault.DatePicker">
    <item name="backgroundInsetTop">@dimen/mtrl_alert_dialog_picker_background_inset</item>
    <item name="backgroundInsetBottom">@dimen/mtrl_alert_dialog_picker_background_inset</item>
    <item name="shapeAppearance">?attr/shapeAppearanceMediumComponent</item>
  </style>
    <style name="MaterialAlertDialog.MaterialComponents.Title.Icon" parent="Base.MaterialAlertDialog.MaterialComponents.Title.Icon">
    <item name="android:layout_gravity">start|center_vertical</item>
    <item name="android:layout_marginEnd" ns2:ignore="NewApi">8dip</item>
    <item name="android:layout_marginRight">8dip</item>
  </style>
    <style name="MaterialAlertDialog.MaterialComponents.Title.Icon.CenterStacked" parent="Base.MaterialAlertDialog.MaterialComponents.Title.Icon">
    <item name="android:layout_gravity">center</item>
  </style>
    <style name="MaterialAlertDialog.MaterialComponents.Title.Panel" parent="Base.MaterialAlertDialog.MaterialComponents.Title.Panel">
    <item name="android:orientation">horizontal</item>
  </style>
    <style name="MaterialAlertDialog.MaterialComponents.Title.Panel.CenterStacked" parent="Base.MaterialAlertDialog.MaterialComponents.Title.Panel">
    <item name="android:orientation">vertical</item>
  </style>
    <style name="MaterialAlertDialog.MaterialComponents.Title.Text" parent="Base.MaterialAlertDialog.MaterialComponents.Title.Text">
    <item name="android:layout_gravity">start|center_vertical</item>
    <item name="android:textAlignment" ns2:ignore="NewApi">viewStart</item>
  </style>
    <style name="MaterialAlertDialog.MaterialComponents.Title.Text.CenterStacked" parent="Base.MaterialAlertDialog.MaterialComponents.Title.Text">
    <item name="android:layout_gravity">center</item>
    <item name="android:textAlignment" ns2:ignore="NewApi">center</item>
  </style>
    <style name="MyTheme" parent="AppTheme">

    </style>
    <style name="PersonImageStyle">
        <item name="android:layout_height">50dp</item>
        <item name="android:layout_width">50dp</item>
        <item name="android:layout_margin">5dp</item>
    </style>
    <style name="PersonLineStyle">
        <item name="background">@color/Lavender</item>
        <item name="android:layout_height">3dp</item>
        <item name="android:layout_width">match_parent</item>
    </style>
    <style name="PersonTvStyle">
        <item name="android:layout_height">match_parent</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:textColor">@color/Black</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textSize">18sp</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:layout_marginLeft">10dp</item>
        <item name="background">@color/Honeydew</item>
    </style>
    <style name="Platform.AppCompat" parent="android:Theme.Holo">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>

        <item name="android:buttonBarStyle">?attr/buttonBarStyle</item>
        <item name="android:buttonBarButtonStyle">?attr/buttonBarButtonStyle</item>
        <item name="android:borderlessButtonStyle">?attr/borderlessButtonStyle</item>

        
        <item name="android:colorForeground">@color/foreground_material_dark</item>
        <item name="android:colorForegroundInverse">@color/foreground_material_light</item>
        <item name="android:colorBackground">@color/background_material_dark</item>
        <item name="android:colorBackgroundCacheHint">@color/abc_background_cache_hint_selector_material_dark</item>
        <item name="android:disabledAlpha">@dimen/abc_disabled_alpha_material_dark</item>
        <item name="android:backgroundDimAmount">0.6</item>
        <item name="android:windowBackground">@color/background_material_dark</item>

        
        <item name="android:textColorPrimary">@color/abc_primary_text_material_dark</item>
        <item name="android:textColorPrimaryInverse">@color/abc_primary_text_material_light</item>
        <item name="android:textColorPrimaryDisableOnly">@color/abc_primary_text_disable_only_material_dark</item>
        <item name="android:textColorSecondary">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorSecondaryInverse">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorTertiary">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorTertiaryInverse">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorHint">@color/abc_hint_foreground_material_dark</item>
        <item name="android:textColorHintInverse">@color/abc_hint_foreground_material_light</item>
        <item name="android:textColorHighlight">@color/highlighted_text_material_dark</item>
        <item name="android:textColorHighlightInverse">@color/highlighted_text_material_light</item>
        <item name="android:textColorLink">?attr/colorAccent</item>
        <item name="android:textColorLinkInverse">?attr/colorAccent</item>
        <item name="android:textColorAlertDialogListItem">@color/abc_primary_text_material_dark</item>

        
        <item name="android:textAppearance">@style/TextAppearance.AppCompat</item>
        <item name="android:textAppearanceInverse">@style/TextAppearance.AppCompat.Inverse</item>
        <item name="android:textAppearanceLarge">@style/TextAppearance.AppCompat.Large</item>
        <item name="android:textAppearanceLargeInverse">@style/TextAppearance.AppCompat.Large.Inverse</item>
        <item name="android:textAppearanceMedium">@style/TextAppearance.AppCompat.Medium</item>
        <item name="android:textAppearanceMediumInverse">@style/TextAppearance.AppCompat.Medium.Inverse</item>
        <item name="android:textAppearanceSmall">@style/TextAppearance.AppCompat.Small</item>
        <item name="android:textAppearanceSmallInverse">@style/TextAppearance.AppCompat.Small.Inverse</item>

        <item name="android:listChoiceIndicatorSingle">@drawable/abc_btn_radio_material</item>
        <item name="listChoiceIndicatorSingleAnimated">@drawable/abc_btn_radio_material_anim</item>
        <item name="android:listChoiceIndicatorMultiple">@drawable/abc_btn_check_material</item>
        <item name="listChoiceIndicatorMultipleAnimated">@drawable/abc_btn_check_material_anim</item>

        
        <item name="android:textAppearanceListItem">@style/TextAppearance.AppCompat.Subhead</item>
        <item name="android:textAppearanceListItemSmall">@style/TextAppearance.AppCompat.Subhead</item>
        
        <item name="android:listPreferredItemHeight">@dimen/abc_list_item_height_material</item>
        <item name="android:listPreferredItemHeightSmall">@dimen/abc_list_item_height_small_material</item>
        <item name="android:listPreferredItemHeightLarge">@dimen/abc_list_item_height_large_material</item>
        <item name="android:listPreferredItemPaddingLeft">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="android:listPreferredItemPaddingRight">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="android:listPreferredItemPaddingStart">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="android:listPreferredItemPaddingEnd">@dimen/abc_list_item_padding_horizontal_material</item>

        <item name="android:actionModeCutDrawable">?actionModeCutDrawable</item>
        <item name="android:actionModeCopyDrawable">?actionModeCopyDrawable</item>
        <item name="android:actionModePasteDrawable">?actionModePasteDrawable</item>
        <item name="android:actionModeSelectAllDrawable">?actionModeSelectAllDrawable</item>

        <item name="android:textSelectHandle">@drawable/abc_text_select_handle_middle_mtrl_dark</item>
        <item name="android:textSelectHandleLeft">@drawable/abc_text_select_handle_left_mtrl_dark</item>
        <item name="android:textSelectHandleRight">@drawable/abc_text_select_handle_right_mtrl_dark</item>
    </style>
    <style name="Platform.AppCompat.Light" parent="android:Theme.Holo.Light">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>

        <item name="android:buttonBarStyle">?attr/buttonBarStyle</item>
        <item name="android:buttonBarButtonStyle">?attr/buttonBarButtonStyle</item>
        <item name="android:borderlessButtonStyle">?attr/borderlessButtonStyle</item>

        
        <item name="android:colorForeground">@color/foreground_material_light</item>
        <item name="android:colorForegroundInverse">@color/foreground_material_dark</item>
        <item name="android:colorBackground">@color/background_material_light</item>
        <item name="android:colorBackgroundCacheHint">@color/abc_background_cache_hint_selector_material_light</item>
        <item name="android:disabledAlpha">@dimen/abc_disabled_alpha_material_light</item>
        <item name="android:backgroundDimAmount">0.6</item>
        <item name="android:windowBackground">@color/background_material_light</item>

        
        <item name="android:textColorPrimary">@color/abc_primary_text_material_light</item>
        <item name="android:textColorPrimaryInverse">@color/abc_primary_text_material_dark</item>
        <item name="android:textColorSecondary">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorSecondaryInverse">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorTertiary">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorTertiaryInverse">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorPrimaryDisableOnly">@color/abc_primary_text_disable_only_material_light</item>
        <item name="android:textColorPrimaryInverseDisableOnly">@color/abc_primary_text_disable_only_material_dark</item>
        <item name="android:textColorHint">@color/abc_hint_foreground_material_light</item>
        <item name="android:textColorHintInverse">@color/abc_hint_foreground_material_dark</item>
        <item name="android:textColorHighlight">@color/highlighted_text_material_light</item>
        <item name="android:textColorHighlightInverse">@color/highlighted_text_material_dark</item>
        <item name="android:textColorLink">?attr/colorAccent</item>
        <item name="android:textColorLinkInverse">?attr/colorAccent</item>
        <item name="android:textColorAlertDialogListItem">@color/abc_primary_text_material_light</item>

        
        <item name="android:textAppearance">@style/TextAppearance.AppCompat</item>
        <item name="android:textAppearanceInverse">@style/TextAppearance.AppCompat.Inverse</item>
        <item name="android:textAppearanceLarge">@style/TextAppearance.AppCompat.Large</item>
        <item name="android:textAppearanceLargeInverse">@style/TextAppearance.AppCompat.Large.Inverse</item>
        <item name="android:textAppearanceMedium">@style/TextAppearance.AppCompat.Medium</item>
        <item name="android:textAppearanceMediumInverse">@style/TextAppearance.AppCompat.Medium.Inverse</item>
        <item name="android:textAppearanceSmall">@style/TextAppearance.AppCompat.Small</item>
        <item name="android:textAppearanceSmallInverse">@style/TextAppearance.AppCompat.Small.Inverse</item>

        <item name="android:listChoiceIndicatorSingle">@drawable/abc_btn_radio_material</item>
        <item name="listChoiceIndicatorSingleAnimated">@drawable/abc_btn_radio_material_anim</item>
        <item name="android:listChoiceIndicatorMultiple">@drawable/abc_btn_check_material</item>
        <item name="listChoiceIndicatorMultipleAnimated">@drawable/abc_btn_check_material_anim</item>

        
        <item name="android:textAppearanceListItem">@style/TextAppearance.AppCompat.Subhead</item>
        <item name="android:textAppearanceListItemSmall">@style/TextAppearance.AppCompat.Subhead</item>
        
        <item name="android:listPreferredItemHeight">@dimen/abc_list_item_height_material</item>
        <item name="android:listPreferredItemHeightSmall">@dimen/abc_list_item_height_small_material</item>
        <item name="android:listPreferredItemHeightLarge">@dimen/abc_list_item_height_large_material</item>
        <item name="android:listPreferredItemPaddingLeft">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="android:listPreferredItemPaddingRight">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="android:listPreferredItemPaddingStart">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="android:listPreferredItemPaddingEnd">@dimen/abc_list_item_padding_horizontal_material</item>

        <item name="android:actionModeCutDrawable">?actionModeCutDrawable</item>
        <item name="android:actionModeCopyDrawable">?actionModeCopyDrawable</item>
        <item name="android:actionModePasteDrawable">?actionModePasteDrawable</item>
        <item name="android:actionModeSelectAllDrawable">?actionModeSelectAllDrawable</item>

        <item name="android:textSelectHandle">@drawable/abc_text_select_handle_middle_mtrl_light</item>
        <item name="android:textSelectHandleLeft">@drawable/abc_text_select_handle_left_mtrl_light</item>
        <item name="android:textSelectHandleRight">@drawable/abc_text_select_handle_right_mtrl_light</item>
    </style>
    <style name="Platform.MaterialComponents" parent="Theme.AppCompat"/>
    <style name="Platform.MaterialComponents.Dialog" parent="Theme.AppCompat.Dialog"/>
    <style name="Platform.MaterialComponents.Light" parent="Theme.AppCompat.Light"/>
    <style name="Platform.MaterialComponents.Light.Dialog" parent="Theme.AppCompat.Light.Dialog"/>
    <style name="Platform.ThemeOverlay.AppCompat" parent=""/>
    <style name="Platform.ThemeOverlay.AppCompat.Dark">
        
        <item name="actionBarItemBackground">@drawable/abc_item_background_holo_dark</item>
        <item name="actionDropDownStyle">@style/Widget.AppCompat.Spinner.DropDown.ActionBar</item>
        <item name="selectableItemBackground">@drawable/abc_item_background_holo_dark</item>

        
        <item name="android:autoCompleteTextViewStyle">@style/Widget.AppCompat.AutoCompleteTextView</item>
        <item name="android:dropDownItemStyle">@style/Widget.AppCompat.DropDownItem.Spinner</item>
    </style>
    <style name="Platform.ThemeOverlay.AppCompat.Light">
        <item name="actionBarItemBackground">@drawable/abc_item_background_holo_light</item>
        <item name="actionDropDownStyle">@style/Widget.AppCompat.Light.Spinner.DropDown.ActionBar</item>
        <item name="selectableItemBackground">@drawable/abc_item_background_holo_light</item>

        
        <item name="android:autoCompleteTextViewStyle">@style/Widget.AppCompat.Light.AutoCompleteTextView</item>
        <item name="android:dropDownItemStyle">@style/Widget.AppCompat.DropDownItem.Spinner</item>
    </style>
    <style name="Platform.Widget.AppCompat.Spinner" parent="android:Widget.Holo.Spinner"/>
    <style name="RtlOverlay.DialogWindowTitle.AppCompat" parent="Base.DialogWindowTitle.AppCompat">
    </style>
    <style name="RtlOverlay.Widget.AppCompat.ActionBar.TitleItem" parent="android:Widget">
        <item name="android:layout_gravity">center_vertical|left</item>
        <item name="android:paddingRight">8dp</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.DialogTitle.Icon" parent="android:Widget">
        <item name="android:layout_marginRight">8dp</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.PopupMenuItem" parent="android:Widget">
        <item name="android:paddingRight">16dp</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.PopupMenuItem.InternalGroup" parent="android:Widget">
        <item name="android:layout_marginLeft">16dp</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.PopupMenuItem.Shortcut" parent="android:Widget">
        <item name="android:layout_marginLeft">16dp</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.PopupMenuItem.SubmenuArrow" parent="android:Widget">
        <item name="android:layout_marginLeft">8dp</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.PopupMenuItem.Text" parent="android:Widget">
        <item name="android:layout_alignParentLeft">true</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.PopupMenuItem.Title" parent="android:Widget">
        <item name="android:layout_marginLeft">16dp</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.Search.DropDown" parent="android:Widget">
        <item name="android:paddingLeft">@dimen/abc_dropdownitem_text_padding_left</item>
        <item name="android:paddingRight">4dp</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.Search.DropDown.Icon1" parent="android:Widget">
        <item name="android:layout_alignParentLeft">true</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.Search.DropDown.Icon2" parent="android:Widget">
        <item name="android:layout_toLeftOf">@id/edit_query</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.Search.DropDown.Query" parent="android:Widget">
        <item name="android:layout_alignParentRight">true</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.Search.DropDown.Text" parent="Base.Widget.AppCompat.DropDownItem.Spinner">
        <item name="android:layout_toLeftOf">@android:id/icon2</item>
        <item name="android:layout_toRightOf">@android:id/icon1</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.SearchView.MagIcon" parent="android:Widget">
        <item name="android:layout_marginLeft">@dimen/abc_dropdownitem_text_padding_left</item>
    </style>
    <style name="RtlUnderlay.Widget.AppCompat.ActionButton" parent="android:Widget">
        <item name="android:paddingLeft">12dp</item>
        <item name="android:paddingRight">12dp</item>
    </style>
    <style name="RtlUnderlay.Widget.AppCompat.ActionButton.Overflow" parent="Base.Widget.AppCompat.ActionButton">
        <item name="android:paddingLeft">@dimen/abc_action_bar_overflow_padding_start_material</item>
        <item name="android:paddingRight">@dimen/abc_action_bar_overflow_padding_end_material</item>
    </style>
    <style name="ShapeAppearance.MaterialComponents" parent="">
    <item name="cornerFamily">rounded</item>
  </style>
    <style name="ShapeAppearance.MaterialComponents.LargeComponent">
    <item name="cornerSize">@dimen/mtrl_shape_corner_size_large_component</item>
  </style>
    <style name="ShapeAppearance.MaterialComponents.MediumComponent">
    <item name="cornerSize">@dimen/mtrl_shape_corner_size_medium_component</item>
  </style>
    <style name="ShapeAppearance.MaterialComponents.SmallComponent">
    <item name="cornerSize">@dimen/mtrl_shape_corner_size_small_component</item>
  </style>
    <style name="ShapeAppearance.MaterialComponents.Test" parent="">
    <item name="cornerFamily">rounded</item>
    <item name="cornerSize">10px</item>
  </style>
    <style name="ShapeAppearance.MaterialComponents.Tooltip" parent="">
    <item name="cornerFamily">rounded</item>
    <item name="cornerSize">4dp</item>
  </style>
    <style name="ShapeAppearanceOverlay" parent=""/>
    <style name="ShapeAppearanceOverlay.BottomLeftDifferentCornerSize">
    <item name="cornerSizeBottomLeft">20px</item>
  </style>
    <style name="ShapeAppearanceOverlay.BottomRightCut">
    <item name="cornerFamilyBottomRight">cut</item>
  </style>
    <style name="ShapeAppearanceOverlay.Cut">
    <item name="cornerFamily">cut</item>
  </style>
    <style name="ShapeAppearanceOverlay.DifferentCornerSize">
    <item name="cornerSize">20px</item>
  </style>
    <style name="ShapeAppearanceOverlay.MaterialComponents.BottomSheet" parent="">
    <item name="cornerSizeBottomRight">0dp</item>
    <item name="cornerSizeBottomLeft">0dp</item>
  </style>
    <style name="ShapeAppearanceOverlay.MaterialComponents.Chip" parent="">
    <item name="cornerSize">50%</item>
  </style>
    <style name="ShapeAppearanceOverlay.MaterialComponents.ExtendedFloatingActionButton" parent="">
    <item name="cornerSize">@null</item>
  </style>
    <style name="ShapeAppearanceOverlay.MaterialComponents.FloatingActionButton" parent="">
    <item name="cornerSize">50%</item>
  </style>
    <style name="ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Day" parent="">
    <item name="cornerSize">@dimen/mtrl_calendar_day_corner</item>
  </style>
    <style name="ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Window.Fullscreen" parent="">
    <item name="cornerSize">0dp</item>
  </style>
    <style name="ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Year" parent="">
    <item name="cornerSize">@dimen/mtrl_calendar_year_corner</item>
  </style>
    <style name="ShapeAppearanceOverlay.MaterialComponents.TextInputLayout.FilledBox" parent="">
    <item name="cornerSizeBottomLeft">@dimen/mtrl_textinput_box_corner_radius_small</item>
    <item name="cornerSizeBottomRight">@dimen/mtrl_textinput_box_corner_radius_small</item>
  </style>
    <style name="ShapeAppearanceOverlay.TopLeftCut">
    <item name="cornerFamilyTopLeft">cut</item>
  </style>
    <style name="ShapeAppearanceOverlay.TopRightDifferentCornerSize">
    <item name="cornerSizeTopRight">20px</item>
  </style>
    <style name="Test.ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Day" parent="">
    <item name="cornerFamily">rounded</item>
    <item name="cornerSize">@dimen/test_mtrl_calendar_day_cornerSize</item>
  </style>
    <style name="Test.Theme.MaterialComponents.MaterialCalendar" parent="Theme.MaterialComponents.Light">
    <item name="materialCalendarStyle">@style/Test.Widget.MaterialComponents.MaterialCalendar</item>
  </style>
    <style name="Test.Widget.MaterialComponents.MaterialCalendar" parent="">
    <item name="dayStyle">@style/Test.Widget.MaterialComponents.MaterialCalendar.Day</item>
    <item name="dayInvalidStyle">@style/Test.Widget.MaterialComponents.MaterialCalendar.Day</item>
    <item name="daySelectedStyle">@style/Test.Widget.MaterialComponents.MaterialCalendar.Day.Selected</item>
    <item name="dayTodayStyle">@style/Test.Widget.MaterialComponents.MaterialCalendar.Day</item>
    <item name="yearStyle">@style/Test.Widget.MaterialComponents.MaterialCalendar.Day</item>
    <item name="yearSelectedStyle">@style/Test.Widget.MaterialComponents.MaterialCalendar.Day.Selected</item>
    <item name="yearTodayStyle">@style/Test.Widget.MaterialComponents.MaterialCalendar.Day</item>
    <item name="rangeFillColor">@color/test_mtrl_calendar_day</item>
  </style>
    <style name="Test.Widget.MaterialComponents.MaterialCalendar.Day" parent="">
    <item name="itemFillColor">@color/test_mtrl_calendar_day</item>
    <item name="itemTextColor">@color/test_mtrl_calendar_day</item>
    <item name="itemStrokeColor">@color/test_mtrl_calendar_day</item>
    <item name="itemStrokeWidth">0dp</item>
    <item name="itemShapeAppearance">@style/Test.ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Day</item>
    <item name="itemShapeAppearanceOverlay">@style/Test.ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Day</item>
  </style>
    <style name="Test.Widget.MaterialComponents.MaterialCalendar.Day.Selected" parent="">
    <item name="itemFillColor">@color/test_mtrl_calendar_day_selected</item>
    <item name="itemTextColor">@color/test_mtrl_calendar_day_selected</item>
    <item name="itemStrokeColor">@color/test_mtrl_calendar_day</item>
    <item name="itemStrokeWidth">0dp</item>
    <item name="itemShapeAppearance">@style/Test.ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Day</item>
    <item name="itemShapeAppearanceOverlay">@style/Test.ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Day</item>
  </style>
    <style name="TestStyleWithLineHeight" parent="TestStyleWithoutLineHeight">
    <item name="lineHeight">@dimen/material_text_view_test_line_height</item>
  </style>
    <style name="TestStyleWithLineHeightAppearance">
    <item name="android:textAppearance">@style/TestStyleWithLineHeight</item>
  </style>
    <style name="TestStyleWithThemeLineHeightAttribute" parent="TestStyleWithoutLineHeight">
    <item name="lineHeight">?attr/themeLineHeight</item>
  </style>
    <style name="TestStyleWithoutLineHeight" parent="TextAppearance.AppCompat.Title">
    <item name="android:textSize">20sp</item>
  </style>
    <style name="TestThemeWithLineHeight" parent="Theme.AppCompat.Light">
    <item name="themeLineHeight">@dimen/material_text_view_test_line_height</item>
  </style>
    <style name="TestThemeWithLineHeightDisabled" parent="TestThemeWithLineHeight">
    <item name="textAppearanceLineHeightEnabled">false</item>
  </style>
    <style name="TextAppearance.AppCompat" parent="Base.TextAppearance.AppCompat"/>
    <style name="TextAppearance.AppCompat.Body1" parent="Base.TextAppearance.AppCompat.Body1"/>
    <style name="TextAppearance.AppCompat.Body2" parent="Base.TextAppearance.AppCompat.Body2"/>
    <style name="TextAppearance.AppCompat.Button" parent="Base.TextAppearance.AppCompat.Button"/>
    <style name="TextAppearance.AppCompat.Caption" parent="Base.TextAppearance.AppCompat.Caption"/>
    <style name="TextAppearance.AppCompat.Display1" parent="Base.TextAppearance.AppCompat.Display1"/>
    <style name="TextAppearance.AppCompat.Display2" parent="Base.TextAppearance.AppCompat.Display2"/>
    <style name="TextAppearance.AppCompat.Display3" parent="Base.TextAppearance.AppCompat.Display3"/>
    <style name="TextAppearance.AppCompat.Display4" parent="Base.TextAppearance.AppCompat.Display4"/>
    <style name="TextAppearance.AppCompat.Headline" parent="Base.TextAppearance.AppCompat.Headline"/>
    <style name="TextAppearance.AppCompat.Inverse" parent="Base.TextAppearance.AppCompat.Inverse"/>
    <style name="TextAppearance.AppCompat.Large" parent="Base.TextAppearance.AppCompat.Large"/>
    <style name="TextAppearance.AppCompat.Large.Inverse" parent="Base.TextAppearance.AppCompat.Large.Inverse"/>
    <style name="TextAppearance.AppCompat.Light.SearchResult.Subtitle" parent="TextAppearance.AppCompat.SearchResult.Subtitle"/>
    <style name="TextAppearance.AppCompat.Light.SearchResult.Title" parent="TextAppearance.AppCompat.SearchResult.Title"/>
    <style name="TextAppearance.AppCompat.Light.Widget.PopupMenu.Large" parent="TextAppearance.AppCompat.Widget.PopupMenu.Large"/>
    <style name="TextAppearance.AppCompat.Light.Widget.PopupMenu.Small" parent="TextAppearance.AppCompat.Widget.PopupMenu.Small"/>
    <style name="TextAppearance.AppCompat.Medium" parent="Base.TextAppearance.AppCompat.Medium"/>
    <style name="TextAppearance.AppCompat.Medium.Inverse" parent="Base.TextAppearance.AppCompat.Medium.Inverse"/>
    <style name="TextAppearance.AppCompat.Menu" parent="Base.TextAppearance.AppCompat.Menu"/>
    <style name="TextAppearance.AppCompat.SearchResult.Subtitle" parent="Base.TextAppearance.AppCompat.SearchResult.Subtitle">
    </style>
    <style name="TextAppearance.AppCompat.SearchResult.Title" parent="Base.TextAppearance.AppCompat.SearchResult.Title">
    </style>
    <style name="TextAppearance.AppCompat.Small" parent="Base.TextAppearance.AppCompat.Small"/>
    <style name="TextAppearance.AppCompat.Small.Inverse" parent="Base.TextAppearance.AppCompat.Small.Inverse"/>
    <style name="TextAppearance.AppCompat.Subhead" parent="Base.TextAppearance.AppCompat.Subhead"/>
    <style name="TextAppearance.AppCompat.Subhead.Inverse" parent="Base.TextAppearance.AppCompat.Subhead.Inverse"/>
    <style name="TextAppearance.AppCompat.Title" parent="Base.TextAppearance.AppCompat.Title"/>
    <style name="TextAppearance.AppCompat.Title.Inverse" parent="Base.TextAppearance.AppCompat.Title.Inverse"/>
    <style name="TextAppearance.AppCompat.Tooltip" parent="Base.TextAppearance.AppCompat.Tooltip"/>
    <style name="TextAppearance.AppCompat.Widget.ActionBar.Menu" parent="Base.TextAppearance.AppCompat.Widget.ActionBar.Menu">
    </style>
    <style name="TextAppearance.AppCompat.Widget.ActionBar.Subtitle" parent="Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle"/>
    <style name="TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse" parent="Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse">
    </style>
    <style name="TextAppearance.AppCompat.Widget.ActionBar.Title" parent="Base.TextAppearance.AppCompat.Widget.ActionBar.Title"/>
    <style name="TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse" parent="Base.TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse">
    </style>
    <style name="TextAppearance.AppCompat.Widget.ActionMode.Subtitle" parent="Base.TextAppearance.AppCompat.Widget.ActionMode.Subtitle">
    </style>
    <style name="TextAppearance.AppCompat.Widget.ActionMode.Subtitle.Inverse" parent="TextAppearance.AppCompat.Widget.ActionMode.Subtitle"/>
    <style name="TextAppearance.AppCompat.Widget.ActionMode.Title" parent="Base.TextAppearance.AppCompat.Widget.ActionMode.Title">
    </style>
    <style name="TextAppearance.AppCompat.Widget.ActionMode.Title.Inverse" parent="TextAppearance.AppCompat.Widget.ActionMode.Title"/>
    <style name="TextAppearance.AppCompat.Widget.Button" parent="Base.TextAppearance.AppCompat.Widget.Button"/>
    <style name="TextAppearance.AppCompat.Widget.Button.Borderless.Colored" parent="Base.TextAppearance.AppCompat.Widget.Button.Borderless.Colored"/>
    <style name="TextAppearance.AppCompat.Widget.Button.Colored" parent="Base.TextAppearance.AppCompat.Widget.Button.Colored"/>
    <style name="TextAppearance.AppCompat.Widget.Button.Inverse" parent="Base.TextAppearance.AppCompat.Widget.Button.Inverse"/>
    <style name="TextAppearance.AppCompat.Widget.DropDownItem" parent="Base.TextAppearance.AppCompat.Widget.DropDownItem">
    </style>
    <style name="TextAppearance.AppCompat.Widget.PopupMenu.Header" parent="Base.TextAppearance.AppCompat.Widget.PopupMenu.Header"/>
    <style name="TextAppearance.AppCompat.Widget.PopupMenu.Large" parent="Base.TextAppearance.AppCompat.Widget.PopupMenu.Large"/>
    <style name="TextAppearance.AppCompat.Widget.PopupMenu.Small" parent="Base.TextAppearance.AppCompat.Widget.PopupMenu.Small"/>
    <style name="TextAppearance.AppCompat.Widget.Switch" parent="Base.TextAppearance.AppCompat.Widget.Switch"/>
    <style name="TextAppearance.AppCompat.Widget.TextView.SpinnerItem" parent="Base.TextAppearance.AppCompat.Widget.TextView.SpinnerItem"/>
    <style name="TextAppearance.Compat.Notification" parent="@android:style/TextAppearance.StatusBar.EventContent"/>
    <style name="TextAppearance.Compat.Notification.Info">
        <item name="android:textSize">12sp</item>
        <item name="android:textColor">?android:attr/textColorSecondary</item>
    </style>
    <style name="TextAppearance.Compat.Notification.Line2" parent="TextAppearance.Compat.Notification.Info"/>
    <style name="TextAppearance.Compat.Notification.Time">
        <item name="android:textSize">12sp</item>
        <item name="android:textColor">?android:attr/textColorSecondary</item>
    </style>
    <style name="TextAppearance.Compat.Notification.Title" parent="@android:style/TextAppearance.StatusBar.EventContent.Title"/>
    <style name="TextAppearance.Design.CollapsingToolbar.Expanded" parent="TextAppearance.AppCompat.Display1">
    <item name="android:textColor">?android:attr/textColorPrimary</item>
  </style>
    <style name="TextAppearance.Design.Counter" parent="TextAppearance.AppCompat.Caption"/>
    <style name="TextAppearance.Design.Counter.Overflow" parent="TextAppearance.AppCompat.Caption">
    <item name="android:textColor">@color/design_error</item>
  </style>
    <style name="TextAppearance.Design.Error" parent="TextAppearance.AppCompat.Caption">
    <item name="android:textColor">@color/design_error</item>
  </style>
    <style name="TextAppearance.Design.HelperText" parent="TextAppearance.AppCompat.Caption"/>
    <style name="TextAppearance.Design.Hint" parent="TextAppearance.AppCompat.Caption">
    <item name="android:textColor">?attr/colorControlActivated</item>
  </style>
    <style name="TextAppearance.Design.Placeholder" parent="TextAppearance.AppCompat.Body1"/>
    <style name="TextAppearance.Design.Prefix" parent="TextAppearance.AppCompat.Body1"/>
    <style name="TextAppearance.Design.Snackbar.Message" parent="android:TextAppearance">
    <item name="android:textSize">@dimen/design_snackbar_text_size</item>
    <item name="android:textColor">?android:textColorPrimary</item>
  </style>
    <style name="TextAppearance.Design.Suffix" parent="TextAppearance.AppCompat.Body1"/>
    <style name="TextAppearance.Design.Tab" parent="TextAppearance.AppCompat.Button">
    <item name="android:textSize">@dimen/design_tab_text_size</item>
    <item name="android:textColor">@color/mtrl_tabs_legacy_text_color_selector</item>
    <item name="textAllCaps">true</item>
  </style>
    <style name="TextAppearance.MaterialComponents.Badge" parent="Base.TextAppearance.MaterialComponents.Badge"/>
    <style name="TextAppearance.MaterialComponents.Body1" parent="TextAppearance.AppCompat.Body2">
    <item name="fontFamily">sans-serif</item>
    <item name="android:fontFamily">sans-serif</item>
    <item name="android:textStyle">normal</item>
    <item name="android:textAllCaps">false</item>
    <item name="android:textSize">16sp</item>
    <item name="android:letterSpacing">0.03125</item>
  </style>
    <style name="TextAppearance.MaterialComponents.Body2" parent="TextAppearance.AppCompat.Body1">
    <item name="fontFamily">sans-serif</item>
    <item name="android:fontFamily">sans-serif</item>
    <item name="android:textStyle">normal</item>
    <item name="android:textAllCaps">false</item>
    <item name="android:textSize">14sp</item>
    <item name="android:letterSpacing">0.0178571429</item>
  </style>
    <style name="TextAppearance.MaterialComponents.Button" parent="Base.TextAppearance.MaterialComponents.Button"/>
    <style name="TextAppearance.MaterialComponents.Caption" parent="TextAppearance.AppCompat.Caption">
    <item name="fontFamily">sans-serif</item>
    <item name="android:fontFamily">sans-serif</item>
    <item name="android:textStyle">normal</item>
    <item name="android:textAllCaps">false</item>
    <item name="android:textSize">12sp</item>
    <item name="android:letterSpacing">0.0333333333</item>
  </style>
    <style name="TextAppearance.MaterialComponents.Chip" parent="TextAppearance.AppCompat">
    <item name="android:textColor">@color/mtrl_chip_text_color</item>
    <item name="android:textSize">@dimen/mtrl_chip_text_size</item>
  </style>
    <style name="TextAppearance.MaterialComponents.Headline1" parent="TextAppearance.AppCompat.Display4">
    <item name="fontFamily">sans-serif-light</item>
    <item name="android:fontFamily">sans-serif-light</item>
    <item name="android:textStyle">normal</item>
    <item name="android:textAllCaps">false</item>
    <item name="android:textSize">96sp</item>
    <item name="android:letterSpacing">-0.015625</item>
  </style>
    <style name="TextAppearance.MaterialComponents.Headline2" parent="TextAppearance.AppCompat.Display3">
    <item name="fontFamily">sans-serif-light</item>
    <item name="android:fontFamily">sans-serif-light</item>
    <item name="android:textStyle">normal</item>
    <item name="android:textAllCaps">false</item>
    <item name="android:textSize">60sp</item>
    <item name="android:letterSpacing">-0.00833333333</item>
  </style>
    <style name="TextAppearance.MaterialComponents.Headline3" parent="TextAppearance.AppCompat.Display2">
    <item name="fontFamily">sans-serif</item>
    <item name="android:fontFamily">sans-serif</item>
    <item name="android:textStyle">normal</item>
    <item name="android:textAllCaps">false</item>
    <item name="android:textSize">48sp</item>
    <item name="android:letterSpacing">0</item>
  </style>
    <style name="TextAppearance.MaterialComponents.Headline4" parent="TextAppearance.AppCompat.Display1">
    <item name="fontFamily">sans-serif</item>
    <item name="android:fontFamily">sans-serif</item>
    <item name="android:textStyle">normal</item>
    <item name="android:textAllCaps">false</item>
    <item name="android:textSize">34sp</item>
    <item name="android:letterSpacing">0.00735294118</item>
  </style>
    <style name="TextAppearance.MaterialComponents.Headline5" parent="TextAppearance.AppCompat.Headline">
    <item name="fontFamily">sans-serif</item>
    <item name="android:fontFamily">sans-serif</item>
    <item name="android:textStyle">normal</item>
    <item name="android:textAllCaps">false</item>
    <item name="android:textSize">24sp</item>
    <item name="android:letterSpacing">0</item>
  </style>
    <style name="TextAppearance.MaterialComponents.Headline6" parent="Base.TextAppearance.MaterialComponents.Headline6"/>
    <style name="TextAppearance.MaterialComponents.Overline" parent="TextAppearance.AppCompat">
    <item name="fontFamily">sans-serif</item>
    <item name="android:fontFamily">sans-serif</item>
    <item name="android:textStyle">normal</item>
    <item name="android:textAllCaps">true</item>
    <item name="android:textSize">10sp</item>
    <item name="android:letterSpacing">0.166666667</item>
  </style>
    <style name="TextAppearance.MaterialComponents.Subtitle1" parent="TextAppearance.AppCompat.Subhead">
    <item name="fontFamily">sans-serif</item>
    <item name="android:fontFamily">sans-serif</item>
    <item name="android:textStyle">normal</item>
    <item name="android:textAllCaps">false</item>
    <item name="android:textSize">16sp</item>
    <item name="android:letterSpacing">0.009375</item>
  </style>
    <style name="TextAppearance.MaterialComponents.Subtitle2" parent="Base.TextAppearance.MaterialComponents.Subtitle2"/>
    <style name="TextAppearance.MaterialComponents.Tooltip" parent="TextAppearance.MaterialComponents.Body2">
    <item name="android:textColor">?attr/colorOnPrimary</item>
  </style>
    <style name="TextAppearance.Widget.AppCompat.ExpandedMenu.Item" parent="Base.TextAppearance.Widget.AppCompat.ExpandedMenu.Item">
    </style>
    <style name="TextAppearance.Widget.AppCompat.Toolbar.Subtitle" parent="Base.TextAppearance.Widget.AppCompat.Toolbar.Subtitle">
    </style>
    <style name="TextAppearance.Widget.AppCompat.Toolbar.Title" parent="Base.TextAppearance.Widget.AppCompat.Toolbar.Title">
    </style>
    <style name="Theme.AppCompat" parent="Base.Theme.AppCompat"/>
    <style name="Theme.AppCompat.CompactMenu" parent="Base.Theme.AppCompat.CompactMenu"/>
    <style name="Theme.AppCompat.DayNight" parent="Theme.AppCompat.Light"/>
    <style name="Theme.AppCompat.DayNight.DarkActionBar" parent="Theme.AppCompat.Light.DarkActionBar"/>
    <style name="Theme.AppCompat.DayNight.Dialog" parent="Theme.AppCompat.Light.Dialog"/>
    <style name="Theme.AppCompat.DayNight.Dialog.Alert" parent="Theme.AppCompat.Light.Dialog.Alert"/>
    <style name="Theme.AppCompat.DayNight.Dialog.MinWidth" parent="Theme.AppCompat.Light.Dialog.MinWidth"/>
    <style name="Theme.AppCompat.DayNight.DialogWhenLarge" parent="Theme.AppCompat.Light.DialogWhenLarge"/>
    <style name="Theme.AppCompat.DayNight.NoActionBar" parent="Theme.AppCompat.Light.NoActionBar"/>
    <style name="Theme.AppCompat.Dialog" parent="Base.Theme.AppCompat.Dialog"/>
    <style name="Theme.AppCompat.Dialog.Alert" parent="Base.Theme.AppCompat.Dialog.Alert"/>
    <style name="Theme.AppCompat.Dialog.MinWidth" parent="Base.Theme.AppCompat.Dialog.MinWidth"/>
    <style name="Theme.AppCompat.DialogWhenLarge" parent="Base.Theme.AppCompat.DialogWhenLarge">
    </style>
    <style name="Theme.AppCompat.Empty" parent=""/>
    <style name="Theme.AppCompat.Light" parent="Base.Theme.AppCompat.Light"/>
    <style name="Theme.AppCompat.Light.DarkActionBar" parent="Base.Theme.AppCompat.Light.DarkActionBar"/>
    <style name="Theme.AppCompat.Light.Dialog" parent="Base.Theme.AppCompat.Light.Dialog"/>
    <style name="Theme.AppCompat.Light.Dialog.Alert" parent="Base.Theme.AppCompat.Light.Dialog.Alert"/>
    <style name="Theme.AppCompat.Light.Dialog.MinWidth" parent="Base.Theme.AppCompat.Light.Dialog.MinWidth"/>
    <style name="Theme.AppCompat.Light.DialogWhenLarge" parent="Base.Theme.AppCompat.Light.DialogWhenLarge">
    </style>
    <style name="Theme.AppCompat.Light.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="Theme.AppCompat.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="Theme.Design" parent="Theme.AppCompat">
  </style>
    <style name="Theme.Design.BottomSheetDialog" parent="Theme.AppCompat.Dialog">
    <item name="android:windowBackground">@android:color/transparent</item>
    <item name="android:windowAnimationStyle">@style/Animation.Design.BottomSheetDialog</item>
    <item name="bottomSheetStyle">@style/Widget.Design.BottomSheet.Modal</item>
  </style>
    <style name="Theme.Design.Light" parent="Theme.AppCompat.Light">
  </style>
    <style name="Theme.Design.Light.BottomSheetDialog" parent="Theme.AppCompat.Light.Dialog">
    <item name="android:windowBackground">@android:color/transparent</item>
    <item name="android:windowAnimationStyle">@style/Animation.Design.BottomSheetDialog</item>
    <item name="bottomSheetStyle">@style/Widget.Design.BottomSheet.Modal</item>
  </style>
    <style name="Theme.Design.Light.NoActionBar">
    <item name="windowActionBar">false</item>
    <item name="windowNoTitle">true</item>
  </style>
    <style name="Theme.Design.NoActionBar">
    <item name="windowActionBar">false</item>
    <item name="windowNoTitle">true</item>
  </style>
    <style name="Theme.MaterialComponents" parent="Base.Theme.MaterialComponents"/>
    <style name="Theme.MaterialComponents.BottomSheetDialog" parent="Theme.MaterialComponents.Dialog">
    <item name="android:windowBackground">@android:color/transparent</item>
    <item name="android:windowAnimationStyle">@style/Animation.MaterialComponents.BottomSheetDialog</item>
    <item name="bottomSheetStyle">@style/Widget.MaterialComponents.BottomSheet.Modal</item>
  </style>
    <style name="Theme.MaterialComponents.Bridge" parent="Base.Theme.MaterialComponents.Bridge"/>
    <style name="Theme.MaterialComponents.CompactMenu" parent="Base.Theme.MaterialComponents.CompactMenu"/>
    <style name="Theme.MaterialComponents.DayNight" parent="Theme.MaterialComponents.Light"/>
    <style name="Theme.MaterialComponents.DayNight.BottomSheetDialog" parent="Theme.MaterialComponents.Light.BottomSheetDialog"/>
    <style name="Theme.MaterialComponents.DayNight.Bridge" parent="Theme.MaterialComponents.Light.Bridge"/>
    <style name="Theme.MaterialComponents.DayNight.DarkActionBar" parent="Theme.MaterialComponents.Light.DarkActionBar"/>
    <style name="Theme.MaterialComponents.DayNight.DarkActionBar.Bridge" parent="Theme.MaterialComponents.Light.DarkActionBar.Bridge"/>
    <style name="Theme.MaterialComponents.DayNight.Dialog" parent="Theme.MaterialComponents.Light.Dialog"/>
    <style name="Theme.MaterialComponents.DayNight.Dialog.Alert" parent="Theme.MaterialComponents.Light.Dialog.Alert"/>
    <style name="Theme.MaterialComponents.DayNight.Dialog.Alert.Bridge" parent="Theme.MaterialComponents.Light.Dialog.Alert.Bridge"/>
    <style name="Theme.MaterialComponents.DayNight.Dialog.Bridge" parent="Theme.MaterialComponents.Light.Dialog.Bridge"/>
    <style name="Theme.MaterialComponents.DayNight.Dialog.FixedSize" parent="Theme.MaterialComponents.Light.Dialog.FixedSize"/>
    <style name="Theme.MaterialComponents.DayNight.Dialog.FixedSize.Bridge" parent="Theme.MaterialComponents.Light.Dialog.FixedSize.Bridge"/>
    <style name="Theme.MaterialComponents.DayNight.Dialog.MinWidth" parent="Theme.MaterialComponents.Light.Dialog.MinWidth"/>
    <style name="Theme.MaterialComponents.DayNight.Dialog.MinWidth.Bridge" parent="Theme.MaterialComponents.Light.Dialog.MinWidth.Bridge"/>
    <style name="Theme.MaterialComponents.DayNight.DialogWhenLarge" parent="Theme.MaterialComponents.Light.DialogWhenLarge"/>
    <style name="Theme.MaterialComponents.DayNight.NoActionBar" parent="Theme.MaterialComponents.Light.NoActionBar"/>
    <style name="Theme.MaterialComponents.DayNight.NoActionBar.Bridge" parent="Theme.MaterialComponents.Light.NoActionBar.Bridge"/>
    <style name="Theme.MaterialComponents.Dialog" parent="Base.Theme.MaterialComponents.Dialog"/>
    <style name="Theme.MaterialComponents.Dialog.Alert" parent="Base.Theme.MaterialComponents.Dialog.Alert"/>
    <style name="Theme.MaterialComponents.Dialog.Alert.Bridge" parent="Base.Theme.MaterialComponents.Dialog.Bridge">
    <item name="android:windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
    <item name="android:windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
  </style>
    <style name="Theme.MaterialComponents.Dialog.Bridge" parent="Base.Theme.MaterialComponents.Dialog.Bridge"/>
    <style name="Theme.MaterialComponents.Dialog.FixedSize" parent="Base.Theme.MaterialComponents.Dialog.FixedSize"/>
    <style name="Theme.MaterialComponents.Dialog.FixedSize.Bridge" parent="Base.Theme.MaterialComponents.Dialog.Bridge">
    <item name="windowFixedWidthMajor">@dimen/abc_dialog_fixed_width_major</item>
    <item name="windowFixedWidthMinor">@dimen/abc_dialog_fixed_width_minor</item>
    <item name="windowFixedHeightMajor">@dimen/abc_dialog_fixed_height_major</item>
    <item name="windowFixedHeightMinor">@dimen/abc_dialog_fixed_height_minor</item>
  </style>
    <style name="Theme.MaterialComponents.Dialog.MinWidth" parent="Base.Theme.MaterialComponents.Dialog.MinWidth"/>
    <style name="Theme.MaterialComponents.Dialog.MinWidth.Bridge" parent="Base.Theme.MaterialComponents.Dialog.Bridge">
    <item name="android:windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
    <item name="android:windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
  </style>
    <style name="Theme.MaterialComponents.DialogWhenLarge" parent="Base.Theme.MaterialComponents.DialogWhenLarge">
  </style>
    <style name="Theme.MaterialComponents.Light" parent="Base.Theme.MaterialComponents.Light"/>
    <style name="Theme.MaterialComponents.Light.BarSize">
    <item name="actionBarSize">@dimen/action_bar_size</item>
  </style>
    <style name="Theme.MaterialComponents.Light.BottomSheetDialog" parent="Theme.MaterialComponents.Light.Dialog">
    <item name="android:windowBackground">@android:color/transparent</item>
    <item name="android:windowAnimationStyle">@style/Animation.MaterialComponents.BottomSheetDialog</item>
    <item name="bottomSheetStyle">@style/Widget.MaterialComponents.BottomSheet.Modal</item>
  </style>
    <style name="Theme.MaterialComponents.Light.Bridge" parent="Base.Theme.MaterialComponents.Light.Bridge"/>
    <style name="Theme.MaterialComponents.Light.DarkActionBar" parent="Base.Theme.MaterialComponents.Light.DarkActionBar"/>
    <style name="Theme.MaterialComponents.Light.DarkActionBar.Bridge" parent="Base.Theme.MaterialComponents.Light.DarkActionBar.Bridge"/>
    <style name="Theme.MaterialComponents.Light.Dialog" parent="Base.Theme.MaterialComponents.Light.Dialog"/>
    <style name="Theme.MaterialComponents.Light.Dialog.Alert" parent="Base.Theme.MaterialComponents.Light.Dialog.Alert"/>
    <style name="Theme.MaterialComponents.Light.Dialog.Alert.Bridge" parent="Base.Theme.MaterialComponents.Light.Dialog.Bridge">
    <item name="android:windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
    <item name="android:windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
  </style>
    <style name="Theme.MaterialComponents.Light.Dialog.Bridge" parent="Base.Theme.MaterialComponents.Light.Dialog.Bridge"/>
    <style name="Theme.MaterialComponents.Light.Dialog.FixedSize" parent="Base.Theme.MaterialComponents.Light.Dialog.FixedSize"/>
    <style name="Theme.MaterialComponents.Light.Dialog.FixedSize.Bridge" parent="Base.Theme.MaterialComponents.Light.Dialog.Bridge">
    <item name="windowFixedWidthMajor">@dimen/abc_dialog_fixed_width_major</item>
    <item name="windowFixedWidthMinor">@dimen/abc_dialog_fixed_width_minor</item>
    <item name="windowFixedHeightMajor">@dimen/abc_dialog_fixed_height_major</item>
    <item name="windowFixedHeightMinor">@dimen/abc_dialog_fixed_height_minor</item>
  </style>
    <style name="Theme.MaterialComponents.Light.Dialog.MinWidth" parent="Base.Theme.MaterialComponents.Light.Dialog.MinWidth"/>
    <style name="Theme.MaterialComponents.Light.Dialog.MinWidth.Bridge" parent="Base.Theme.MaterialComponents.Light.Dialog.Bridge">
    <item name="android:windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
    <item name="android:windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
  </style>
    <style name="Theme.MaterialComponents.Light.DialogWhenLarge" parent="Base.Theme.MaterialComponents.Light.DialogWhenLarge">
  </style>
    <style name="Theme.MaterialComponents.Light.LargeTouch">
    <item name="minTouchTargetSize">@dimen/mtrl_large_touch_target</item>
  </style>
    <style name="Theme.MaterialComponents.Light.NoActionBar">
    <item name="windowActionBar">false</item>
    <item name="windowNoTitle">true</item>
  </style>
    <style name="Theme.MaterialComponents.Light.NoActionBar.Bridge" parent="Theme.MaterialComponents.Light.Bridge">
    <item name="windowActionBar">false</item>
    <item name="windowNoTitle">true</item>
  </style>
    <style name="Theme.MaterialComponents.NoActionBar">
    <item name="windowActionBar">false</item>
    <item name="windowNoTitle">true</item>
  </style>
    <style name="Theme.MaterialComponents.NoActionBar.Bridge" parent="Theme.MaterialComponents.Bridge">
    <item name="windowActionBar">false</item>
    <item name="windowNoTitle">true</item>
  </style>
    <style name="Theme.OrderSystem" parent="Theme.MaterialComponents.DayNight.DarkActionBar.Bridge">
        
        <item name="colorPrimary">@color/purple_500</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor" ns2:targetApi="l">?attr/colorPrimaryVariant</item>
        
    </style>
    <style name="ThemeOverlay.AppCompat" parent="Base.ThemeOverlay.AppCompat"/>
    <style name="ThemeOverlay.AppCompat.ActionBar" parent="Base.ThemeOverlay.AppCompat.ActionBar"/>
    <style name="ThemeOverlay.AppCompat.Dark" parent="Base.ThemeOverlay.AppCompat.Dark"/>
    <style name="ThemeOverlay.AppCompat.Dark.ActionBar" parent="Base.ThemeOverlay.AppCompat.Dark.ActionBar"/>
    <style name="ThemeOverlay.AppCompat.DayNight" parent="ThemeOverlay.AppCompat.Light"/>
    <style name="ThemeOverlay.AppCompat.DayNight.ActionBar">
        <item name="colorControlNormal">?android:attr/textColorPrimary</item>
        <item name="searchViewStyle">@style/Widget.AppCompat.SearchView.ActionBar</item>
    </style>
    <style name="ThemeOverlay.AppCompat.Dialog" parent="Base.ThemeOverlay.AppCompat.Dialog"/>
    <style name="ThemeOverlay.AppCompat.Dialog.Alert" parent="Base.ThemeOverlay.AppCompat.Dialog.Alert"/>
    <style name="ThemeOverlay.AppCompat.Light" parent="Base.ThemeOverlay.AppCompat.Light"/>
    <style name="ThemeOverlay.Design.TextInputEditText" parent=""/>
    <style name="ThemeOverlay.MaterialComponents" parent="ThemeOverlay.AppCompat"/>
    <style name="ThemeOverlay.MaterialComponents.ActionBar" parent="ThemeOverlay.AppCompat.ActionBar"/>
    <style name="ThemeOverlay.MaterialComponents.ActionBar.Primary" parent="ThemeOverlay.AppCompat.ActionBar">
    <item name="android:textColorPrimary">?attr/colorOnPrimary</item>
    <item name="android:textColorSecondary">@color/material_on_primary_emphasis_medium</item>
    <item name="actionMenuTextColor">?attr/colorOnPrimary</item>
    <item name="colorControlNormal">?attr/colorOnPrimary</item>
    
    <item name="android:colorBackground">?attr/colorPrimary</item>
  </style>
    <style name="ThemeOverlay.MaterialComponents.ActionBar.Surface" parent="ThemeOverlay.AppCompat.ActionBar">
    <item name="android:textColorPrimary">@color/material_on_surface_emphasis_high_type</item>
    <item name="android:textColorSecondary">@color/material_on_surface_emphasis_medium</item>
    <item name="actionMenuTextColor">@color/material_on_surface_emphasis_medium</item>
    <item name="colorControlNormal">@color/material_on_surface_emphasis_medium</item>
    
    <item name="android:colorBackground">?attr/colorSurface</item>
  </style>
    <style name="ThemeOverlay.MaterialComponents.AutoCompleteTextView" parent="">
    <item name="colorControlActivated">?attr/colorPrimary</item>
  </style>
    <style name="ThemeOverlay.MaterialComponents.AutoCompleteTextView.FilledBox">
    <item name="autoCompleteTextViewStyle">
      @style/Widget.MaterialComponents.AutoCompleteTextView.FilledBox
    </item>
  </style>
    <style name="ThemeOverlay.MaterialComponents.AutoCompleteTextView.FilledBox.Dense">
    <item name="autoCompleteTextViewStyle">
      @style/Widget.MaterialComponents.AutoCompleteTextView.FilledBox.Dense
    </item>
  </style>
    <style name="ThemeOverlay.MaterialComponents.AutoCompleteTextView.OutlinedBox">
    <item name="autoCompleteTextViewStyle">
      @style/Widget.MaterialComponents.AutoCompleteTextView.OutlinedBox
    </item>
  </style>
    <style name="ThemeOverlay.MaterialComponents.AutoCompleteTextView.OutlinedBox.Dense">
    <item name="autoCompleteTextViewStyle">
      @style/Widget.MaterialComponents.AutoCompleteTextView.OutlinedBox.Dense
    </item>
  </style>
    <style name="ThemeOverlay.MaterialComponents.BottomAppBar.Primary" parent="">
    <item name="colorControlNormal">?attr/colorOnPrimary</item>
    <item name="actionMenuTextColor">?attr/colorOnPrimary</item>
  </style>
    <style name="ThemeOverlay.MaterialComponents.BottomAppBar.Surface" parent="">
    <item name="colorControlNormal">@color/material_on_surface_emphasis_medium</item>
    <item name="actionMenuTextColor">@color/material_on_surface_emphasis_medium</item>
  </style>
    <style name="ThemeOverlay.MaterialComponents.BottomSheetDialog" parent="ThemeOverlay.MaterialComponents.Dialog">
    <item name="android:windowBackground">@android:color/transparent</item>
    <item name="android:statusBarColor" ns2:targetApi="21">@android:color/transparent</item>
    <item name="android:windowAnimationStyle">
      @style/Animation.MaterialComponents.BottomSheetDialog
    </item>
    <item name="bottomSheetStyle">@style/Widget.MaterialComponents.BottomSheet.Modal</item>
    <item name="materialButtonStyle">@style/Widget.MaterialComponents.Button</item>
  </style>
    <style name="ThemeOverlay.MaterialComponents.Dark" parent="ThemeOverlay.AppCompat.Dark">
    <item name="android:colorBackground">@color/design_dark_default_color_background</item>
    <item name="colorOnBackground">@color/design_dark_default_color_on_background</item>
    <item name="colorSurface">@color/design_dark_default_color_surface</item>
    <item name="colorOnSurface">@color/design_dark_default_color_on_surface</item>
    <item name="colorError">@color/design_dark_default_color_error</item>
    <item name="colorOnError">@color/design_dark_default_color_on_error</item>
  </style>
    <style name="ThemeOverlay.MaterialComponents.Dark.ActionBar" parent="ThemeOverlay.AppCompat.Dark.ActionBar"/>
    <style name="ThemeOverlay.MaterialComponents.DayNight.BottomSheetDialog" parent="ThemeOverlay.MaterialComponents.Light.BottomSheetDialog"/>
    <style name="ThemeOverlay.MaterialComponents.Dialog" parent="Base.ThemeOverlay.MaterialComponents.Dialog"/>
    <style name="ThemeOverlay.MaterialComponents.Dialog.Alert" parent="Base.ThemeOverlay.MaterialComponents.Dialog.Alert"/>
    <style name="ThemeOverlay.MaterialComponents.Light" parent="ThemeOverlay.AppCompat.Light">
    <item name="android:colorBackground">@color/design_default_color_background</item>
    <item name="colorOnBackground">@color/design_default_color_on_background</item>
    <item name="colorSurface">@color/design_default_color_surface</item>
    <item name="colorOnSurface">@color/design_default_color_on_surface</item>
    <item name="colorError">@color/design_default_color_error</item>
    <item name="colorOnError">@color/design_default_color_on_error</item>
  </style>
    <style name="ThemeOverlay.MaterialComponents.Light.BottomSheetDialog" parent="ThemeOverlay.MaterialComponents.Dialog">
    <item name="android:windowBackground">@android:color/transparent</item>
    <item name="android:statusBarColor" ns2:targetApi="21">@android:color/transparent</item>
    <item name="android:windowAnimationStyle">
      @style/Animation.MaterialComponents.BottomSheetDialog
    </item>
    <item name="bottomSheetStyle">@style/Widget.MaterialComponents.BottomSheet.Modal</item>
    <item name="materialButtonStyle">@style/Widget.MaterialComponents.Button</item>
  </style>
    <style name="ThemeOverlay.MaterialComponents.MaterialAlertDialog" parent="Base.ThemeOverlay.MaterialComponents.MaterialAlertDialog">
    <item name="materialAlertDialogTitlePanelStyle">@style/MaterialAlertDialog.MaterialComponents.Title.Panel</item>
    <item name="materialAlertDialogTitleIconStyle">@style/MaterialAlertDialog.MaterialComponents.Title.Icon</item>
    <item name="materialAlertDialogTitleTextStyle">@style/MaterialAlertDialog.MaterialComponents.Title.Text</item>
  </style>
    <style name="ThemeOverlay.MaterialComponents.MaterialAlertDialog.Centered" parent="Base.ThemeOverlay.MaterialComponents.MaterialAlertDialog">
    <item name="materialAlertDialogTitlePanelStyle">@style/MaterialAlertDialog.MaterialComponents.Title.Panel.CenterStacked</item>
    <item name="materialAlertDialogTitleIconStyle">@style/MaterialAlertDialog.MaterialComponents.Title.Icon.CenterStacked</item>
    <item name="materialAlertDialogTitleTextStyle">@style/MaterialAlertDialog.MaterialComponents.Title.Text.CenterStacked</item>
  </style>
    <style name="ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date" parent="ThemeOverlay.MaterialComponents.MaterialAlertDialog"/>
    <style name="ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Calendar"/>
    <style name="ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Spinner"/>
    <style name="ThemeOverlay.MaterialComponents.MaterialCalendar" parent="ThemeOverlay.MaterialComponents.Dialog">
    <item name="materialCalendarStyle">@style/Widget.MaterialComponents.MaterialCalendar</item>

    
    <item name="materialCalendarHeaderLayout">@style/Widget.MaterialComponents.MaterialCalendar.HeaderLayout</item>
    <item name="materialCalendarHeaderDivider">@style/Widget.MaterialComponents.MaterialCalendar.HeaderDivider</item>
    <item name="materialCalendarHeaderTitle">@style/Widget.MaterialComponents.MaterialCalendar.HeaderTitle</item>
    <item name="materialCalendarHeaderSelection">@style/Widget.MaterialComponents.MaterialCalendar.HeaderSelection</item>
    <item name="materialCalendarHeaderConfirmButton">@style/Widget.MaterialComponents.MaterialCalendar.HeaderConfirmButton</item>
    <item name="materialCalendarHeaderToggleButton">@style/Widget.MaterialComponents.MaterialCalendar.HeaderToggleButton</item>

    
    <item name="materialCalendarDay">@style/Widget.MaterialComponents.MaterialCalendar.DayTextView</item>

    
    <item name="buttonBarPositiveButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog</item>
    <item name="buttonBarNegativeButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog</item>
  </style>
    <style name="ThemeOverlay.MaterialComponents.MaterialCalendar.Fullscreen" parent="ThemeOverlay.MaterialComponents.MaterialCalendar">
    <item name="materialCalendarStyle">@style/Widget.MaterialComponents.MaterialCalendar.Fullscreen</item>
    <item name="materialCalendarHeaderSelection">@style/Widget.MaterialComponents.MaterialCalendar.HeaderSelection.Fullscreen</item>
  </style>
    <style name="ThemeOverlay.MaterialComponents.TextInputEditText" parent="ThemeOverlay.Design.TextInputEditText">
    <item name="colorControlActivated">?attr/colorPrimary</item>
    <item name="android:editTextBackground">@null</item>
    <item name="editTextBackground">@null</item>
  </style>
    <style name="ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox">
    <item name="editTextStyle">@style/Widget.MaterialComponents.TextInputEditText.FilledBox</item>
  </style>
    <style name="ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox.Dense">
    <item name="editTextStyle">@style/Widget.MaterialComponents.TextInputEditText.FilledBox.Dense
    </item>
  </style>
    <style name="ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox">
    <item name="editTextStyle">@style/Widget.MaterialComponents.TextInputEditText.OutlinedBox</item>
  </style>
    <style name="ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox.Dense">
    <item name="editTextStyle">@style/Widget.MaterialComponents.TextInputEditText.OutlinedBox.Dense
    </item>
  </style>
    <style name="ThemeOverlay.MaterialComponents.Toolbar.Primary" parent="">
    <item name="colorControlNormal">?attr/colorOnPrimary</item>
    <item name="actionMenuTextColor">?attr/colorOnPrimary</item>
  </style>
    <style name="ThemeOverlay.MaterialComponents.Toolbar.Surface" parent="">
    <item name="colorControlNormal">@color/material_on_surface_emphasis_medium</item>
    <item name="actionMenuTextColor">@color/material_on_surface_emphasis_medium</item>
  </style>
    <style name="ThemeOverlayColorAccentRed" ns2:ignore="NewApi">
    <item name="android:colorAccent">#FFFF0000</item>
  </style>
    <style name="Widget.AppCompat.ActionBar" parent="Base.Widget.AppCompat.ActionBar">
    </style>
    <style name="Widget.AppCompat.ActionBar.Solid" parent="Base.Widget.AppCompat.ActionBar.Solid">
    </style>
    <style name="Widget.AppCompat.ActionBar.TabBar" parent="Base.Widget.AppCompat.ActionBar.TabBar">
    </style>
    <style name="Widget.AppCompat.ActionBar.TabText" parent="Base.Widget.AppCompat.ActionBar.TabText">
    </style>
    <style name="Widget.AppCompat.ActionBar.TabView" parent="Base.Widget.AppCompat.ActionBar.TabView">
    </style>
    <style name="Widget.AppCompat.ActionButton" parent="Base.Widget.AppCompat.ActionButton"/>
    <style name="Widget.AppCompat.ActionButton.CloseMode" parent="Base.Widget.AppCompat.ActionButton.CloseMode"/>
    <style name="Widget.AppCompat.ActionButton.Overflow" parent="Base.Widget.AppCompat.ActionButton.Overflow"/>
    <style name="Widget.AppCompat.ActionMode" parent="Base.Widget.AppCompat.ActionMode">
    </style>
    <style name="Widget.AppCompat.ActivityChooserView" parent="Base.Widget.AppCompat.ActivityChooserView">
    </style>
    <style name="Widget.AppCompat.AutoCompleteTextView" parent="Base.Widget.AppCompat.AutoCompleteTextView">
    </style>
    <style name="Widget.AppCompat.Button" parent="Base.Widget.AppCompat.Button"/>
    <style name="Widget.AppCompat.Button.Borderless" parent="Base.Widget.AppCompat.Button.Borderless"/>
    <style name="Widget.AppCompat.Button.Borderless.Colored" parent="Base.Widget.AppCompat.Button.Borderless.Colored"/>
    <style name="Widget.AppCompat.Button.ButtonBar.AlertDialog" parent="Base.Widget.AppCompat.Button.ButtonBar.AlertDialog"/>
    <style name="Widget.AppCompat.Button.Colored" parent="Base.Widget.AppCompat.Button.Colored"/>
    <style name="Widget.AppCompat.Button.Small" parent="Base.Widget.AppCompat.Button.Small"/>
    <style name="Widget.AppCompat.ButtonBar" parent="Base.Widget.AppCompat.ButtonBar"/>
    <style name="Widget.AppCompat.ButtonBar.AlertDialog" parent="Base.Widget.AppCompat.ButtonBar.AlertDialog"/>
    <style name="Widget.AppCompat.CompoundButton.CheckBox" parent="Base.Widget.AppCompat.CompoundButton.CheckBox"/>
    <style name="Widget.AppCompat.CompoundButton.RadioButton" parent="Base.Widget.AppCompat.CompoundButton.RadioButton"/>
    <style name="Widget.AppCompat.CompoundButton.Switch" parent="Base.Widget.AppCompat.CompoundButton.Switch"/>
    <style name="Widget.AppCompat.DrawerArrowToggle" parent="Base.Widget.AppCompat.DrawerArrowToggle">
        <item name="color">?attr/colorControlNormal</item>
    </style>
    <style name="Widget.AppCompat.DropDownItem.Spinner" parent="RtlOverlay.Widget.AppCompat.Search.DropDown.Text"/>
    <style name="Widget.AppCompat.EditText" parent="Base.Widget.AppCompat.EditText"/>
    <style name="Widget.AppCompat.ImageButton" parent="Base.Widget.AppCompat.ImageButton"/>
    <style name="Widget.AppCompat.Light.ActionBar" parent="Base.Widget.AppCompat.Light.ActionBar">
    </style>
    <style name="Widget.AppCompat.Light.ActionBar.Solid" parent="Base.Widget.AppCompat.Light.ActionBar.Solid">
    </style>
    <style name="Widget.AppCompat.Light.ActionBar.Solid.Inverse"/>
    <style name="Widget.AppCompat.Light.ActionBar.TabBar" parent="Base.Widget.AppCompat.Light.ActionBar.TabBar">
    </style>
    <style name="Widget.AppCompat.Light.ActionBar.TabBar.Inverse"/>
    <style name="Widget.AppCompat.Light.ActionBar.TabText" parent="Base.Widget.AppCompat.Light.ActionBar.TabText">
    </style>
    <style name="Widget.AppCompat.Light.ActionBar.TabText.Inverse" parent="Base.Widget.AppCompat.Light.ActionBar.TabText.Inverse">
    </style>
    <style name="Widget.AppCompat.Light.ActionBar.TabView" parent="Base.Widget.AppCompat.Light.ActionBar.TabView">
    </style>
    <style name="Widget.AppCompat.Light.ActionBar.TabView.Inverse"/>
    <style name="Widget.AppCompat.Light.ActionButton" parent="Widget.AppCompat.ActionButton"/>
    <style name="Widget.AppCompat.Light.ActionButton.CloseMode" parent="Widget.AppCompat.ActionButton.CloseMode"/>
    <style name="Widget.AppCompat.Light.ActionButton.Overflow" parent="Widget.AppCompat.ActionButton.Overflow"/>
    <style name="Widget.AppCompat.Light.ActionMode.Inverse" parent="Widget.AppCompat.ActionMode"/>
    <style name="Widget.AppCompat.Light.ActivityChooserView" parent="Widget.AppCompat.ActivityChooserView"/>
    <style name="Widget.AppCompat.Light.AutoCompleteTextView" parent="Widget.AppCompat.AutoCompleteTextView"/>
    <style name="Widget.AppCompat.Light.DropDownItem.Spinner" parent="Widget.AppCompat.DropDownItem.Spinner"/>
    <style name="Widget.AppCompat.Light.ListPopupWindow" parent="Widget.AppCompat.ListPopupWindow"/>
    <style name="Widget.AppCompat.Light.ListView.DropDown" parent="Widget.AppCompat.ListView.DropDown"/>
    <style name="Widget.AppCompat.Light.PopupMenu" parent="Base.Widget.AppCompat.Light.PopupMenu"/>
    <style name="Widget.AppCompat.Light.PopupMenu.Overflow" parent="Base.Widget.AppCompat.Light.PopupMenu.Overflow">
    </style>
    <style name="Widget.AppCompat.Light.SearchView" parent="Widget.AppCompat.SearchView"/>
    <style name="Widget.AppCompat.Light.Spinner.DropDown.ActionBar" parent="Widget.AppCompat.Spinner.DropDown.ActionBar"/>
    <style name="Widget.AppCompat.ListMenuView" parent="Base.Widget.AppCompat.ListMenuView"/>
    <style name="Widget.AppCompat.ListPopupWindow" parent="Base.Widget.AppCompat.ListPopupWindow">
    </style>
    <style name="Widget.AppCompat.ListView" parent="Base.Widget.AppCompat.ListView"/>
    <style name="Widget.AppCompat.ListView.DropDown" parent="Base.Widget.AppCompat.ListView.DropDown"/>
    <style name="Widget.AppCompat.ListView.Menu" parent="Base.Widget.AppCompat.ListView.Menu"/>
    <style name="Widget.AppCompat.PopupMenu" parent="Base.Widget.AppCompat.PopupMenu"/>
    <style name="Widget.AppCompat.PopupMenu.Overflow" parent="Base.Widget.AppCompat.PopupMenu.Overflow">
    </style>
    <style name="Widget.AppCompat.PopupWindow" parent="Base.Widget.AppCompat.PopupWindow">
    </style>
    <style name="Widget.AppCompat.ProgressBar" parent="Base.Widget.AppCompat.ProgressBar">
    </style>
    <style name="Widget.AppCompat.ProgressBar.Horizontal" parent="Base.Widget.AppCompat.ProgressBar.Horizontal">
    </style>
    <style name="Widget.AppCompat.RatingBar" parent="Base.Widget.AppCompat.RatingBar"/>
    <style name="Widget.AppCompat.RatingBar.Indicator" parent="Base.Widget.AppCompat.RatingBar.Indicator"/>
    <style name="Widget.AppCompat.RatingBar.Small" parent="Base.Widget.AppCompat.RatingBar.Small"/>
    <style name="Widget.AppCompat.SearchView" parent="Base.Widget.AppCompat.SearchView"/>
    <style name="Widget.AppCompat.SearchView.ActionBar" parent="Base.Widget.AppCompat.SearchView.ActionBar"/>
    <style name="Widget.AppCompat.SeekBar" parent="Base.Widget.AppCompat.SeekBar"/>
    <style name="Widget.AppCompat.SeekBar.Discrete" parent="Base.Widget.AppCompat.SeekBar.Discrete"/>
    <style name="Widget.AppCompat.Spinner" parent="Base.Widget.AppCompat.Spinner"/>
    <style name="Widget.AppCompat.Spinner.DropDown"/>
    <style name="Widget.AppCompat.Spinner.DropDown.ActionBar"/>
    <style name="Widget.AppCompat.Spinner.Underlined" parent="Base.Widget.AppCompat.Spinner.Underlined"/>
    <style name="Widget.AppCompat.TextView" parent="Base.Widget.AppCompat.TextView"/>
    <style name="Widget.AppCompat.TextView.SpinnerItem" parent="Base.Widget.AppCompat.TextView.SpinnerItem"/>
    <style name="Widget.AppCompat.Toolbar" parent="Base.Widget.AppCompat.Toolbar"/>
    <style name="Widget.AppCompat.Toolbar.Button.Navigation" parent="Base.Widget.AppCompat.Toolbar.Button.Navigation"/>
    <style name="Widget.Compat.NotificationActionContainer" parent=""/>
    <style name="Widget.Compat.NotificationActionText" parent=""/>
    <style name="Widget.Design.AppBarLayout" parent="android:Widget">
    <item name="android:background">?attr/colorPrimary</item>
    <item name="android:stateListAnimator" ns2:ignore="NewApi">
      @animator/design_appbar_state_list_animator
    </item>
    <item name="android:keyboardNavigationCluster" ns2:ignore="NewApi">true</item>
    <item name="android:touchscreenBlocksFocus" ns2:ignore="NewApi">true</item>
  </style>
    <style name="Widget.Design.BottomNavigationView" parent="">
    <item name="elevation">@dimen/design_bottom_navigation_elevation</item>
    <item name="enforceTextAppearance">false</item>
    <item name="enforceMaterialTheme">false</item>
    <item name="itemBackground">?attr/selectableItemBackgroundBorderless</item>
    <item name="itemHorizontalTranslationEnabled">true</item>
    <item name="itemIconSize">@dimen/design_bottom_navigation_icon_size</item>
    <item name="labelVisibilityMode">auto</item>
  </style>
    <style name="Widget.Design.BottomSheet.Modal" parent="android:Widget">
    <item name="enforceMaterialTheme">false</item>
    <item name="android:background">?android:attr/colorBackground</item>
    <item name="android:elevation" ns2:ignore="NewApi">
      @dimen/design_bottom_sheet_modal_elevation
    </item>
    <item name="behavior_peekHeight">auto</item>
    <item name="behavior_hideable">true</item>
    <item name="behavior_skipCollapsed">false</item>
    <item name="shapeAppearance">@null</item>
    <item name="shapeAppearanceOverlay">@null</item>
    <item name="backgroundTint">?android:attr/colorBackground</item>
  </style>
    <style name="Widget.Design.CollapsingToolbar" parent="android:Widget">
    <item name="expandedTitleMargin">32dp</item>
    <item name="statusBarScrim">?attr/colorPrimaryDark</item>
  </style>
    <style name="Widget.Design.FloatingActionButton" parent="android:Widget">
    <item name="android:background">@drawable/design_fab_background</item>
    <item name="android:clickable">true</item>
    <item name="android:focusable">true</item>
    <item name="backgroundTint">?attr/colorAccent</item>
    <item name="fabSize">auto</item>
    <item name="elevation">@dimen/design_fab_elevation</item>
    <item name="hoveredFocusedTranslationZ">@dimen/design_fab_translation_z_hovered_focused</item>
    <item name="pressedTranslationZ">@dimen/design_fab_translation_z_pressed</item>
    <item name="rippleColor">?attr/colorControlHighlight</item>
    <item name="borderWidth">@dimen/design_fab_border_width</item>
    <item name="maxImageSize">@dimen/design_fab_image_size</item>
    <item name="showMotionSpec">@animator/design_fab_show_motion_spec</item>
    <item name="hideMotionSpec">@animator/design_fab_hide_motion_spec</item>
  </style>
    <style name="Widget.Design.NavigationView" parent="Widget.Design.ScrimInsetsFrameLayout">
    <item name="elevation">@dimen/design_navigation_elevation</item>
    <item name="itemIconPadding">@dimen/design_navigation_item_icon_padding</item>
    <item name="itemHorizontalPadding">@dimen/design_navigation_item_horizontal_padding</item>
    <item name="android:background">?android:attr/windowBackground</item>
    <item name="android:fitsSystemWindows">true</item>
    <item name="android:maxWidth">@dimen/design_navigation_max_width</item>
  </style>
    <style name="Widget.Design.ScrimInsetsFrameLayout" parent="">
    <item name="insetForeground">#4000</item>
  </style>
    <style name="Widget.Design.Snackbar" parent="android:Widget">
    <item name="android:minWidth">@dimen/design_snackbar_min_width</item>
    <item name="android:maxWidth">@dimen/design_snackbar_max_width</item>
    <item name="android:background">@drawable/design_snackbar_background</item>
    <item name="android:paddingLeft">@dimen/design_snackbar_padding_horizontal</item>
    <item name="android:paddingRight">@dimen/design_snackbar_padding_horizontal</item>
    <item name="elevation">@dimen/design_snackbar_elevation</item>
    <item name="maxActionInlineWidth">@dimen/design_snackbar_action_inline_max_width</item>
    <item name="animationMode">slide</item>
    <item name="actionTextColorAlpha">@dimen/design_snackbar_action_text_color_alpha</item>
  </style>
    <style name="Widget.Design.TabLayout" parent="Base.Widget.Design.TabLayout">
    <item name="tabGravity">fill</item>
    <item name="tabMode">fixed</item>
    <item name="tabIndicatorFullWidth">true</item>
  </style>
    <style name="Widget.Design.TextInputEditText" parent="Widget.AppCompat.EditText">
    <item name="enforceMaterialTheme">false</item>
    <item name="enforceTextAppearance">false</item>
  </style>
    <style name="Widget.Design.TextInputLayout" parent="android:Widget">
    <item name="materialThemeOverlay">@style/ThemeOverlay.Design.TextInputEditText</item>
    <item name="enforceMaterialTheme">false</item>
    <item name="enforceTextAppearance">false</item>

    <item name="boxBackgroundMode">none</item>
    <item name="boxStrokeColor">@color/design_box_stroke_color</item>
    <item name="passwordToggleDrawable">@drawable/design_password_eye</item>
    <item name="passwordToggleTint">@color/design_icon_tint</item>
    <item name="passwordToggleContentDescription">@string/password_toggle_content_description</item>
    <item name="errorIconDrawable">@null</item>
    <item name="endIconTint">@color/design_icon_tint</item>
    <item name="startIconTint">@color/design_icon_tint</item>

    <item name="counterTextAppearance">@style/TextAppearance.Design.Counter</item>
    <item name="counterOverflowTextAppearance">@style/TextAppearance.Design.Counter.Overflow</item>
    <item name="errorTextAppearance">@style/TextAppearance.Design.Error</item>
    <item name="helperTextTextAppearance">@style/TextAppearance.Design.HelperText</item>
    <item name="hintTextAppearance">@style/TextAppearance.Design.Hint</item>
    <item name="placeholderTextAppearance">@style/TextAppearance.Design.Placeholder</item>
    <item name="prefixTextAppearance">@style/TextAppearance.Design.Prefix</item>
    <item name="suffixTextAppearance">@style/TextAppearance.Design.Suffix</item>

    <item name="counterTextColor">@null</item>
    <item name="counterOverflowTextColor">@null</item>
    <item name="errorTextColor">@null</item>
    <item name="helperTextTextColor">@null</item>
    <item name="hintTextColor">@null</item>
    <item name="placeholderTextColor">@null</item>
    <item name="prefixTextColor">@null</item>
    <item name="suffixTextColor">@null</item>

    <item name="shapeAppearance">@null</item>
    <item name="shapeAppearanceOverlay">@null</item>
  </style>
    <style name="Widget.MaterialComponents.ActionBar.Primary" parent="Widget.AppCompat.ActionBar.Solid">
    <item name="background">?attr/colorPrimary</item>
    <item name="elevation">@dimen/design_appbar_elevation</item>
  </style>
    <style name="Widget.MaterialComponents.ActionBar.PrimarySurface" parent="Widget.MaterialComponents.ActionBar.Primary"/>
    <style name="Widget.MaterialComponents.ActionBar.Solid" parent="Widget.AppCompat.ActionBar.Solid">
    <item name="titleTextStyle">?attr/textAppearanceHeadline6</item>
    <item name="subtitleTextStyle">?attr/textAppearanceSubtitle1</item>
    
    <item name="android:minHeight">@dimen/mtrl_toolbar_default_height</item>
    <item name="maxButtonHeight">@dimen/mtrl_toolbar_default_height</item>
  </style>
    <style name="Widget.MaterialComponents.ActionBar.Surface" parent="Widget.AppCompat.Light.ActionBar.Solid">
    <item name="background">?attr/colorSurface</item>
    <item name="elevation">0dp</item>
  </style>
    <style name="Widget.MaterialComponents.AppBarLayout.Primary" parent="Widget.Design.AppBarLayout"/>
    <style name="Widget.MaterialComponents.AppBarLayout.PrimarySurface" parent="Widget.MaterialComponents.AppBarLayout.Primary"/>
    <style name="Widget.MaterialComponents.AppBarLayout.Surface" parent="Widget.Design.AppBarLayout">
    <item name="android:background">?attr/colorSurface</item>
  </style>
    <style name="Widget.MaterialComponents.AutoCompleteTextView.FilledBox" parent="Base.Widget.MaterialComponents.AutoCompleteTextView">
    <item name="android:paddingTop">28dp</item>
    <item name="android:paddingBottom">12dp</item>
  </style>
    <style name="Widget.MaterialComponents.AutoCompleteTextView.FilledBox.Dense">
    <item name="android:paddingTop">24dp</item>
    <item name="android:paddingBottom">8dp</item>
  </style>
    <style name="Widget.MaterialComponents.AutoCompleteTextView.OutlinedBox" parent="Base.Widget.MaterialComponents.AutoCompleteTextView"/>
    <style name="Widget.MaterialComponents.AutoCompleteTextView.OutlinedBox.Dense">
    <item name="android:paddingTop">13dp</item>
    <item name="android:paddingBottom">13dp</item>
  </style>
    <style name="Widget.MaterialComponents.Badge" parent="android:Widget">
    <item name="backgroundColor">?attr/colorError</item>
    <item name="maxCharacterCount">@integer/mtrl_badge_max_character_count</item>
    <item name="badgeGravity">TOP_END</item>
  </style>
    <style name="Widget.MaterialComponents.BottomAppBar" parent="Widget.AppCompat.Toolbar">
    <item name="enforceMaterialTheme">true</item>
    <item name="backgroundTint">?attr/colorSurface</item>
    <item name="fabCradleMargin">@dimen/mtrl_bottomappbar_fab_cradle_margin</item>
    <item name="fabCradleRoundedCornerRadius">
      @dimen/mtrl_bottomappbar_fab_cradle_rounded_corner_radius
    </item>
    <item name="fabCradleVerticalOffset">@dimen/mtrl_bottomappbar_fab_cradle_vertical_offset</item>
    <item name="android:minHeight">@dimen/mtrl_bottomappbar_height</item>
    <item name="maxButtonHeight">@dimen/mtrl_bottomappbar_height</item>
    <item name="elevation">8dp</item>
    <item name="paddingBottomSystemWindowInsets">true</item>
    <item name="paddingLeftSystemWindowInsets">true</item>
    <item name="paddingRightSystemWindowInsets">true</item>
  </style>
    <style name="Widget.MaterialComponents.BottomAppBar.Colored" parent="Widget.MaterialComponents.BottomAppBar">
    <item name="backgroundTint">?attr/colorPrimary</item>
    <item name="materialThemeOverlay">@style/ThemeOverlay.MaterialComponents.BottomAppBar.Primary</item>
  </style>
    <style name="Widget.MaterialComponents.BottomAppBar.PrimarySurface" parent="Widget.MaterialComponents.BottomAppBar.Colored"/>
    <style name="Widget.MaterialComponents.BottomNavigationView" parent="Widget.Design.BottomNavigationView">
    <item name="enforceTextAppearance">true</item>
    <item name="enforceMaterialTheme">true</item>
    <item name="android:background">?attr/colorSurface</item>
    <item name="itemBackground">@null</item>
    <item name="itemHorizontalTranslationEnabled">false</item>
    <item name="itemIconTint">@color/mtrl_bottom_nav_item_tint</item>
    <item name="itemTextAppearanceInactive">?attr/textAppearanceCaption</item>
    <item name="itemTextAppearanceActive">?attr/textAppearanceCaption</item>
    <item name="itemTextColor">@color/mtrl_bottom_nav_item_tint</item>
    <item name="itemRippleColor">@color/mtrl_bottom_nav_ripple_color</item>
  </style>
    <style name="Widget.MaterialComponents.BottomNavigationView.Colored">
    <item name="enforceTextAppearance">true</item>
    <item name="enforceMaterialTheme">true</item>
    <item name="android:background">?attr/colorPrimary</item>
    <item name="itemIconTint">@color/mtrl_bottom_nav_colored_item_tint</item>
    <item name="itemRippleColor">@color/mtrl_bottom_nav_colored_ripple_color</item>
    <item name="itemTextAppearanceInactive">?attr/textAppearanceCaption</item>
    <item name="itemTextAppearanceActive">?attr/textAppearanceCaption</item>
    <item name="itemTextColor">@color/mtrl_bottom_nav_colored_item_tint</item>
  </style>
    <style name="Widget.MaterialComponents.BottomNavigationView.PrimarySurface" parent="Widget.MaterialComponents.BottomNavigationView.Colored"/>
    <style name="Widget.MaterialComponents.BottomSheet" parent="Widget.Design.BottomSheet.Modal">
    <item name="enforceMaterialTheme">true</item>
    <item name="android:background">@null</item>
    <item name="shapeAppearance">?attr/shapeAppearanceLargeComponent</item>
    <item name="shapeAppearanceOverlay">@style/ShapeAppearanceOverlay.MaterialComponents.BottomSheet</item>
    <item name="backgroundTint">?attr/colorSurface</item>
    <item name="android:elevation" ns2:ignore="NewApi">
      @dimen/design_bottom_sheet_elevation
    </item>
  </style>
    <style name="Widget.MaterialComponents.BottomSheet.Modal" parent="Widget.MaterialComponents.BottomSheet">
    <item name="android:elevation" ns2:ignore="NewApi">
      @dimen/design_bottom_sheet_modal_elevation
    </item>
  </style>
    <style name="Widget.MaterialComponents.Button" parent="Widget.AppCompat.Button">
    <item name="enforceMaterialTheme">true</item>
    <item name="android:background">@empty</item>
    <item name="enforceTextAppearance">true</item>
    <item name="android:textAppearance">?attr/textAppearanceButton</item>
    <item name="android:textColor">@color/mtrl_btn_text_color_selector</item>
    <item name="android:paddingLeft">@dimen/mtrl_btn_padding_left</item>
    <item name="android:paddingRight">@dimen/mtrl_btn_padding_right</item>
    <item name="android:paddingTop">@dimen/mtrl_btn_padding_top</item>
    <item name="android:paddingBottom">@dimen/mtrl_btn_padding_bottom</item>
    <item name="android:insetLeft">0dp</item>
    <item name="android:insetRight">0dp</item>
    <item name="android:insetTop">@dimen/mtrl_btn_inset</item>
    <item name="android:insetBottom">@dimen/mtrl_btn_inset</item>
    <item name="android:stateListAnimator" ns2:ignore="NewApi">@animator/mtrl_btn_state_list_anim</item>
    <item name="cornerRadius">@null</item>
    <item name="elevation">@dimen/mtrl_btn_elevation</item>
    <item name="iconPadding">@dimen/mtrl_btn_icon_padding</item>
    <item name="iconTint">@color/mtrl_btn_text_color_selector</item>
    <item name="rippleColor">@color/mtrl_btn_ripple_color</item>
    <item name="backgroundTint">@color/mtrl_btn_bg_color_selector</item>
    <item name="shapeAppearance">?attr/shapeAppearanceSmallComponent</item>
  </style>
    <style name="Widget.MaterialComponents.Button.Icon">
    <item name="android:paddingLeft">@dimen/mtrl_btn_icon_btn_padding_left</item>
  </style>
    <style name="Widget.MaterialComponents.Button.OutlinedButton" parent="Widget.MaterialComponents.Button.TextButton">
    <item name="android:paddingLeft">@dimen/mtrl_btn_padding_left</item>
    <item name="android:paddingRight">@dimen/mtrl_btn_padding_right</item>
    <item name="strokeColor">@color/mtrl_btn_stroke_color_selector</item>
    <item name="strokeWidth">@dimen/mtrl_btn_stroke_size</item>
  </style>
    <style name="Widget.MaterialComponents.Button.OutlinedButton.Icon">
    <item name="android:paddingLeft">@dimen/mtrl_btn_icon_btn_padding_left</item>
  </style>
    <style name="Widget.MaterialComponents.Button.TextButton" parent="Widget.MaterialComponents.Button.UnelevatedButton">
    <item name="android:textColor">@color/mtrl_text_btn_text_color_selector</item>
    <item name="android:paddingLeft">@dimen/mtrl_btn_text_btn_padding_left</item>
    <item name="android:paddingRight">@dimen/mtrl_btn_text_btn_padding_right</item>
    <item name="iconTint">@color/mtrl_text_btn_text_color_selector</item>
    <item name="iconPadding">@dimen/mtrl_btn_text_btn_icon_padding</item>
    <item name="backgroundTint">@color/mtrl_btn_text_btn_bg_color_selector</item>
    <item name="rippleColor">@color/mtrl_btn_text_btn_ripple_color</item>
  </style>
    <style name="Widget.MaterialComponents.Button.TextButton.Dialog">
    <item name="android:minWidth">@dimen/mtrl_btn_dialog_btn_min_width</item>
    <item name="android:lines">1</item>
    <item name="android:ellipsize">end</item>
    <item name="android:singleLine">true</item>
    <item name="android:layout_marginStart">@dimen/mtrl_btn_text_btn_padding_left</item>
    <item name="android:layout_marginLeft">@dimen/mtrl_btn_text_btn_padding_left</item>
  </style>
    <style name="Widget.MaterialComponents.Button.TextButton.Dialog.Flush">
    <item name="android:layout_marginStart">0dp</item>
    <item name="android:layout_marginLeft">0dp</item>
  </style>
    <style name="Widget.MaterialComponents.Button.TextButton.Dialog.Icon">
    
  </style>
    <style name="Widget.MaterialComponents.Button.TextButton.Icon">
    
  </style>
    <style name="Widget.MaterialComponents.Button.TextButton.Snackbar">
    <item name="android:textColor">?attr/colorPrimary</item>
  </style>
    <style name="Widget.MaterialComponents.Button.UnelevatedButton">
    <item name="android:stateListAnimator" ns2:ignore="NewApi">@animator/mtrl_btn_unelevated_state_list_anim</item>
    <item name="elevation">0dp</item>
  </style>
    <style name="Widget.MaterialComponents.Button.UnelevatedButton.Icon">
    <item name="android:paddingLeft">@dimen/mtrl_btn_icon_btn_padding_left</item>
  </style>
    <style name="Widget.MaterialComponents.CardView" parent="CardView">
    <item name="enforceMaterialTheme">true</item>

    <item name="android:stateListAnimator" ns2:ignore="NewApi">
      @animator/mtrl_card_state_list_anim
    </item>
    <item name="cardBackgroundColor">?attr/colorSurface</item>
    <item name="cardCornerRadius">@null</item>
    <item name="cardElevation">@dimen/mtrl_card_elevation</item>
    <item name="cardForegroundColor">@color/mtrl_card_view_foreground</item>
    <item name="checkedIcon">@drawable/ic_mtrl_checked_circle</item>
    <item name="checkedIconTint">?attr/colorPrimary</item>
    <item name="rippleColor">@color/mtrl_card_view_ripple</item>
    <item name="shapeAppearance">?attr/shapeAppearanceMediumComponent</item>
  </style>
    <style name="Widget.MaterialComponents.CheckedTextView" parent="Base.Widget.MaterialComponents.CheckedTextView">
    <item name="android:textAppearance">?attr/textAppearanceBody1</item>
    <item name="android:textColor">@color/material_on_surface_emphasis_medium</item>
  </style>
    <style name="Widget.MaterialComponents.Chip.Action" parent="Base.Widget.MaterialComponents.Chip">
    <item name="closeIconVisible">false</item>
  </style>
    <style name="Widget.MaterialComponents.Chip.Choice" parent="Base.Widget.MaterialComponents.Chip">
    <item name="android:checkable">true</item>

    <item name="chipIconVisible">false</item>
    <item name="checkedIconVisible">false</item>
    <item name="closeIconVisible">false</item>

    <item name="checkedIcon">@drawable/ic_mtrl_chip_checked_black</item>

    <item name="android:textColor">@color/mtrl_choice_chip_text_color</item>

    <item name="chipBackgroundColor">@color/mtrl_choice_chip_background_color</item>
    <item name="rippleColor">@color/mtrl_choice_chip_ripple_color</item>

  </style>
    <style name="Widget.MaterialComponents.Chip.Entry" parent="Base.Widget.MaterialComponents.Chip">
    <item name="android:checkable">true</item>
  </style>
    <style name="Widget.MaterialComponents.Chip.Filter" parent="Base.Widget.MaterialComponents.Chip">
    <item name="android:checkable">true</item>

    <item name="chipIconVisible">false</item>
    <item name="closeIconVisible">false</item>

    <item name="checkedIcon">@drawable/ic_mtrl_chip_checked_black</item>
  </style>
    <style name="Widget.MaterialComponents.ChipGroup" parent="android:Widget">
    <item name="chipSpacingHorizontal">8dp</item>
    <item name="singleLine">false</item>
    <item name="singleSelection">false</item>
  </style>
    <style name="Widget.MaterialComponents.CompoundButton.CheckBox" parent="Widget.AppCompat.CompoundButton.CheckBox">
    <item name="enforceMaterialTheme">true</item>
    <item name="useMaterialThemeColors">true</item>
    <item name="android:minWidth">?attr/minTouchTargetSize</item>
    <item name="android:minHeight">?attr/minTouchTargetSize</item>
  </style>
    <style name="Widget.MaterialComponents.CompoundButton.RadioButton" parent="Widget.AppCompat.CompoundButton.RadioButton">
    <item name="enforceMaterialTheme">true</item>
    <item name="useMaterialThemeColors">true</item>
    <item name="android:minWidth">?attr/minTouchTargetSize</item>
    <item name="android:minHeight">?attr/minTouchTargetSize</item>
  </style>
    <style name="Widget.MaterialComponents.CompoundButton.Switch" parent="Widget.AppCompat.CompoundButton.Switch">
    <item name="enforceMaterialTheme">true</item>
    <item name="useMaterialThemeColors">true</item>
    <item name="android:minWidth">?attr/minTouchTargetSize</item>
    <item name="android:minHeight">?attr/minTouchTargetSize</item>
  </style>
    <style name="Widget.MaterialComponents.ExtendedFloatingActionButton" parent="Widget.MaterialComponents.Button">
    <item name="android:insetTop">0dp</item>
    <item name="android:insetBottom">0dp</item>
    <item name="android:maxLines">1</item>
    <item name="android:minHeight">@dimen/mtrl_extended_fab_min_height</item>
    <item name="android:minWidth">@dimen/mtrl_extended_fab_min_width</item>
    <item name="android:paddingTop">@dimen/mtrl_extended_fab_top_padding</item>
    <item name="android:paddingBottom">@dimen/mtrl_extended_fab_bottom_padding</item>
    <item name="android:paddingStart" ns2:ignore="NewApi">
      @dimen/mtrl_extended_fab_start_padding
    </item>
    <item name="android:paddingEnd" ns2:ignore="NewApi">
      @dimen/mtrl_extended_fab_end_padding
    </item>
    <item name="android:paddingLeft">@dimen/mtrl_extended_fab_start_padding</item>
    <item name="android:paddingRight">@dimen/mtrl_extended_fab_end_padding</item>
    <item name="android:stateListAnimator" ns2:ignore="NewApi">
      @animator/mtrl_extended_fab_state_list_animator
    </item>
    <item name="android:textColor">@color/mtrl_fab_icon_text_color_selector</item>
    <item name="backgroundTint">@color/mtrl_fab_bg_color_selector</item>
    <item name="elevation">@dimen/mtrl_extended_fab_elevation</item>
    <item name="iconPadding">@dimen/mtrl_extended_fab_icon_text_spacing</item>
    <item name="iconSize">@dimen/mtrl_extended_fab_icon_size</item>
    <item name="iconTint">@color/mtrl_fab_icon_text_color_selector</item>
    <item name="rippleColor">@color/mtrl_fab_ripple_color</item>
    <item name="shapeAppearanceOverlay">
      @style/ShapeAppearanceOverlay.MaterialComponents.ExtendedFloatingActionButton
    </item>
  </style>
    <style name="Widget.MaterialComponents.ExtendedFloatingActionButton.Icon" parent="Widget.MaterialComponents.ExtendedFloatingActionButton">
    <item name="android:gravity">start|center_vertical</item>
    <item name="android:paddingStart" ns2:ignore="NewApi">
      @dimen/mtrl_extended_fab_start_padding_icon
    </item>
    <item name="android:paddingEnd" ns2:ignore="NewApi">
      @dimen/mtrl_extended_fab_end_padding_icon
    </item>
    <item name="android:paddingLeft">@dimen/mtrl_extended_fab_start_padding_icon</item>
    <item name="android:paddingRight">@dimen/mtrl_extended_fab_end_padding_icon</item>
  </style>
    <style name="Widget.MaterialComponents.FloatingActionButton" parent="Widget.Design.FloatingActionButton">
    <item name="android:background">@null</item>
    <item name="enforceMaterialTheme">true</item>
    <item name="ensureMinTouchTargetSize">true</item>
    <item name="elevation">@dimen/mtrl_fab_elevation</item>
    <item name="backgroundTint">@color/mtrl_fab_bg_color_selector</item>
    <item name="tint">@color/mtrl_fab_icon_text_color_selector</item>
    <item name="hoveredFocusedTranslationZ">@dimen/mtrl_fab_translation_z_hovered_focused</item>
    <item name="pressedTranslationZ">@dimen/mtrl_fab_translation_z_pressed</item>
    <item name="rippleColor">@color/mtrl_fab_ripple_color</item>
    <item name="showMotionSpec">@animator/mtrl_fab_show_motion_spec</item>
    <item name="hideMotionSpec">@animator/mtrl_fab_hide_motion_spec</item>
    <item name="shapeAppearance">?attr/shapeAppearanceSmallComponent</item>
    <item name="shapeAppearanceOverlay">
      @style/ShapeAppearanceOverlay.MaterialComponents.FloatingActionButton
    </item>
  </style>
    <style name="Widget.MaterialComponents.Light.ActionBar.Solid" parent="Widget.AppCompat.Light.ActionBar.Solid">
    <item name="titleTextStyle">?attr/textAppearanceHeadline6</item>
    <item name="subtitleTextStyle">?attr/textAppearanceSubtitle1</item>
    
    <item name="android:minHeight">@dimen/mtrl_toolbar_default_height</item>
    <item name="maxButtonHeight">@dimen/mtrl_toolbar_default_height</item>
  </style>
    <style name="Widget.MaterialComponents.MaterialButtonToggleGroup" parent="android:Widget">
    <item name="singleSelection">false</item>
  </style>
    <style name="Widget.MaterialComponents.MaterialCalendar" parent="android:Widget">
    <item name="android:windowFullscreen">false</item>
    <item name="shapeAppearance">?attr/shapeAppearanceMediumComponent</item>
    <item name="dayStyle">@style/Widget.MaterialComponents.MaterialCalendar.Day</item>
    <item name="dayInvalidStyle">@style/Widget.MaterialComponents.MaterialCalendar.Day.Invalid</item>
    <item name="daySelectedStyle">@style/Widget.MaterialComponents.MaterialCalendar.Day.Selected</item>
    <item name="dayTodayStyle">@style/Widget.MaterialComponents.MaterialCalendar.Day.Today</item>
    <item name="yearStyle">@style/Widget.MaterialComponents.MaterialCalendar.Year</item>
    <item name="yearSelectedStyle">@style/Widget.MaterialComponents.MaterialCalendar.Year.Selected</item>
    <item name="yearTodayStyle">@style/Widget.MaterialComponents.MaterialCalendar.Year.Today</item>
    <item name="rangeFillColor">@color/mtrl_calendar_selected_range</item>
  </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.Day" parent="Widget.MaterialComponents.MaterialCalendar.Item">
    <item name="itemShapeAppearanceOverlay">@style/ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Day</item>
    <item name="android:width">@dimen/mtrl_calendar_day_width</item>
    <item name="android:height">@dimen/mtrl_calendar_day_height</item>
    <item name="android:insetTop">@dimen/mtrl_calendar_day_vertical_padding</item>
    <item name="android:insetBottom">@dimen/mtrl_calendar_day_vertical_padding</item>
    <item name="android:insetLeft">@dimen/mtrl_calendar_day_horizontal_padding</item>
    <item name="android:insetRight">@dimen/mtrl_calendar_day_horizontal_padding</item>
  </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.Day.Invalid">
    <item name="itemTextColor">@color/material_on_surface_disabled</item>
    <item name="itemStrokeWidth">0dp</item>
  </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.Day.Selected">
    <item name="itemFillColor">?attr/colorPrimary</item>
    <item name="itemTextColor">?attr/colorOnPrimary</item>
    <item name="itemStrokeWidth">0dp</item>
  </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.Day.Today">
    <item name="itemStrokeColor">@color/material_on_surface_emphasis_high_type</item>
    <item name="itemStrokeWidth">@dimen/mtrl_calendar_day_today_stroke</item>
  </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.DayTextView" parent="Widget.AppCompat.TextView">
    <item name="android:textAppearance">?attr/textAppearanceCaption</item>
    <item name="android:gravity">center</item>
  </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.Fullscreen" parent="Widget.MaterialComponents.MaterialCalendar">
    <item name="android:windowFullscreen">true</item>
    <item name="shapeAppearanceOverlay">@style/ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Window.Fullscreen</item>
  </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.HeaderConfirmButton" parent="Widget.MaterialComponents.Button.TextButton">
    <item name="android:textColor">@color/mtrl_on_primary_text_btn_text_color_selector</item>
    <item name="rippleColor">@color/mtrl_btn_ripple_color</item>
  </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.HeaderDivider" parent="android:Widget">
    <item name="android:visibility">gone</item>
    <item name="android:background">?attr/colorOnPrimary</item>
  </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.HeaderLayout" parent="android:Widget">
    <item name="android:background">?attr/colorPrimary</item>
  </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.HeaderSelection" parent="Widget.AppCompat.TextView">
    <item name="android:textAppearance">?attr/textAppearanceHeadline4</item>
    <item name="android:textColor">?attr/colorOnPrimary</item>
    <item name="android:maxLines">@integer/mtrl_calendar_selection_text_lines</item>
    <item name="android:ellipsize">end</item>
    <item name="autoSizeTextType">uniform</item>
    <item name="autoSizeMaxTextSize">34sp</item>
    <item name="autoSizeMinTextSize">2sp</item>
  </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.HeaderSelection.Fullscreen">
    <item name="android:textAppearance">?attr/textAppearanceHeadline6</item>
    <item name="android:maxLines">1</item>
    <item name="autoSizeMaxTextSize">20sp</item>
  </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.HeaderTitle" parent="Widget.AppCompat.TextView">
    <item name="android:textAppearance">?attr/textAppearanceOverline</item>
    <item name="android:textColor">?attr/colorOnPrimary</item>
    <item name="android:maxLines">1</item>
    <item name="android:ellipsize">end</item>
    <item name="autoSizeTextType">uniform</item>
    <item name="autoSizeMaxTextSize">10sp</item>
    <item name="autoSizeMinTextSize">2sp</item>
  </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.HeaderToggleButton" parent="Widget.AppCompat.ImageButton">
    <item name="android:background">?attr/actionBarItemBackground</item>
    <item name="android:tint">?attr/colorOnPrimary</item>
  </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.Item" parent="">
    <item name="itemFillColor">@android:color/transparent</item>
    <item name="itemTextColor">@color/material_on_surface_emphasis_high_type</item>
    <item name="itemStrokeColor">@color/mtrl_calendar_item_stroke_color</item>
    <item name="itemStrokeWidth">1dp</item>
    <item name="itemShapeAppearance">?attr/shapeAppearanceSmallComponent</item>
  </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.Year" parent="Widget.MaterialComponents.MaterialCalendar.Item">
    <item name="itemShapeAppearanceOverlay">@style/ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Year</item>
    <item name="android:width">@dimen/mtrl_calendar_year_width</item>
    <item name="android:height">@dimen/mtrl_calendar_year_height</item>
    <item name="android:insetTop">@dimen/mtrl_calendar_year_vertical_padding</item>
    <item name="android:insetBottom">@dimen/mtrl_calendar_year_vertical_padding</item>
    <item name="android:insetLeft">@dimen/mtrl_calendar_year_horizontal_padding</item>
    <item name="android:insetRight">@dimen/mtrl_calendar_year_horizontal_padding</item>
  </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.Year.Selected" parent="Widget.MaterialComponents.MaterialCalendar.Year">
    <item name="itemFillColor">?attr/colorPrimary</item>
    <item name="itemTextColor">?attr/colorOnPrimary</item>
    <item name="itemStrokeColor">?attr/colorOnPrimary</item>
    <item name="itemStrokeWidth">0dp</item>
  </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.Year.Today" parent="Widget.MaterialComponents.MaterialCalendar.Year">
    <item name="itemStrokeColor">@color/material_on_surface_emphasis_high_type</item>
    <item name="itemStrokeWidth">@dimen/mtrl_calendar_day_today_stroke</item>
  </style>
    <style name="Widget.MaterialComponents.NavigationView" parent="Widget.Design.NavigationView">
    <item name="enforceMaterialTheme">true</item>
    <item name="android:background">?attr/colorSurface</item>
    
    <item name="itemBackground">@null</item>
    <item name="itemIconPadding">@dimen/mtrl_navigation_item_icon_padding</item>
    <item name="itemIconTint">@color/mtrl_navigation_item_icon_tint</item>
    <item name="itemIconSize">@dimen/mtrl_navigation_item_icon_size</item>
    <item name="itemHorizontalPadding">@dimen/mtrl_navigation_item_horizontal_padding</item>
    <item name="itemShapeAppearance">?attr/shapeAppearanceSmallComponent</item>
    <item name="itemShapeFillColor">@color/mtrl_navigation_item_background_color</item>
    <item name="itemShapeInsetStart">@dimen/mtrl_navigation_item_shape_horizontal_margin</item>
    <item name="itemShapeInsetTop">@dimen/mtrl_navigation_item_shape_vertical_margin</item>
    <item name="itemShapeInsetEnd">@dimen/mtrl_navigation_item_shape_horizontal_margin</item>
    <item name="itemShapeInsetBottom">@dimen/mtrl_navigation_item_shape_vertical_margin</item>
    <item name="itemTextAppearance">?attr/textAppearanceSubtitle2</item>
    <item name="itemTextColor">@color/mtrl_navigation_item_text_color</item>
  </style>
    <style name="Widget.MaterialComponents.PopupMenu" parent="Base.Widget.MaterialComponents.PopupMenu"/>
    <style name="Widget.MaterialComponents.PopupMenu.ContextMenu" parent="Base.Widget.MaterialComponents.PopupMenu.ContextMenu"/>
    <style name="Widget.MaterialComponents.PopupMenu.ListPopupWindow" parent="Base.Widget.MaterialComponents.PopupMenu.ListPopupWindow"/>
    <style name="Widget.MaterialComponents.PopupMenu.Overflow" parent="Base.Widget.MaterialComponents.PopupMenu.Overflow"/>
    <style name="Widget.MaterialComponents.ShapeableImageView" parent="android:Widget">
    <item name="strokeColor">@color/material_on_surface_stroke</item>
    <item name="shapeAppearance">?attr/shapeAppearanceMediumComponent</item>
  </style>
    <style name="Widget.MaterialComponents.Slider" parent="Base.Widget.MaterialComponents.Slider"/>
    <style name="Widget.MaterialComponents.Snackbar" parent="Widget.Design.Snackbar">
    
    <item name="android:background">@null</item>
    <item name="android:layout_margin">@dimen/mtrl_snackbar_margin</item>
    <item name="animationMode">fade</item>
    <item name="backgroundOverlayColorAlpha">@dimen/mtrl_snackbar_background_overlay_color_alpha</item>
    <item name="actionTextColorAlpha">@dimen/mtrl_snackbar_action_text_color_alpha</item>
  </style>
    <style name="Widget.MaterialComponents.Snackbar.FullWidth" parent="Widget.Design.Snackbar">
    <item name="backgroundOverlayColorAlpha">@dimen/mtrl_snackbar_background_overlay_color_alpha</item>
    <item name="actionTextColorAlpha">@dimen/mtrl_snackbar_action_text_color_alpha</item>
  </style>
    <style name="Widget.MaterialComponents.Snackbar.TextView" parent="Widget.AppCompat.TextView">
    <item name="android:alpha">@dimen/material_emphasis_high_type</item>
    <item name="android:ellipsize">end</item>
    <item name="android:maxLines">@integer/design_snackbar_text_max_lines</item>
    <item name="android:textAlignment" ns2:ignore="NewApi">viewStart</item>
    <item name="android:textAppearance">?attr/textAppearanceBody2</item>
    <item name="android:textColor">?attr/colorSurface</item>
    <item name="android:paddingTop">@dimen/design_snackbar_padding_vertical</item>
    <item name="android:paddingBottom">@dimen/design_snackbar_padding_vertical</item>
    <item name="android:paddingLeft">@dimen/design_snackbar_padding_horizontal</item>
    <item name="android:paddingRight">@dimen/design_snackbar_padding_horizontal</item>
  </style>
    <style name="Widget.MaterialComponents.TabLayout" parent="Widget.Design.TabLayout">
    <item name="enforceMaterialTheme">true</item>
    <item name="enforceTextAppearance">true</item>
    <item name="android:background">?attr/colorSurface</item>
    <item name="tabIconTint">@color/mtrl_tabs_icon_color_selector</item>
    <item name="tabIndicatorAnimationDuration">@integer/mtrl_tab_indicator_anim_duration_ms</item>
    <item name="tabIndicatorColor">?attr/colorPrimary</item>
    <item name="tabTextAppearance">?attr/textAppearanceButton</item>
    <item name="tabTextColor">@color/mtrl_tabs_icon_color_selector</item>
    <item name="tabRippleColor">@color/mtrl_tabs_ripple_color</item>
    <item name="tabUnboundedRipple">true</item>
  </style>
    <style name="Widget.MaterialComponents.TabLayout.Colored">
    <item name="android:background">?attr/colorPrimary</item>
    <item name="tabIconTint">@color/mtrl_tabs_icon_color_selector_colored</item>
    <item name="tabIndicatorColor">?attr/colorOnPrimary</item>
    <item name="tabTextColor">@color/mtrl_tabs_icon_color_selector_colored</item>
    <item name="tabRippleColor">@color/mtrl_tabs_colored_ripple_color</item>
  </style>
    <style name="Widget.MaterialComponents.TabLayout.PrimarySurface" parent="Widget.MaterialComponents.TabLayout.Colored"/>
    <style name="Widget.MaterialComponents.TextInputEditText.FilledBox" parent="Base.Widget.MaterialComponents.TextInputEditText">
    <item name="android:paddingTop">28dp</item>
    <item name="android:paddingBottom">12dp</item>
  </style>
    <style name="Widget.MaterialComponents.TextInputEditText.FilledBox.Dense">
    <item name="android:paddingTop">24dp</item>
    <item name="android:paddingBottom">8dp</item>
  </style>
    <style name="Widget.MaterialComponents.TextInputEditText.OutlinedBox" parent="Base.Widget.MaterialComponents.TextInputEditText"/>
    <style name="Widget.MaterialComponents.TextInputEditText.OutlinedBox.Dense">
    <item name="android:paddingTop">13dp</item>
    <item name="android:paddingBottom">13dp</item>
  </style>
    <style name="Widget.MaterialComponents.TextInputLayout.FilledBox" parent="Base.Widget.MaterialComponents.TextInputLayout">
    <item name="materialThemeOverlay">
      @style/ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox
    </item>
    <item name="boxBackgroundMode">filled</item>
    <item name="boxBackgroundColor">@color/mtrl_filled_background_color</item>
    <item name="endIconTint">@color/mtrl_filled_icon_tint</item>
    <item name="startIconTint">@color/mtrl_filled_icon_tint</item>
    <item name="boxCollapsedPaddingTop">12dp</item>
    <item name="boxStrokeColor">@color/mtrl_filled_stroke_color</item>
    <item name="shapeAppearanceOverlay">
      @style/ShapeAppearanceOverlay.MaterialComponents.TextInputLayout.FilledBox
    </item>
  </style>
    <style name="Widget.MaterialComponents.TextInputLayout.FilledBox.Dense">
    <item name="materialThemeOverlay">
      @style/ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox.Dense
    </item>
    <item name="boxCollapsedPaddingTop">8dp</item>
  </style>
    <style name="Widget.MaterialComponents.TextInputLayout.FilledBox.Dense.ExposedDropdownMenu">
    <item name="materialThemeOverlay">
      @style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.FilledBox.Dense
    </item>
    <item name="endIconMode">dropdown_menu</item>
  </style>
    <style name="Widget.MaterialComponents.TextInputLayout.FilledBox.ExposedDropdownMenu">
    <item name="materialThemeOverlay">
      @style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.FilledBox
    </item>
    <item name="endIconMode">dropdown_menu</item>
  </style>
    <style name="Widget.MaterialComponents.TextInputLayout.OutlinedBox" parent="Base.Widget.MaterialComponents.TextInputLayout">
    <item name="materialThemeOverlay">
      @style/ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox
    </item>
    <item name="boxCollapsedPaddingTop">0dp</item>
  </style>
    <style name="Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense">
    <item name="materialThemeOverlay">
      @style/ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox.Dense
    </item>
  </style>
    <style name="Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense.ExposedDropdownMenu">
    <item name="materialThemeOverlay">
      @style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.OutlinedBox.Dense
    </item>
    <item name="endIconMode">dropdown_menu</item>
  </style>
    <style name="Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu">
    <item name="materialThemeOverlay">
      @style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.OutlinedBox
    </item>
    <item name="endIconMode">dropdown_menu</item>
  </style>
    <style name="Widget.MaterialComponents.TextView" parent="Base.Widget.MaterialComponents.TextView"/>
    <style name="Widget.MaterialComponents.Toolbar" parent="Widget.AppCompat.Toolbar">
    <item name="titleTextAppearance">?attr/textAppearanceHeadline6</item>
    <item name="titleTextColor">?android:attr/textColorPrimary</item>
    <item name="subtitleTextAppearance">?attr/textAppearanceSubtitle1</item>
    <item name="subtitleTextColor">?android:attr/textColorSecondary</item>
    
    <item name="android:minHeight">@dimen/mtrl_toolbar_default_height</item>
    <item name="maxButtonHeight">@dimen/mtrl_toolbar_default_height</item>
  </style>
    <style name="Widget.MaterialComponents.Toolbar.Primary">
    <item name="android:elevation" ns2:ignore="NewApi">@dimen/design_appbar_elevation</item>
    <item name="android:background">?attr/colorPrimary</item>
    <item name="titleTextColor">?attr/colorOnPrimary</item>
    <item name="subtitleTextColor">@color/material_on_primary_emphasis_medium</item>
    
    <item name="android:theme">@style/ThemeOverlay.MaterialComponents.Toolbar.Primary</item>
  </style>
    <style name="Widget.MaterialComponents.Toolbar.PrimarySurface" parent="Widget.MaterialComponents.Toolbar.Primary"/>
    <style name="Widget.MaterialComponents.Toolbar.Surface">
    <item name="android:background">?attr/colorSurface</item>
    <item name="titleTextColor">@color/material_on_surface_emphasis_high_type</item>
    <item name="subtitleTextColor">@color/material_on_surface_emphasis_medium</item>
    
    <item name="android:theme">@style/ThemeOverlay.MaterialComponents.Toolbar.Surface</item>
  </style>
    <style name="Widget.MaterialComponents.Tooltip" parent="android:Widget">
    <item name="android:layout_margin">8dp</item>
    <item name="android:minWidth">@dimen/mtrl_tooltip_minWidth</item>
    <item name="android:minHeight">@dimen/mtrl_tooltip_minHeight</item>
    <item name="android:padding">@dimen/mtrl_tooltip_padding</item>
    <item name="android:textAppearance">@style/TextAppearance.MaterialComponents.Tooltip</item>
    <item name="shapeAppearance">@style/ShapeAppearance.MaterialComponents.Tooltip</item>
  </style>
    <style name="Widget.Support.CoordinatorLayout" parent="android:Widget">
        <item name="statusBarBackground">#000000</item>
    </style>
    <declare-styleable name="ActionBar">
        
        <attr name="navigationMode">
            <!-- Normal static title text -->
            <enum name="normal" value="0"/>
            <!-- The action bar will use a selection list for navigation. -->
            <enum name="listMode" value="1"/>
            <!-- The action bar will use a series of horizontal tabs for navigation. -->
            <enum name="tabMode" value="2"/>
        </attr>
        
        <attr name="displayOptions">
            <flag name="none" value="0"/>
            <flag name="useLogo" value="0x1"/>
            <flag name="showHome" value="0x2"/>
            <flag name="homeAsUp" value="0x4"/>
            <flag name="showTitle" value="0x8"/>
            <flag name="showCustom" value="0x10"/>
            <flag name="disableHome" value="0x20"/>
        </attr>
        
        <attr name="title"/>
        
        <attr format="string" name="subtitle"/>
        
        <attr format="reference" name="titleTextStyle"/>
        
        <attr format="reference" name="subtitleTextStyle"/>
        
        <attr format="reference" name="icon"/>
        
        <attr format="reference" name="logo"/>
        
        <attr format="reference" name="divider"/>
        
        <attr format="reference" name="background"/>
        
        <attr format="reference|color" name="backgroundStacked"/>
        
        <attr format="reference|color" name="backgroundSplit"/>
        
        <attr format="reference" name="customNavigationLayout"/>
        
        <attr name="height"/>
        
        <attr format="reference" name="homeLayout"/>
        
        <attr format="reference" name="progressBarStyle"/>
        
        <attr format="reference" name="indeterminateProgressStyle"/>
        
        <attr format="dimension" name="progressBarPadding"/>
        
        <attr name="homeAsUpIndicator"/>
        
        <attr format="dimension" name="itemPadding"/>
        
        <attr format="boolean" name="hideOnContentScroll"/>
        
        <attr format="dimension" name="contentInsetStart"/>
        
        <attr format="dimension" name="contentInsetEnd"/>
        
        <attr format="dimension" name="contentInsetLeft"/>
        
        <attr format="dimension" name="contentInsetRight"/>
        
        <attr format="dimension" name="contentInsetStartWithNavigation"/>
        
        <attr format="dimension" name="contentInsetEndWithActions"/>
        
        <attr format="dimension" name="elevation"/>
        
        <attr format="reference" name="popupTheme"/>
    </declare-styleable>
    <declare-styleable name="ActionBarLayout">
        <attr name="android:layout_gravity"/>
    </declare-styleable>
    <declare-styleable name="ActionMenuItemView">
        <attr name="android:minWidth"/>
    </declare-styleable>
    <declare-styleable name="ActionMenuView">
        
    </declare-styleable>
    <declare-styleable name="ActionMode">
        
        <attr name="titleTextStyle"/>
        
        <attr name="subtitleTextStyle"/>
        
        <attr name="background"/>
        
        <attr name="backgroundSplit"/>
        
        <attr name="height"/>
        
        <attr format="reference" name="closeItemLayout"/>
    </declare-styleable>
    <declare-styleable name="ActivityChooserView">
        
        <attr format="string" name="initialActivityCount"/>
        
        <attr format="reference" name="expandActivityOverflowButtonDrawable"/>
    </declare-styleable>
    <declare-styleable name="AlertDialog">
        <attr name="android:layout"/>
        <attr format="reference" name="buttonPanelSideLayout"/>
        <attr format="reference" name="listLayout"/>
        <attr format="reference" name="multiChoiceItemLayout"/>
        <attr format="reference" name="singleChoiceItemLayout"/>
        <attr format="reference" name="listItemLayout"/>
        <attr format="boolean" name="showTitle"/>
        <attr format="dimension" name="buttonIconDimen"/>
    </declare-styleable>
    <declare-styleable name="AnimatedStateListDrawableCompat">
        
        <attr name="android:visible"/>
        
        <attr name="android:variablePadding"/>
        
        <attr name="android:constantSize"/>
        
        <attr name="android:dither"/>
        
        <attr name="android:enterFadeDuration"/>
        
        <attr name="android:exitFadeDuration"/>
        
        
    </declare-styleable>
    <declare-styleable name="AnimatedStateListDrawableItem">
        
        <attr name="android:drawable"/>
        
        <attr name="android:id"/>
    </declare-styleable>
    <declare-styleable name="AnimatedStateListDrawableTransition">
        
        <attr name="android:fromId"/>
        
        <attr name="android:toId"/>
        
        <attr name="android:drawable"/>
        
        <attr name="android:reversible"/>
    </declare-styleable>
    <declare-styleable name="AppBarLayout">
    
    <attr name="elevation"/>
    <attr name="android:background"/>
    
    <attr format="boolean" name="expanded"/>
    <attr name="android:keyboardNavigationCluster"/>
    <attr name="android:touchscreenBlocksFocus"/>
    
    <attr format="boolean" name="liftOnScroll"/>
    
    <attr format="reference" name="liftOnScrollTargetViewId"/>
    
    <attr format="color" name="statusBarForeground"/>
  </declare-styleable>
    <declare-styleable name="AppBarLayoutStates">
    
    <attr format="boolean" name="state_collapsed"/>
    
    <attr format="boolean" name="state_collapsible"/>
    
    <attr format="boolean" name="state_lifted"/>
    
    <attr format="boolean" name="state_liftable"/>
  </declare-styleable>
    <declare-styleable name="AppBarLayout_Layout">
    <attr name="layout_scrollFlags">
      <!-- Disable scrolling on the view. This flag should not be combined with any of the other
           scroll flags. -->
      <flag name="noScroll" value="0x0"/>

      <!-- The view will be scroll in direct relation to scroll events. This flag needs to be
           set for any of the other flags to take effect. If any sibling views
           before this one do not have this flag, then this value has no effect. -->
      <flag name="scroll" value="0x1"/>

      <!-- When exiting (scrolling off screen) the view will be scrolled until it is
           'collapsed'. The collapsed height is defined by the view's minimum height. -->
      <flag name="exitUntilCollapsed" value="0x2"/>

      <!-- When entering (scrolling on screen) the view will scroll on any downwards
           scroll event, regardless of whether the scrolling view is also scrolling. This
           is commonly referred to as the 'quick return' pattern. -->
      <flag name="enterAlways" value="0x4"/>

      <!-- An additional flag for 'enterAlways' which modifies the returning view to
           only initially scroll back to it's collapsed height. Once the scrolling view has
           reached the end of it's scroll range, the remainder of this view will be scrolled
           into view. -->
      <flag name="enterAlwaysCollapsed" value="0x8"/>

      <!-- Upon a scroll ending, if the view is only partially visible then it will be
           snapped and scrolled to it's closest edge. -->
      <flag name="snap" value="0x10"/>

      <!-- An additional flag to be used with 'snap'. If set, the view will be snapped to its
           top and bottom margins, as opposed to the edges of the view itself. -->
      <flag name="snapMargins" value="0x20"/>
    </attr>

    
    <attr format="reference" name="layout_scrollInterpolator"/>
  </declare-styleable>
    <declare-styleable name="AppCompatImageView">
        <attr name="android:src"/>
        
        <attr format="reference" name="srcCompat"/>

        
        <attr format="color" name="tint"/>

        
        <attr name="tintMode">
            <!-- The tint is drawn on top of the drawable.
                 [Sa + (1 - Sa)*Da, Rc = Sc + (1 - Sa)*Dc] -->
            <enum name="src_over" value="3"/>
            <!-- The tint is masked by the alpha channel of the drawable. The drawable’s
                 color channels are thrown out. [Sa * Da, Sc * Da] -->
            <enum name="src_in" value="5"/>
            <!-- The tint is drawn above the drawable, but with the drawable’s alpha
                 channel masking the result. [Da, Sc * Da + (1 - Sa) * Dc] -->
            <enum name="src_atop" value="9"/>
            <!-- Multiplies the color and alpha channels of the drawable with those of
                 the tint. [Sa * Da, Sc * Dc] -->
            <enum name="multiply" value="14"/>
            <!-- [Sa + Da - Sa * Da, Sc + Dc - Sc * Dc] -->
            <enum name="screen" value="15"/>
            <!-- Combines the tint and icon color and alpha channels, clamping the
                 result to valid color values. Saturate(S + D) -->
            <enum name="add" value="16"/>
        </attr>
    </declare-styleable>
    <declare-styleable name="AppCompatSeekBar">
        <attr name="android:thumb"/>
        
        <attr format="reference" name="tickMark"/>
        
        <attr format="color" name="tickMarkTint"/>
        
        <attr name="tickMarkTintMode">
            <!-- The tint is drawn on top of the drawable.
                 [Sa + (1 - Sa)*Da, Rc = Sc + (1 - Sa)*Dc] -->
            <enum name="src_over" value="3"/>
            <!-- The tint is masked by the alpha channel of the drawable. The drawable’s
                 color channels are thrown out. [Sa * Da, Sc * Da] -->
            <enum name="src_in" value="5"/>
            <!-- The tint is drawn above the drawable, but with the drawable’s alpha
                 channel masking the result. [Da, Sc * Da + (1 - Sa) * Dc] -->
            <enum name="src_atop" value="9"/>
            <!-- Multiplies the color and alpha channels of the drawable with those of
                 the tint. [Sa * Da, Sc * Dc] -->
            <enum name="multiply" value="14"/>
            <!-- [Sa + Da - Sa * Da, Sc + Dc - Sc * Dc] -->
            <enum name="screen" value="15"/>
            <!-- Combines the tint and drawable color and alpha channels, clamping the
                 result to valid color values. Saturate(S + D) -->
            <enum name="add" value="16"/>
        </attr>
    </declare-styleable>
    <declare-styleable name="AppCompatTextHelper">
        <attr name="android:drawableLeft"/>
        <attr name="android:drawableTop"/>
        <attr name="android:drawableRight"/>
        <attr name="android:drawableBottom"/>
        <attr name="android:drawableStart"/>
        <attr name="android:drawableEnd"/>
        <attr name="android:textAppearance"/>
    </declare-styleable>
    <declare-styleable name="AppCompatTextView">
        
        <attr format="reference|boolean" name="textAllCaps"/>
        
        <attr format="string" name="textLocale"/>
        <attr name="android:textAppearance"/>
        
        <attr format="enum" name="autoSizeTextType">
            <!-- No auto-sizing (default). -->
            <enum name="none" value="0"/>
            <!-- Uniform horizontal and vertical text size scaling to fit within the
            container. -->
            <enum name="uniform" value="1"/>
        </attr>
        
        <attr format="dimension" name="autoSizeStepGranularity"/>
        
        <attr format="reference" name="autoSizePresetSizes"/>
        
        <attr format="dimension" name="autoSizeMinTextSize"/>
        
        <attr format="dimension" name="autoSizeMaxTextSize"/>
        
        <attr format="string" name="fontFamily"/>
        
        <attr format="dimension" name="lineHeight"/>
        
        <attr format="dimension" name="firstBaselineToTopHeight"/>
        
        <attr format="dimension" name="lastBaselineToBottomHeight"/>
        
        <attr format="string" name="fontVariationSettings"/>
        
        <attr format="reference" name="drawableLeftCompat"/>
        <attr format="reference" name="drawableTopCompat"/>
        <attr format="reference" name="drawableRightCompat"/>
        <attr format="reference" name="drawableBottomCompat"/>
        <attr format="reference" name="drawableStartCompat"/>
        <attr format="reference" name="drawableEndCompat"/>
        
        <attr format="color" name="drawableTint"/>
        
        <attr name="drawableTintMode">
            <!-- The tint is drawn on top of the drawable.
                 [Sa + (1 - Sa)*Da, Rc = Sc + (1 - Sa)*Dc] -->
            <enum name="src_over" value="3"/>
            <!-- The tint is masked by the alpha channel of the drawable. The drawable’s
                 color channels are thrown out. [Sa * Da, Sc * Da] -->
            <enum name="src_in" value="5"/>
            <!-- The tint is drawn above the drawable, but with the drawable’s alpha
                 channel masking the result. [Da, Sc * Da + (1 - Sa) * Dc] -->
            <enum name="src_atop" value="9"/>
            <!-- Multiplies the color and alpha channels of the drawable with those of
                 the tint. [Sa * Da, Sc * Dc] -->
            <enum name="multiply" value="14"/>
            <!-- [Sa + Da - Sa * Da, Sc + Dc - Sc * Dc] -->
            <enum name="screen" value="15"/>
            <!-- Combines the tint and drawable color and alpha channels, clamping the
                 result to valid color values. Saturate(S + D) -->
            <enum name="add" value="16"/>
        </attr>
    </declare-styleable>
    <declare-styleable name="AppCompatTheme">

        
        
        
        <eat-comment/>

        
        <attr format="boolean" name="windowActionBar"/>

        
        <attr format="boolean" name="windowNoTitle"/>

        
        <attr format="boolean" name="windowActionBarOverlay"/>

        
        <attr format="boolean" name="windowActionModeOverlay"/>

        
        <attr format="dimension|fraction" name="windowFixedWidthMajor"/>
        
        <attr format="dimension|fraction" name="windowFixedHeightMinor"/>

        
        <attr format="dimension|fraction" name="windowFixedWidthMinor"/>
        
        <attr format="dimension|fraction" name="windowFixedHeightMajor"/>

        
        <attr format="dimension|fraction" name="windowMinWidthMajor"/>
        
        <attr format="dimension|fraction" name="windowMinWidthMinor"/>

        <attr name="android:windowIsFloating"/>
        <attr name="android:windowAnimationStyle"/>

        
        
        
        <eat-comment/>
        
        <attr format="reference" name="actionBarTabStyle"/>
        <attr format="reference" name="actionBarTabBarStyle"/>
        <attr format="reference" name="actionBarTabTextStyle"/>
        <attr format="reference" name="actionOverflowButtonStyle"/>
        <attr format="reference" name="actionOverflowMenuStyle"/>
        
        <attr format="reference" name="actionBarPopupTheme"/>
        
        <attr format="reference" name="actionBarStyle"/>
        
        <attr format="reference" name="actionBarSplitStyle"/>
        
        <attr format="reference" name="actionBarTheme"/>
        
        <attr format="reference" name="actionBarWidgetTheme"/>
        
        <attr format="dimension" name="actionBarSize">
            <enum name="wrap_content" value="0"/>
        </attr>
        
        <attr format="reference" name="actionBarDivider"/>
        
        <attr format="reference" name="actionBarItemBackground"/>
        
        <attr format="reference" name="actionMenuTextAppearance"/>
        
        
        <attr format="color|reference" name="actionMenuTextColor"/>


        
        
        
        <eat-comment/>
        <attr format="reference" name="actionModeStyle"/>
        <attr format="reference" name="actionModeCloseButtonStyle"/>
        
        <attr format="reference" name="actionModeBackground"/>
        
        <attr format="reference" name="actionModeSplitBackground"/>
        
        <attr format="reference" name="actionModeCloseDrawable"/>
        
        <attr format="reference" name="actionModeCutDrawable"/>
        
        <attr format="reference" name="actionModeCopyDrawable"/>
        
        <attr format="reference" name="actionModePasteDrawable"/>
        
        <attr format="reference" name="actionModeSelectAllDrawable"/>
        
        <attr format="reference" name="actionModeShareDrawable"/>
        
        <attr format="reference" name="actionModeFindDrawable"/>
        
        <attr format="reference" name="actionModeWebSearchDrawable"/>

        
        <attr format="reference" name="actionModePopupWindowStyle"/>


        
        
        
        <eat-comment/>
        
        <attr format="reference" name="textAppearanceLargePopupMenu"/>
        
        <attr format="reference" name="textAppearanceSmallPopupMenu"/>
        
        <attr format="reference" name="textAppearancePopupMenuHeader"/>


        
        
        
        <eat-comment/>

        
        <attr format="reference" name="dialogTheme"/>
        
        <attr format="dimension" name="dialogPreferredPadding"/>
        
        <attr format="reference" name="listDividerAlertDialog"/>
        
        <attr format="dimension" name="dialogCornerRadius"/>

        
        
        
        <eat-comment/>

        
        <attr format="reference" name="actionDropDownStyle"/>
        
        <attr format="dimension" name="dropdownListPreferredItemHeight"/>
        
        <attr format="reference" name="spinnerDropDownItemStyle"/>
        
        <attr format="reference" name="homeAsUpIndicator"/>

        
        <attr format="reference" name="actionButtonStyle"/>

        
        <attr format="reference" name="buttonBarStyle"/>
        
        <attr format="reference" name="buttonBarButtonStyle"/>
        
        <attr format="reference" name="selectableItemBackground"/>
        
        <attr format="reference" name="selectableItemBackgroundBorderless"/>
        
        <attr format="reference" name="borderlessButtonStyle"/>
        
        <attr format="reference" name="dividerVertical"/>
        
        <attr format="reference" name="dividerHorizontal"/>
        
        <attr format="reference" name="activityChooserViewStyle"/>

        
        <attr format="reference" name="toolbarStyle"/>
        
        <attr format="reference" name="toolbarNavigationButtonStyle"/>

        
        <attr format="reference" name="popupMenuStyle"/>
        
        <attr format="reference" name="popupWindowStyle"/>

        
        <attr format="reference|color" name="editTextColor"/>
        
        <attr format="reference" name="editTextBackground"/>

        
        <attr format="reference" name="imageButtonStyle"/>

        
        
        
        <eat-comment/>
        
        <attr format="reference" name="textAppearanceSearchResultTitle"/>
        
        <attr format="reference" name="textAppearanceSearchResultSubtitle"/>
        
        <attr format="reference|color" name="textColorSearchUrl"/>
        
        <attr format="reference" name="searchViewStyle"/>

        
        
        
        <eat-comment/>

        
        <attr format="dimension" name="listPreferredItemHeight"/>
        
        <attr format="dimension" name="listPreferredItemHeightSmall"/>
        
        <attr format="dimension" name="listPreferredItemHeightLarge"/>

        
        <attr format="dimension" name="listPreferredItemPaddingLeft"/>
        
        <attr format="dimension" name="listPreferredItemPaddingRight"/>

        
        <attr format="dimension" name="listPreferredItemPaddingStart"/>
        
        <attr format="dimension" name="listPreferredItemPaddingEnd"/>

        
        <attr format="reference" name="dropDownListViewStyle"/>
        <attr format="reference" name="listPopupWindowStyle"/>

        
        <attr format="reference" name="textAppearanceListItem"/>
        
        <attr format="reference" name="textAppearanceListItemSecondary"/>
        
        <attr format="reference" name="textAppearanceListItemSmall"/>

        
        
        
        <eat-comment/>

        
        <attr format="reference" name="panelBackground"/>
        
        <attr format="dimension" name="panelMenuListWidth"/>
        
        <attr format="reference" name="panelMenuListTheme"/>
        
        <attr format="reference" name="listChoiceBackgroundIndicator"/>

        
        
        
        <eat-comment/>

        
        <attr format="color" name="colorPrimary"/>

        
        <attr format="color" name="colorPrimaryDark"/>

        
        <attr format="color" name="colorAccent"/>

        
        <attr format="color" name="colorControlNormal"/>

        
        <attr format="color" name="colorControlActivated"/>

        
        <attr format="color" name="colorControlHighlight"/>

        
        <attr format="color" name="colorButtonNormal"/>

        
        <attr format="color" name="colorSwitchThumbNormal"/>

        
        <attr format="reference" name="controlBackground"/>

        
        <attr format="color" name="colorBackgroundFloating"/>

        
        
        
        <eat-comment/>
        <attr format="reference" name="alertDialogStyle"/>
        <attr format="reference" name="alertDialogButtonGroupStyle"/>
        <attr format="boolean" name="alertDialogCenterButtons"/>
        
        <attr format="reference" name="alertDialogTheme"/>

        
        <attr format="reference|color" name="textColorAlertDialogListItem"/>

        
        <attr format="reference" name="buttonBarPositiveButtonStyle"/>

        
        <attr format="reference" name="buttonBarNegativeButtonStyle"/>

        
        <attr format="reference" name="buttonBarNeutralButtonStyle"/>

        
        
        
        <eat-comment/>

        
        <attr format="reference" name="autoCompleteTextViewStyle"/>
        
        <attr format="reference" name="buttonStyle"/>
        
        <attr format="reference" name="buttonStyleSmall"/>
        
        <attr format="reference" name="checkboxStyle"/>
        
        <attr format="reference" name="checkedTextViewStyle"/>
        
        <attr format="reference" name="editTextStyle"/>
        
        <attr format="reference" name="radioButtonStyle"/>
        
        <attr format="reference" name="ratingBarStyle"/>
        
        <attr format="reference" name="ratingBarStyleIndicator"/>
        
        <attr format="reference" name="ratingBarStyleSmall"/>
        
        <attr format="reference" name="seekBarStyle"/>
        
        <attr format="reference" name="spinnerStyle"/>
        
        <attr format="reference" name="switchStyle"/>

        
        <attr format="reference" name="listMenuViewStyle"/>

        
        
        
        <eat-comment/>

        
        <attr format="reference" name="tooltipFrameBackground"/>
        
        <attr format="reference|color" name="tooltipForegroundColor"/>

        
        <attr format="reference|color" name="colorError"/>

        <attr format="string" name="viewInflaterClass"/>

        
        
        
        <eat-comment/>

        
        <attr format="reference" name="listChoiceIndicatorMultipleAnimated"/>
        
        <attr format="reference" name="listChoiceIndicatorSingleAnimated"/>

    </declare-styleable>
    <declare-styleable name="Badge">
    <attr format="color" name="backgroundColor"/>
    <attr format="color" name="badgeTextColor"/>
    <attr format="integer" name="maxCharacterCount"/>
    <attr format="integer" name="number"/>
    <attr name="badgeGravity">
      <!-- Gravity.TOP | Gravity.END -->
      <enum name="TOP_END" value="8388661"/>
      <!-- Gravity.TOP | Gravity.START -->
      <enum name="TOP_START" value="8388659"/>
      <!-- Gravity.BOTTOM | Gravity.END -->
      <enum name="BOTTOM_END" value="8388693"/>
      <!-- Gravity.BOTTOM | Gravity.START -->
      <enum name="BOTTOM_START" value="8388691"/>
    </attr>

    
    <attr format="dimension" name="horizontalOffset"/>
    <attr format="dimension" name="verticalOffset"/>
  </declare-styleable>
    <declare-styleable name="BottomAppBar">
    
    <attr name="backgroundTint"/>
    
    <attr name="elevation"/>
    
    <attr name="fabAlignmentMode">
      <!-- Mode that aligns the fab to the center. -->
      <enum name="center" value="0"/>
      <!-- Mode that aligns the fab to the end. -->
      <enum name="end" value="1"/>
    </attr>
    
    <attr name="fabAnimationMode">
      <!-- Mode that scales the fab down to a point, moves it, then scales the fab back to its normal size. -->
      <enum name="scale" value="0"/>
      <!-- Mode that slides the fab from one alignment mode to the next. -->
      <enum name="slide" value="1"/>
    </attr>
    
    <attr format="dimension" name="fabCradleMargin"/>
    
    <attr format="dimension" name="fabCradleRoundedCornerRadius"/>
    
    <attr format="dimension" name="fabCradleVerticalOffset"/>
    
    <attr format="boolean" name="hideOnScroll"/>
    
    <attr name="paddingBottomSystemWindowInsets"/>
    
    <attr name="paddingLeftSystemWindowInsets"/>
    
    <attr name="paddingRightSystemWindowInsets"/>
  </declare-styleable>
    <declare-styleable name="BottomNavigationView">
    
    <attr name="backgroundTint"/>
    
    <attr name="menu"/>
    
    <attr name="labelVisibilityMode">
      <!-- Label behaves as "labeled" when there are 3 items or less, or "selected" when there are
           4 items or more. -->
      <enum name="auto" value="-1"/>
      <!-- Label is shown on the selected navigation item. -->
      <enum name="selected" value="0"/>
      <!-- Label is shown on all navigation items. -->
      <enum name="labeled" value="1"/>
      <!-- Label is not shown on any navigation items. -->
      <enum name="unlabeled" value="2"/>
    </attr>
    
    <attr name="itemBackground"/>
    
    <attr format="color" name="itemRippleColor"/>
    
    <attr name="itemIconSize"/>
    
    <attr name="itemIconTint"/>
    
    <attr format="reference" name="itemTextAppearanceInactive"/>
    
    <attr format="reference" name="itemTextAppearanceActive"/>
    
    <attr name="itemTextColor"/>
    
    <attr format="boolean" name="itemHorizontalTranslationEnabled"/>
    <attr name="elevation"/>
  </declare-styleable>
    <declare-styleable name="BottomSheetBehavior_Layout">
    
    <attr format="dimension" name="behavior_peekHeight">
      <!-- Peek at the 16:9 ratio keyline of its parent -->
      <enum name="auto" value="-1"/>
    </attr>
    
    <attr format="boolean" name="behavior_hideable"/>
    
    <attr format="boolean" name="behavior_skipCollapsed"/>
    
    <attr format="boolean" name="behavior_fitToContents"/>
    
    <attr format="boolean" name="behavior_draggable"/>
    
    <attr format="reference|float" name="behavior_halfExpandedRatio"/>
    
    <attr format="reference|dimension" name="behavior_expandedOffset"/>
    
     <attr name="shapeAppearance"/>
    
    <attr name="shapeAppearanceOverlay"/>
    
    <attr name="backgroundTint"/>
    
    <attr name="behavior_saveFlags">
      <!-- This flag will preserve the peekHeight on configuration change. -->
      <flag name="peekHeight" value="0x1"/>
      <!-- This flag will preserve the fitToContents boolean value on configuration change. -->
      <flag name="fitToContents" value="0x2"/>
      <!-- This flag will preserve the hideable boolean value on configuration change. -->
      <flag name="hideable" value="0x4"/>
      <!-- This flag will preserve the skipCollapsed boolean value on configuration change. -->
      <flag name="skipCollapsed" value="0x8"/>
      <!-- This flag will preserve the all the aforementioned values on configuration change. -->
      <flag name="all" value="-1"/>
      <!-- This flag will not preserve the aforementioned values on configuration change. The only
           value preserved will be the positional state, e.g. collapsed, hidden, expanded, etc.
           This is the default behavior. -->
      <flag name="none" value="0"/>
    </attr>
    
    <attr format="boolean" name="gestureInsetBottomIgnored"/>
    <attr name="android:elevation"/>

  </declare-styleable>
    <declare-styleable name="ButtonBarLayout">
        
        <attr format="boolean" name="allowStacking"/>
    </declare-styleable>
    <declare-styleable name="CardView">
        
        <attr format="color" name="cardBackgroundColor"/>
        
        <attr format="dimension" name="cardCornerRadius"/>
        
        <attr format="dimension" name="cardElevation"/>
        
        <attr format="dimension" name="cardMaxElevation"/>
        
        <attr format="boolean" name="cardUseCompatPadding"/>
        
        <attr format="boolean" name="cardPreventCornerOverlap"/>
        
        <attr format="dimension" name="contentPadding"/>
        
        <attr format="dimension" name="contentPaddingLeft"/>
        
        <attr format="dimension" name="contentPaddingRight"/>
        
        <attr format="dimension" name="contentPaddingTop"/>
        
        <attr format="dimension" name="contentPaddingBottom"/>
        
        <attr name="android:minWidth"/>
        
        <attr name="android:minHeight"/>
    </declare-styleable>
    <declare-styleable name="Chip">
    
    
    <attr format="color" name="chipSurfaceColor"/>
    
    <attr format="color" name="chipBackgroundColor"/>
    
    <attr format="dimension" name="chipMinHeight"/>
    
    <attr format="dimension" name="chipCornerRadius"/>
    
    <attr format="color" name="chipStrokeColor"/>
    
    <attr format="dimension" name="chipStrokeWidth"/>
    
    <attr name="rippleColor"/>
    
    <attr format="dimension" name="chipMinTouchTargetSize"/>
    
    <attr name="ensureMinTouchTargetSize"/>

    
    <attr name="android:text"/>
    
    <attr name="android:textColor"/>
    
    <attr name="android:textAppearance"/>
    
    <attr name="android:ellipsize"/>
    
    <attr name="android:maxWidth"/>

    
    <attr format="boolean" name="chipIconVisible"/>
    
    <attr format="boolean" name="chipIconEnabled"/>
    
    <attr format="reference" name="chipIcon"/>
    
    <attr format="color" name="chipIconTint"/>
    
    <attr format="dimension" name="chipIconSize"/>

    
    <attr format="boolean" name="closeIconVisible"/>
    
    <attr format="boolean" name="closeIconEnabled"/>
    
    <attr format="reference" name="closeIcon"/>
    
    <attr format="color" name="closeIconTint"/>
    
    <attr format="dimension" name="closeIconSize"/>

    
    <attr name="android:checkable"/>
    
    <attr format="boolean" name="checkedIconVisible"/>
    
    <attr format="boolean" name="checkedIconEnabled"/>
    
    <attr name="checkedIcon"/>
    
    <attr name="checkedIconTint"/>

    
    <attr name="showMotionSpec"/>
    
    <attr name="hideMotionSpec"/>

    
    <attr name="shapeAppearance"/>
    
    <attr name="shapeAppearanceOverlay"/>

    

    

    
    <attr format="dimension" name="chipStartPadding"/>
    
    <attr format="dimension" name="iconStartPadding"/>

    

    
    <attr format="dimension" name="iconEndPadding"/>
    
    <attr format="dimension" name="textStartPadding"/>

    

    
    <attr format="dimension" name="textEndPadding"/>
    
    <attr format="dimension" name="closeIconStartPadding"/>

    

    
    <attr format="dimension" name="closeIconEndPadding"/>
    
    <attr format="dimension" name="chipEndPadding"/>

    
  </declare-styleable>
    <declare-styleable name="ChipGroup">

    
    <attr format="dimension" name="chipSpacing"/>
    
    <attr format="dimension" name="chipSpacingHorizontal"/>
    
    <attr format="dimension" name="chipSpacingVertical"/>

    
    <attr format="boolean" name="singleLine"/>

    
    <attr name="singleSelection"/>
    
    <attr name="selectionRequired"/>
    
    <attr format="reference" name="checkedChip"/>

  </declare-styleable>
    <declare-styleable name="CircleImageView"><attr format="dimension" name="civ_border_width"/><attr format="color" name="civ_border_color"/><attr format="boolean" name="civ_border_overlay"/><attr format="color" name="civ_fill_color"/></declare-styleable>
    <declare-styleable name="CollapsingToolbarLayout">
    
    <attr format="dimension" name="expandedTitleMargin"/>
    
    <attr format="dimension" name="expandedTitleMarginStart"/>
    
    <attr format="dimension" name="expandedTitleMarginTop"/>
    
    <attr format="dimension" name="expandedTitleMarginEnd"/>
    
    <attr format="dimension" name="expandedTitleMarginBottom"/>
    
    <attr format="reference" name="expandedTitleTextAppearance"/>
    
    <attr format="reference" name="collapsedTitleTextAppearance"/>
    
    <attr format="color" name="contentScrim"/>
    
    <attr format="color" name="statusBarScrim"/>
    
    <attr format="reference" name="toolbarId"/>
    
    <attr format="dimension" name="scrimVisibleHeightTrigger"/>
    
    <attr format="integer" name="scrimAnimationDuration"/>

    
    <attr name="collapsedTitleGravity">
      <!-- Push title to the top of its container, not changing its size. -->
      <flag name="top" value="0x30"/>
      <!-- Push title to the bottom of its container, not changing its size. -->
      <flag name="bottom" value="0x50"/>
      <!-- Push title to the left of its container, not changing its size. -->
      <flag name="left" value="0x03"/>
      <!-- Push title to the right of its container, not changing its size. -->
      <flag name="right" value="0x05"/>
      <!-- Place title in the vertical center of its container, not changing its size. -->
      <flag name="center_vertical" value="0x10"/>
      <!-- Grow the vertical size of the title if needed so it completely fills its container. -->
      <flag name="fill_vertical" value="0x70"/>
      <!-- Place title in the horizontal center of its container, not changing its size. -->
      <flag name="center_horizontal" value="0x01"/>
      <!-- Place the title in the center of its container in both the vertical and horizontal axis, not changing its size. -->
      <flag name="center" value="0x11"/>
      <!-- Push title to the beginning of its container, not changing its size. -->
      <flag name="start" value="0x00800003"/>
      <!-- Push title to the end of its container, not changing its size. -->
      <flag name="end" value="0x00800005"/>
    </attr>

    
    <attr name="expandedTitleGravity">
      <!-- Push title to the top of its container, not changing its size. -->
      <flag name="top" value="0x30"/>
      <!-- Push title to the bottom of its container, not changing its size. -->
      <flag name="bottom" value="0x50"/>
      <!-- Push title to the left of its container, not changing its size. -->
      <flag name="left" value="0x03"/>
      <!-- Push title to the right of its container, not changing its size. -->
      <flag name="right" value="0x05"/>
      <!-- Place title in the vertical center of its container, not changing its size. -->
      <flag name="center_vertical" value="0x10"/>
      <!-- Grow the vertical size of the title if needed so it completely fills its container. -->
      <flag name="fill_vertical" value="0x70"/>
      <!-- Place title in the horizontal center of its container, not changing its size. -->
      <flag name="center_horizontal" value="0x01"/>
      <!-- Place the title in the center of its container in both the vertical and horizontal axis, not changing its size. -->
      <flag name="center" value="0x11"/>
      <!-- Push title to the beginning of its container, not changing its size. -->
      <flag name="start" value="0x00800003"/>
      <!-- Push title to the end of its container, not changing its size. -->
      <flag name="end" value="0x00800005"/>
    </attr>

    
    <attr format="boolean" name="titleEnabled"/>
    
    <attr name="title"/>
    
    <attr format="integer" name="maxLines"/>
  </declare-styleable>
    <declare-styleable name="CollapsingToolbarLayout_Layout">
    <attr name="layout_collapseMode">
      <!-- The view will act as normal with no collapsing behavior. -->
      <enum name="none" value="0"/>
      <!-- The view will pin in place. -->
      <enum name="pin" value="1"/>
      <!-- The view will scroll in a parallax fashion. See the
           layout_collapseParallaxMultiplier attribute to change the multiplier. -->
      <enum name="parallax" value="2"/>
    </attr>

    
    <attr format="float" name="layout_collapseParallaxMultiplier"/>
  </declare-styleable>
    <declare-styleable name="ColorStateListItem">
        
        <attr name="android:color"/>
        
        <attr format="float" name="alpha"/>
        <attr name="android:alpha"/>
    </declare-styleable>
    <declare-styleable name="CompoundButton">
        <attr name="android:button"/>
        
        <attr format="reference" name="buttonCompat"/>
        
        <attr format="color" name="buttonTint"/>

        
        <attr name="buttonTintMode">
            <!-- The tint is drawn on top of the drawable.
                 [Sa + (1 - Sa)*Da, Rc = Sc + (1 - Sa)*Dc] -->
            <enum name="src_over" value="3"/>
            <!-- The tint is masked by the alpha channel of the drawable. The drawable’s
                 color channels are thrown out. [Sa * Da, Sc * Da] -->
            <enum name="src_in" value="5"/>
            <!-- The tint is drawn above the drawable, but with the drawable’s alpha
                 channel masking the result. [Da, Sc * Da + (1 - Sa) * Dc] -->
            <enum name="src_atop" value="9"/>
            <!-- Multiplies the color and alpha channels of the drawable with those of
                 the tint. [Sa * Da, Sc * Dc] -->
            <enum name="multiply" value="14"/>
            <!-- [Sa + Da - Sa * Da, Sc + Dc - Sc * Dc] -->
            <enum name="screen" value="15"/>
            <!-- Combines the tint and icon color and alpha channels, clamping the
                 result to valid color values. Saturate(S + D) -->
            <enum name="add" value="16"/>
        </attr>
    </declare-styleable>
    <declare-styleable name="Constraint"><attr name="android:orientation"/><attr name="android:id"/><attr name="android:visibility"/><attr name="visibilityMode"/><attr name="android:alpha"/><attr name="android:elevation"/><attr name="android:rotation"/><attr name="android:rotationX"/><attr name="android:rotationY"/><attr name="android:scaleX"/><attr name="android:scaleY"/><attr name="pivotAnchor"/><attr name="android:transformPivotX"/><attr name="android:transformPivotY"/><attr name="android:translationX"/><attr name="android:translationY"/><attr name="android:translationZ"/><attr name="android:layout_width"/><attr name="android:layout_height"/><attr name="android:layout_marginStart"/><attr name="android:layout_marginBottom"/><attr name="android:layout_marginTop"/><attr name="android:layout_marginEnd"/><attr name="android:layout_marginLeft"/><attr name="android:layout_marginRight"/><attr name="layout_constraintCircle"/><attr name="layout_constraintCircleRadius"/><attr name="layout_constraintCircleAngle"/><attr name="layout_constraintGuide_begin"/><attr name="layout_constraintGuide_end"/><attr name="layout_constraintGuide_percent"/><attr name="layout_constraintLeft_toLeftOf"/><attr name="layout_constraintLeft_toRightOf"/><attr name="layout_constraintRight_toLeftOf"/><attr name="layout_constraintRight_toRightOf"/><attr name="layout_constraintTop_toTopOf"/><attr name="layout_constraintTop_toBottomOf"/><attr name="layout_constraintBottom_toTopOf"/><attr name="layout_constraintBottom_toBottomOf"/><attr name="layout_constraintBaseline_toBaselineOf"/><attr name="layout_constraintStart_toEndOf"/><attr name="layout_constraintStart_toStartOf"/><attr name="layout_constraintEnd_toStartOf"/><attr name="layout_constraintEnd_toEndOf"/><attr name="layout_goneMarginLeft"/><attr name="layout_goneMarginTop"/><attr name="layout_goneMarginRight"/><attr name="layout_goneMarginBottom"/><attr name="layout_goneMarginStart"/><attr name="layout_goneMarginEnd"/><attr name="layout_constrainedWidth"/><attr name="layout_constrainedHeight"/><attr name="layout_constraintHorizontal_bias"/><attr name="layout_constraintVertical_bias"/><attr name="layout_constraintWidth_default"/><attr name="layout_constraintHeight_default"/><attr name="layout_constraintWidth_min"/><attr name="layout_constraintWidth_max"/><attr name="layout_constraintWidth_percent"/><attr name="layout_constraintHeight_min"/><attr name="layout_constraintHeight_max"/><attr name="layout_constraintHeight_percent"/><attr name="layout_constraintLeft_creator"/><attr name="layout_constraintTop_creator"/><attr name="layout_constraintRight_creator"/><attr name="layout_constraintBottom_creator"/><attr name="layout_constraintBaseline_creator"/><attr name="layout_constraintDimensionRatio"/><attr name="layout_constraintHorizontal_weight"/><attr name="layout_constraintVertical_weight"/><attr name="layout_constraintHorizontal_chainStyle"/><attr name="layout_constraintVertical_chainStyle"/><attr name="layout_editor_absoluteX"/><attr name="layout_editor_absoluteY"/><attr name="barrierDirection"/><attr name="barrierMargin"/><attr name="constraint_referenced_ids"/><attr name="android:maxHeight"/><attr name="android:maxWidth"/><attr name="android:minHeight"/><attr name="android:minWidth"/><attr name="barrierAllowsGoneWidgets"/><attr name="chainUseRtl"/><attr name="flow_horizontalStyle"/><attr name="flow_verticalStyle"/><attr name="flow_verticalAlign"/><attr name="flow_horizontalAlign"/><attr name="flow_verticalBias"/><attr name="flow_horizontalBias"/><attr name="flow_wrapMode"/><attr name="flow_maxElementsWrap"/><attr name="flow_horizontalGap"/><attr name="flow_verticalGap"/><attr name="flow_firstHorizontalStyle"/><attr name="flow_firstVerticalStyle"/><attr name="flow_firstHorizontalBias"/><attr name="flow_firstVerticalBias"/><attr name="flow_lastHorizontalStyle"/><attr name="flow_lastVerticalStyle"/><attr name="flow_lastHorizontalBias"/><attr name="flow_lastVerticalBias"/><attr name="animate_relativeTo"/><attr name="transitionEasing"/><attr name="pathMotionArc"/><attr name="transitionPathRotate"/><attr name="drawPath"/><attr name="motionProgress"/><attr name="layout_constraintTag"/><attr name="motionStagger"/></declare-styleable>
    <declare-styleable name="ConstraintLayout_Layout"><attr name="android:orientation"/><attr name="android:minWidth"/><attr name="android:minHeight"/><attr name="android:maxWidth"/><attr name="android:maxHeight"/><attr name="android:visibility"/><attr name="android:elevation"/><attr name="layout_optimizationLevel"/><attr name="layoutDescription"/><attr name="constraintSet"/><attr name="barrierDirection"/><attr name="barrierAllowsGoneWidgets"/><attr name="barrierMargin"/><attr name="constraint_referenced_ids"/><attr name="chainUseRtl"/><attr name="flow_horizontalStyle"/><attr name="flow_verticalStyle"/><attr name="flow_wrapMode"/><attr name="flow_maxElementsWrap"/><attr name="flow_horizontalGap"/><attr name="flow_verticalGap"/><attr name="android:padding"/><attr name="android:paddingTop"/><attr name="android:paddingBottom"/><attr name="android:paddingLeft"/><attr name="android:paddingRight"/><attr name="android:paddingStart"/><attr name="android:paddingEnd"/><attr name="flow_verticalAlign"/><attr name="flow_horizontalAlign"/><attr name="flow_verticalBias"/><attr name="flow_horizontalBias"/><attr name="flow_firstHorizontalStyle"/><attr name="flow_firstVerticalStyle"/><attr name="flow_firstHorizontalBias"/><attr name="flow_firstVerticalBias"/><attr name="flow_lastHorizontalStyle"/><attr name="flow_lastVerticalStyle"/><attr name="flow_lastHorizontalBias"/><attr name="flow_lastVerticalBias"/><attr name="layout_constraintCircle"/><attr name="layout_constraintCircleRadius"/><attr name="layout_constraintCircleAngle"/><attr name="layout_constraintGuide_begin"/><attr name="layout_constraintGuide_end"/><attr name="layout_constraintGuide_percent"/><attr name="layout_constraintLeft_toLeftOf"/><attr name="layout_constraintLeft_toRightOf"/><attr name="layout_constraintRight_toLeftOf"/><attr name="layout_constraintRight_toRightOf"/><attr name="layout_constraintTop_toTopOf"/><attr name="layout_constraintTop_toBottomOf"/><attr name="layout_constraintBottom_toTopOf"/><attr name="layout_constraintBottom_toBottomOf"/><attr name="layout_constraintBaseline_toBaselineOf"/><attr name="layout_constraintStart_toEndOf"/><attr name="layout_constraintStart_toStartOf"/><attr name="layout_constraintEnd_toStartOf"/><attr name="layout_constraintEnd_toEndOf"/><attr name="layout_goneMarginLeft"/><attr name="layout_goneMarginTop"/><attr name="layout_goneMarginRight"/><attr name="layout_goneMarginBottom"/><attr name="layout_goneMarginStart"/><attr name="layout_goneMarginEnd"/><attr name="layout_constrainedWidth"/><attr name="layout_constrainedHeight"/><attr name="layout_constraintHorizontal_bias"/><attr name="layout_constraintVertical_bias"/><attr name="layout_constraintWidth_default"/><attr name="layout_constraintHeight_default"/><attr name="layout_constraintWidth_min"/><attr name="layout_constraintWidth_max"/><attr name="layout_constraintWidth_percent"/><attr name="layout_constraintHeight_min"/><attr name="layout_constraintHeight_max"/><attr name="layout_constraintHeight_percent"/><attr name="layout_constraintLeft_creator"/><attr name="layout_constraintTop_creator"/><attr name="layout_constraintRight_creator"/><attr name="layout_constraintBottom_creator"/><attr name="layout_constraintBaseline_creator"/><attr name="layout_constraintDimensionRatio"/><attr name="layout_constraintHorizontal_weight"/><attr name="layout_constraintVertical_weight"/><attr name="layout_constraintHorizontal_chainStyle"/><attr name="layout_constraintVertical_chainStyle"/><attr name="layout_editor_absoluteX"/><attr name="layout_editor_absoluteY"/><attr name="layout_constraintTag"/></declare-styleable>
    <declare-styleable name="ConstraintLayout_placeholder"><attr name="placeholder_emptyVisibility"/><attr name="content"/></declare-styleable>
    <declare-styleable name="ConstraintSet"><attr format="reference" name="deriveConstraintsFrom"/><attr name="android:orientation"/><attr name="android:id"/><attr name="android:visibility"/><attr name="android:alpha"/><attr name="android:elevation"/><attr name="android:rotation"/><attr name="android:rotationX"/><attr name="android:rotationY"/><attr name="android:scaleX"/><attr name="android:scaleY"/><attr name="pivotAnchor"/><attr name="android:pivotX"/><attr name="android:pivotY"/><attr name="android:transformPivotX"/><attr name="android:transformPivotY"/><attr name="android:translationX"/><attr name="android:translationY"/><attr name="android:translationZ"/><attr name="android:layout_width"/><attr name="android:layout_height"/><attr name="android:layout_marginStart"/><attr name="android:layout_marginBottom"/><attr name="android:layout_marginTop"/><attr name="android:layout_marginEnd"/><attr name="android:layout_marginLeft"/><attr name="android:layout_marginRight"/><attr name="layout_constraintCircle"/><attr name="layout_constraintCircleRadius"/><attr name="layout_constraintCircleAngle"/><attr name="layout_constraintGuide_begin"/><attr name="layout_constraintGuide_end"/><attr name="layout_constraintGuide_percent"/><attr name="layout_constraintLeft_toLeftOf"/><attr name="layout_constraintLeft_toRightOf"/><attr name="layout_constraintRight_toLeftOf"/><attr name="layout_constraintRight_toRightOf"/><attr name="layout_constraintTop_toTopOf"/><attr name="layout_constraintTop_toBottomOf"/><attr name="layout_constraintBottom_toTopOf"/><attr name="layout_constraintBottom_toBottomOf"/><attr name="layout_constraintBaseline_toBaselineOf"/><attr name="layout_constraintStart_toEndOf"/><attr name="layout_constraintStart_toStartOf"/><attr name="layout_constraintEnd_toStartOf"/><attr name="layout_constraintEnd_toEndOf"/><attr name="layout_goneMarginLeft"/><attr name="layout_goneMarginTop"/><attr name="layout_goneMarginRight"/><attr name="layout_goneMarginBottom"/><attr name="layout_goneMarginStart"/><attr name="layout_goneMarginEnd"/><attr name="layout_constrainedWidth"/><attr name="layout_constrainedHeight"/><attr name="layout_constraintHorizontal_bias"/><attr name="layout_constraintVertical_bias"/><attr name="layout_constraintWidth_default"/><attr name="layout_constraintHeight_default"/><attr name="layout_constraintWidth_min"/><attr name="layout_constraintWidth_max"/><attr name="layout_constraintWidth_percent"/><attr name="layout_constraintHeight_min"/><attr name="layout_constraintHeight_max"/><attr name="layout_constraintHeight_percent"/><attr name="layout_constraintLeft_creator"/><attr name="layout_constraintTop_creator"/><attr name="layout_constraintRight_creator"/><attr name="layout_constraintBottom_creator"/><attr name="layout_constraintBaseline_creator"/><attr name="layout_constraintDimensionRatio"/><attr name="layout_constraintHorizontal_weight"/><attr name="layout_constraintVertical_weight"/><attr name="layout_constraintHorizontal_chainStyle"/><attr name="layout_constraintVertical_chainStyle"/><attr name="layout_editor_absoluteX"/><attr name="layout_editor_absoluteY"/><attr name="barrierDirection"/><attr name="constraint_referenced_ids"/><attr name="barrierMargin"/><attr name="android:maxHeight"/><attr name="android:maxWidth"/><attr name="android:minHeight"/><attr name="android:minWidth"/><attr name="barrierAllowsGoneWidgets"/><attr name="chainUseRtl"/><attr name="flow_horizontalStyle"/><attr name="flow_verticalStyle"/><attr name="flow_verticalAlign"/><attr name="flow_horizontalAlign"/><attr name="flow_verticalBias"/><attr name="flow_horizontalBias"/><attr name="flow_wrapMode"/><attr name="flow_maxElementsWrap"/><attr name="flow_horizontalGap"/><attr name="flow_verticalGap"/><attr name="flow_firstHorizontalStyle"/><attr name="flow_firstVerticalStyle"/><attr name="flow_firstHorizontalBias"/><attr name="flow_firstVerticalBias"/><attr name="flow_lastHorizontalStyle"/><attr name="flow_lastVerticalStyle"/><attr name="flow_lastHorizontalBias"/><attr name="flow_lastVerticalBias"/><attr name="animate_relativeTo"/><attr name="transitionEasing"/><attr name="pathMotionArc"/><attr name="transitionPathRotate"/><attr name="motionStagger"/><attr name="drawPath"/><attr name="motionProgress"/><attr name="layout_constraintTag"/></declare-styleable>
    <declare-styleable name="CoordinatorLayout">
        
        <attr format="reference" name="keylines"/>
        
        <attr format="color|reference" name="statusBarBackground"/>
    </declare-styleable>
    <declare-styleable name="CoordinatorLayout_Layout">
        <attr name="android:layout_gravity"/>
        
        <attr format="string" name="layout_behavior"/>
        
        <attr format="reference" name="layout_anchor"/>
        
        <attr format="integer" name="layout_keyline"/>

        
        <attr name="layout_anchorGravity">
            <!-- Push object to the top of its container, not changing its size. -->
            <flag name="top" value="0x30"/>
            <!-- Push object to the bottom of its container, not changing its size. -->
            <flag name="bottom" value="0x50"/>
            <!-- Push object to the left of its container, not changing its size. -->
            <flag name="left" value="0x03"/>
            <!-- Push object to the right of its container, not changing its size. -->
            <flag name="right" value="0x05"/>
            <!-- Place object in the vertical center of its container, not changing its size. -->
            <flag name="center_vertical" value="0x10"/>
            <!-- Grow the vertical size of the object if needed so it completely fills its container. -->
            <flag name="fill_vertical" value="0x70"/>
            <!-- Place object in the horizontal center of its container, not changing its size. -->
            <flag name="center_horizontal" value="0x01"/>
            <!-- Grow the horizontal size of the object if needed so it completely fills its container. -->
            <flag name="fill_horizontal" value="0x07"/>
            <!-- Place the object in the center of its container in both the vertical and horizontal axis, not changing its size. -->
            <flag name="center" value="0x11"/>
            <!-- Grow the horizontal and vertical size of the object if needed so it completely fills its container. -->
            <flag name="fill" value="0x77"/>
            <!-- Additional option that can be set to have the top and/or bottom edges of
                 the child clipped to its container's bounds.
                 The clip will be based on the vertical gravity: a top gravity will clip the bottom
                 edge, a bottom gravity will clip the top edge, and neither will clip both edges. -->
            <flag name="clip_vertical" value="0x80"/>
            <!-- Additional option that can be set to have the left and/or right edges of
                 the child clipped to its container's bounds.
                 The clip will be based on the horizontal gravity: a left gravity will clip the right
                 edge, a right gravity will clip the left edge, and neither will clip both edges. -->
            <flag name="clip_horizontal" value="0x08"/>
            <!-- Push object to the beginning of its container, not changing its size. -->
            <flag name="start" value="0x00800003"/>
            <!-- Push object to the end of its container, not changing its size. -->
            <flag name="end" value="0x00800005"/>
        </attr>

        
        <attr format="enum" name="layout_insetEdge">
            <!-- Don't inset. -->
            <enum name="none" value="0x0"/>
            <!-- Inset the top edge. -->
            <enum name="top" value="0x30"/>
            <!-- Inset the bottom edge. -->
            <enum name="bottom" value="0x50"/>
            <!-- Inset the left edge. -->
            <enum name="left" value="0x03"/>
            <!-- Inset the right edge. -->
            <enum name="right" value="0x05"/>
            <!-- Inset the start edge. -->
            <enum name="start" value="0x00800003"/>
            <!-- Inset the end edge. -->
            <enum name="end" value="0x00800005"/>
        </attr>
        
        <attr name="layout_dodgeInsetEdges">
            <!-- Don't dodge any edges -->
            <flag name="none" value="0x0"/>
            <!-- Dodge the top inset edge. -->
            <flag name="top" value="0x30"/>
            <!-- Dodge the bottom inset edge. -->
            <flag name="bottom" value="0x50"/>
            <!-- Dodge the left inset edge. -->
            <flag name="left" value="0x03"/>
            <!-- Dodge the right inset edge. -->
            <flag name="right" value="0x05"/>
            <!-- Dodge the start inset edge. -->
            <flag name="start" value="0x00800003"/>
            <!-- Dodge the end inset edge. -->
            <flag name="end" value="0x00800005"/>
            <!-- Dodge all the inset edges. -->
            <flag name="all" value="0x77"/>
        </attr>
    </declare-styleable>
    <declare-styleable name="CustomAttribute"><attr format="string" name="attributeName"/><attr format="color" name="customColorValue"/><attr format="color" name="customColorDrawableValue"/><attr format="integer" name="customIntegerValue"/><attr format="float" name="customFloatValue"/><attr format="string" name="customStringValue"/><attr format="dimension" name="customDimension"/><attr format="dimension" name="customPixelDimension"/><attr format="boolean" name="customBoolean"/></declare-styleable>
    <declare-styleable name="DrawerArrowToggle">
        
        <attr format="color" name="color"/>
        
        <attr format="boolean" name="spinBars"/>
        
        <attr format="dimension" name="drawableSize"/>
        
        <attr format="dimension" name="gapBetweenBars"/>
        
        <attr format="dimension" name="arrowHeadLength"/>
        
        <attr format="dimension" name="arrowShaftLength"/>
        
        <attr format="dimension" name="barLength"/>
        
        <attr format="dimension" name="thickness"/>
    </declare-styleable>
    <declare-styleable name="ExtendedFloatingActionButton">
    
    <attr name="elevation"/>
    
    <attr name="showMotionSpec"/>
    
    <attr name="hideMotionSpec"/>
    
    <attr format="reference" name="extendMotionSpec"/>
    
    <attr format="reference" name="shrinkMotionSpec"/>
  </declare-styleable>
    <declare-styleable name="ExtendedFloatingActionButton_Behavior_Layout">
    
    <attr name="behavior_autoHide"/>
    
    <attr format="boolean" name="behavior_autoShrink"/>
  </declare-styleable>
    <declare-styleable name="FloatingActionButton">
    
    <attr name="android:enabled"/>
    
    <attr name="backgroundTint"/>
    <attr name="backgroundTintMode"/>

    
    <attr name="rippleColor"/>

    
    <attr name="fabSize">
      <!-- A size which will change based on the window size. -->
      <enum name="auto" value="-1"/>
      <!-- The normal sized button. -->
      <enum name="normal" value="0"/>
      <!-- The mini sized button. -->
      <enum name="mini" value="1"/>
    </attr>
    
    <attr format="dimension" name="fabCustomSize"/>
    
    <attr name="elevation"/>
    
    <attr name="ensureMinTouchTargetSize"/>
    
    <attr format="dimension" name="hoveredFocusedTranslationZ"/>
    
    <attr format="dimension" name="pressedTranslationZ"/>
    
    <attr format="dimension" name="borderWidth"/>
    
    <attr format="boolean" name="useCompatPadding"/>
    
    <attr format="dimension" name="maxImageSize"/>
    
    <attr name="showMotionSpec"/>
    
    <attr name="hideMotionSpec"/>
    
    <attr name="shapeAppearance"/>
    
    <attr name="shapeAppearanceOverlay"/>
  </declare-styleable>
    <declare-styleable name="FloatingActionButton_Behavior_Layout">
    
    <attr format="boolean" name="behavior_autoHide"/>
  </declare-styleable>
    <declare-styleable name="FlowLayout">
    
    <attr format="dimension" name="itemSpacing"/>
    
    <attr format="dimension" name="lineSpacing"/>
  </declare-styleable>
    <declare-styleable name="FontFamily">
        
        <attr format="string" name="fontProviderAuthority"/>
        
        <attr format="string" name="fontProviderPackage"/>
        
        <attr format="string" name="fontProviderQuery"/>
        
        <attr format="reference" name="fontProviderCerts"/>
        
        <attr name="fontProviderFetchStrategy">
            <!-- The blocking font fetch works as follows.
              First, check the local cache, then if the requested font is not cached, request the
              font from the provider and wait until it is finished.  You can change the length of
              the timeout by modifying fontProviderFetchTimeout.  If the timeout happens, the
              default typeface will be used instead. -->
            <enum name="blocking" value="0"/>
            <!-- The async font fetch works as follows.
              First, check the local cache, then if the requeted font is not cached, trigger a
              request the font and continue with layout inflation. Once the font fetch succeeds, the
              target text view will be refreshed with the downloaded font data. The
              fontProviderFetchTimeout will be ignored if async loading is specified. -->
            <enum name="async" value="1"/>
        </attr>
        
        <attr format="integer" name="fontProviderFetchTimeout">
            <!-- A special value for the timeout. In this case, the blocking font fetching will not
              timeout and wait until a reply is received from the font provider. -->
            <enum name="forever" value="-1"/>
        </attr>
    </declare-styleable>
    <declare-styleable name="FontFamilyFont">
        
        <attr name="fontStyle">
            <enum name="normal" value="0"/>
            <enum name="italic" value="1"/>
        </attr>
        
        <attr format="reference" name="font"/>
        
        <attr format="integer" name="fontWeight"/>
        
        <attr format="string" name="fontVariationSettings"/>
        
        <attr format="integer" name="ttcIndex"/>
        
        <attr name="android:fontStyle"/>
        <attr name="android:font"/>
        <attr name="android:fontWeight"/>
        <attr name="android:fontVariationSettings"/>
        <attr name="android:ttcIndex"/>
    </declare-styleable>
    <declare-styleable name="ForegroundLinearLayout">
    <attr name="android:foreground"/>
    <attr name="android:foregroundGravity"/>
    
    <attr format="boolean" name="foregroundInsidePadding"/>
  </declare-styleable>
    <declare-styleable name="Fragment">
        <attr name="android:name"/>
        <attr name="android:id"/>
        <attr name="android:tag"/>
    </declare-styleable>
    <declare-styleable name="FragmentContainerView">
        <attr name="android:name"/>
        <attr name="android:tag"/>
    </declare-styleable>
    <declare-styleable name="GradientColor">
        
        <attr name="android:startColor"/>
        
        <attr name="android:centerColor"/>
        
        <attr name="android:endColor"/>
        
        <attr name="android:type"/>

        
        
        <attr name="android:gradientRadius"/>

        
        
        <attr name="android:centerX"/>
        
        <attr name="android:centerY"/>

        
        
        <attr name="android:startX"/>
        
        <attr name="android:startY"/>
        
        <attr name="android:endX"/>
        
        <attr name="android:endY"/>

        
        <attr name="android:tileMode"/>
    </declare-styleable>
    <declare-styleable name="GradientColorItem">
        
        <attr name="android:offset"/>
        
        <attr name="android:color"/>
    </declare-styleable>
    <declare-styleable name="ImageFilterView"><attr format="reference" name="altSrc"/><attr format="float" name="saturation"/><attr format="float" name="brightness"/><attr format="float" name="warmth"/><attr format="float" name="contrast"/><attr format="float" name="crossfade"/><attr format="dimension" name="round"/><attr format="boolean" name="overlay"/><attr format="float" name="roundPercent"/></declare-styleable>
    <declare-styleable name="Insets">
    
    <attr format="boolean" name="paddingBottomSystemWindowInsets"/>
    <attr format="boolean" name="paddingLeftSystemWindowInsets"/>
    <attr format="boolean" name="paddingRightSystemWindowInsets"/>
  </declare-styleable>
    <declare-styleable name="KeyAttribute"><attr name="framePosition"/><attr name="motionTarget"/><attr name="transitionEasing"/><attr name="curveFit"/><attr name="motionProgress"/><attr name="android:alpha"/><attr name="android:elevation"/><attr name="android:rotation"/><attr name="android:rotationX"/><attr name="android:rotationY"/><attr name="android:transformPivotX"/><attr name="android:transformPivotY"/><attr name="transitionPathRotate"/><attr name="android:scaleX"/><attr name="android:scaleY"/><attr name="android:translationX"/><attr name="android:translationY"/><attr name="android:translationZ"/></declare-styleable>
    <declare-styleable name="KeyCycle"><attr name="motionTarget"/><attr name="curveFit"/><attr name="framePosition"/><attr name="transitionEasing"/><attr name="motionProgress"/><attr name="waveShape"/><attr name="wavePeriod"/><attr name="waveOffset"/><attr name="waveVariesBy"/><attr name="transitionPathRotate"/><attr name="android:alpha"/><attr name="android:elevation"/><attr name="android:rotation"/><attr name="android:rotationX"/><attr name="android:rotationY"/><attr name="android:scaleX"/><attr name="android:scaleY"/><attr name="android:translationX"/><attr name="android:translationY"/><attr name="android:translationZ"/></declare-styleable>
    <declare-styleable name="KeyFrame"/>
    <declare-styleable name="KeyFramesAcceleration"/>
    <declare-styleable name="KeyFramesVelocity"/>
    <declare-styleable name="KeyPosition"><attr format="enum" name="keyPositionType">
            <enum name="deltaRelative" value="0"/>
            <enum name="pathRelative" value="1"/>
            <enum name="parentRelative" value="2"/>
        </attr><attr format="float" name="percentX"/><attr format="float" name="percentY"/><attr format="float" name="percentWidth"/><attr format="float" name="percentHeight"/><attr name="framePosition"/><attr name="motionTarget"/><attr name="transitionEasing"/><attr name="pathMotionArc"/><attr name="curveFit"/><attr name="drawPath"/><attr name="sizePercent"/></declare-styleable>
    <declare-styleable name="KeyTimeCycle"><attr name="framePosition"/><attr name="motionTarget"/><attr name="transitionEasing"/><attr name="curveFit"/><attr name="waveShape"/><attr name="wavePeriod"/><attr name="motionProgress"/><attr name="waveOffset"/><attr name="waveDecay"/><attr name="android:alpha"/><attr name="android:elevation"/><attr name="android:rotation"/><attr name="android:rotationX"/><attr name="android:rotationY"/><attr name="transitionPathRotate"/><attr name="android:scaleX"/><attr name="android:scaleY"/><attr name="android:translationX"/><attr name="android:translationY"/><attr name="android:translationZ"/></declare-styleable>
    <declare-styleable name="KeyTrigger"><attr name="framePosition"/><attr name="motionTarget"/><attr format="reference" name="triggerReceiver"/><attr format="string" name="onNegativeCross"/><attr format="string" name="onPositiveCross"/><attr format="string" name="onCross"/><attr format="float" name="triggerSlack"/><attr format="reference" name="triggerId"/><attr format="boolean" name="motion_postLayoutCollision"/><attr format="reference" name="motion_triggerOnCollision"/></declare-styleable>
    <declare-styleable name="Layout"><attr name="android:layout_width"/><attr name="android:layout_height"/><attr name="android:layout_marginStart"/><attr name="android:layout_marginBottom"/><attr name="android:layout_marginTop"/><attr name="android:layout_marginEnd"/><attr name="android:layout_marginLeft"/><attr name="android:layout_marginRight"/><attr name="layout_constraintCircle"/><attr name="layout_constraintCircleRadius"/><attr name="layout_constraintCircleAngle"/><attr name="layout_constraintGuide_begin"/><attr name="layout_constraintGuide_end"/><attr name="layout_constraintGuide_percent"/><attr name="layout_constraintLeft_toLeftOf"/><attr name="layout_constraintLeft_toRightOf"/><attr name="layout_constraintRight_toLeftOf"/><attr name="layout_constraintRight_toRightOf"/><attr name="layout_constraintTop_toTopOf"/><attr name="layout_constraintTop_toBottomOf"/><attr name="layout_constraintBottom_toTopOf"/><attr name="layout_constraintBottom_toBottomOf"/><attr name="layout_constraintBaseline_toBaselineOf"/><attr name="layout_constraintStart_toEndOf"/><attr name="layout_constraintStart_toStartOf"/><attr name="layout_constraintEnd_toStartOf"/><attr name="layout_constraintEnd_toEndOf"/><attr name="layout_goneMarginLeft"/><attr name="layout_goneMarginTop"/><attr name="layout_goneMarginRight"/><attr name="layout_goneMarginBottom"/><attr name="layout_goneMarginStart"/><attr name="layout_goneMarginEnd"/><attr name="layout_constrainedWidth"/><attr name="layout_constrainedHeight"/><attr name="layout_constraintHorizontal_bias"/><attr name="layout_constraintVertical_bias"/><attr name="layout_constraintWidth_default"/><attr name="layout_constraintHeight_default"/><attr name="layout_constraintWidth_min"/><attr name="layout_constraintWidth_max"/><attr name="android:orientation"/><attr name="layout_constraintWidth_percent"/><attr name="layout_constraintHeight_min"/><attr name="layout_constraintHeight_max"/><attr name="layout_constraintHeight_percent"/><attr name="layout_constraintLeft_creator"/><attr name="layout_constraintTop_creator"/><attr name="layout_constraintRight_creator"/><attr name="layout_constraintBottom_creator"/><attr name="layout_constraintBaseline_creator"/><attr name="layout_constraintDimensionRatio"/><attr name="layout_constraintHorizontal_weight"/><attr name="layout_constraintVertical_weight"/><attr name="layout_constraintHorizontal_chainStyle"/><attr name="layout_constraintVertical_chainStyle"/><attr name="layout_editor_absoluteX"/><attr name="layout_editor_absoluteY"/><attr name="barrierDirection"/><attr name="barrierMargin"/><attr name="constraint_referenced_ids"/><attr format="dimension" name="maxHeight"/><attr format="dimension" name="maxWidth"/><attr format="dimension" name="minHeight"/><attr format="dimension" name="minWidth"/><attr name="barrierAllowsGoneWidgets"/><attr name="chainUseRtl"/></declare-styleable>
    <declare-styleable name="LinearLayoutCompat">
        
        <attr name="android:orientation"/>
        <attr name="android:gravity"/>
        
        <attr name="android:baselineAligned"/>
        
        <attr name="android:baselineAlignedChildIndex"/>
        
        <attr name="android:weightSum"/>
        
        <attr format="boolean" name="measureWithLargestChild"/>
        
        <attr name="divider"/>
        
        <attr name="showDividers">
            <flag name="none" value="0"/>
            <flag name="beginning" value="1"/>
            <flag name="middle" value="2"/>
            <flag name="end" value="4"/>
        </attr>
        
        <attr format="dimension" name="dividerPadding"/>
    </declare-styleable>
    <declare-styleable name="LinearLayoutCompat_Layout">
        <attr name="android:layout_width"/>
        <attr name="android:layout_height"/>
        <attr name="android:layout_weight"/>
        <attr name="android:layout_gravity"/>
    </declare-styleable>
    <declare-styleable name="ListPopupWindow">
        
        <attr name="android:dropDownVerticalOffset"/>
        
        <attr name="android:dropDownHorizontalOffset"/>
    </declare-styleable>
    <declare-styleable name="MaterialAlertDialog">
    <attr format="dimension" name="backgroundInsetStart"/>
    <attr format="dimension" name="backgroundInsetTop"/>
    <attr format="dimension" name="backgroundInsetEnd"/>
    <attr format="dimension" name="backgroundInsetBottom"/>
  </declare-styleable>
    <declare-styleable name="MaterialAlertDialogTheme">
    <attr format="reference" name="materialAlertDialogTheme"/>
    <attr format="reference" name="materialAlertDialogTitlePanelStyle"/>
    <attr format="reference" name="materialAlertDialogTitleIconStyle"/>
    <attr format="reference" name="materialAlertDialogTitleTextStyle"/>
    <attr format="reference" name="materialAlertDialogBodyTextStyle"/>
  </declare-styleable>
    <declare-styleable name="MaterialAutoCompleteTextView" parent="AppCompatAutoCompleteTextView">
    <attr name="android:inputType"/>
  </declare-styleable>
    <declare-styleable name="MaterialButton">
    
    <attr name="android:checkable"/>
    <attr name="android:insetLeft"/>
    <attr name="android:insetRight"/>
    <attr name="android:insetTop"/>
    <attr name="android:insetBottom"/>
    
    <attr name="android:background"/>
    <attr name="backgroundTint"/>
    <attr name="backgroundTintMode"/>
    
    <attr name="elevation"/>
    
    <attr format="reference" name="icon"/>
    
    <attr format="dimension" name="iconSize"/>
    
    <attr format="dimension" name="iconPadding"/>
    
    <attr name="iconGravity">
      <!-- Push icon to the start of the button. -->
      <flag name="start" value="0x1"/>
      <!-- Push the icon to the start of the text keeping a distance equal to
           {@link R.attr#iconPadding} from the text. -->
      <flag name="textStart" value="0x2"/>
      <!-- Push icon to the end of the button. -->
      <flag name="end" value="0x3"/>
      <!-- Push the icon to the end of the text keeping a distance equal to
           {@link R.attr#iconPadding} from the text. -->
      <flag name="textEnd" value="0x4"/>
    </attr>
    
    <attr format="color" name="iconTint"/>
    
    <attr name="iconTintMode"/>
    
    <attr name="shapeAppearance"/>
    
    <attr name="shapeAppearanceOverlay"/>
    
    <attr name="strokeColor"/>
    
    <attr name="strokeWidth"/>
    
    <attr format="dimension" name="cornerRadius"/>
    
    <attr name="rippleColor"/>
  </declare-styleable>
    <declare-styleable name="MaterialButtonToggleGroup">
    
    <attr name="singleSelection"/>

    
    <attr name="selectionRequired"/>
    
    <attr format="reference" name="checkedButton"/>
  </declare-styleable>
    <declare-styleable name="MaterialCalendar">
    <attr name="android:windowFullscreen"/>
    <attr format="reference" name="dayStyle"/>
    <attr format="reference" name="dayInvalidStyle"/>
    <attr format="reference" name="daySelectedStyle"/>
    <attr format="reference" name="dayTodayStyle"/>
    <attr format="reference" name="yearStyle"/>
    <attr format="reference" name="yearSelectedStyle"/>
    <attr format="reference" name="yearTodayStyle"/>
    <attr format="color" name="rangeFillColor"/>
  </declare-styleable>
    <declare-styleable name="MaterialCalendarItem">
    <attr name="android:insetLeft"/>
    <attr name="android:insetTop"/>
    <attr name="android:insetRight"/>
    <attr name="android:insetBottom"/>
    <attr format="color" name="itemFillColor"/>
    <attr name="itemTextColor"/>
    <attr format="color" name="itemStrokeColor"/>
    <attr format="dimension" name="itemStrokeWidth"/>
    <attr name="itemShapeAppearance"/>
    <attr name="itemShapeAppearanceOverlay"/>
  </declare-styleable>
    <declare-styleable name="MaterialCardView">
    
    <attr name="android:checkable"/>
    
    <attr format="color" name="cardForegroundColor"/>
    
    <attr name="checkedIcon"/>
    
    <attr name="checkedIconTint"/>
    
    <attr name="rippleColor"/>
    
    <attr format="boolean" name="state_dragged"/>
    
    <attr name="strokeColor"/>
    
    <attr name="strokeWidth"/>

    
    <attr name="shapeAppearance"/>
    
    <attr name="shapeAppearanceOverlay"/>
  </declare-styleable>
    <declare-styleable name="MaterialCheckBox">
    
    <attr name="useMaterialThemeColors"/>
    
    <attr name="buttonTint"/>
  </declare-styleable>
    <declare-styleable name="MaterialRadioButton">
    
    <attr name="useMaterialThemeColors"/>
    
    <attr name="buttonTint"/>
  </declare-styleable>
    <declare-styleable name="MaterialShape">
    
    <attr format="reference" name="shapeAppearance"/>
    
    <attr format="reference" name="shapeAppearanceOverlay"/>
  </declare-styleable>
    <declare-styleable name="MaterialTextAppearance" parent="TextAppearance">
    
    <attr name="android:lineHeight"/>
    <attr name="lineHeight"/>
  </declare-styleable>
    <declare-styleable name="MaterialTextView" parent="AppCompatTextView">
    <attr name="android:textAppearance"/>
    <attr name="android:lineHeight"/>
    <attr name="lineHeight"/>
  </declare-styleable>
    <declare-styleable name="MenuGroup">

        
        <attr name="android:id"/>

        
        <attr name="android:menuCategory"/>

        
        <attr name="android:orderInCategory"/>

        
        <attr name="android:checkableBehavior"/>

        
        <attr name="android:visible"/>

        
        <attr name="android:enabled"/>

    </declare-styleable>
    <declare-styleable name="MenuItem">

        
        <attr name="android:id"/>

        
        <attr name="android:menuCategory"/>

        
        <attr name="android:orderInCategory"/>

        
        <attr name="android:title"/>

        
        <attr name="android:titleCondensed"/>

        
        <attr name="android:icon"/>

        
        <attr name="android:alphabeticShortcut"/>

        
        <attr name="alphabeticModifiers">
            <flag name="META" value="0x10000"/>
            <flag name="CTRL" value="0x1000"/>
            <flag name="ALT" value="0x02"/>
            <flag name="SHIFT" value="0x1"/>
            <flag name="SYM" value="0x4"/>
            <flag name="FUNCTION" value="0x8"/>
        </attr>

        
        <attr name="android:numericShortcut"/>

        
        <attr name="numericModifiers">
            <flag name="META" value="0x10000"/>
            <flag name="CTRL" value="0x1000"/>
            <flag name="ALT" value="0x02"/>
            <flag name="SHIFT" value="0x1"/>
            <flag name="SYM" value="0x4"/>
            <flag name="FUNCTION" value="0x8"/>
        </attr>

        
        <attr name="android:checkable"/>

        
        <attr name="android:checked"/>

        
        <attr name="android:visible"/>

        
        <attr name="android:enabled"/>

        
        <attr name="android:onClick"/>

        
        <attr name="showAsAction">
            <!-- Never show this item in an action bar, show it in the overflow menu instead.
                 Mutually exclusive with "ifRoom" and "always". -->
            <flag name="never" value="0"/>
            <!-- Show this item in an action bar if there is room for it as determined
                 by the system. Favor this option over "always" where possible.
                 Mutually exclusive with "never" and "always". -->
            <flag name="ifRoom" value="1"/>
            <!-- Always show this item in an actionbar, even if it would override
                 the system's limits of how much stuff to put there. This may make
                 your action bar look bad on some screens. In most cases you should
                 use "ifRoom" instead. Mutually exclusive with "ifRoom" and "never". -->
            <flag name="always" value="2"/>
            <!-- When this item is shown as an action in the action bar, show a text
                 label with it even if it has an icon representation. -->
            <flag name="withText" value="4"/>
            <!-- This item's action view collapses to a normal menu
                 item. When expanded, the action view takes over a
                 larger segment of its container. -->
            <flag name="collapseActionView" value="8"/>
        </attr>

        
        <attr format="reference" name="actionLayout"/>

        
        <attr format="string" name="actionViewClass"/>

        
        <attr format="string" name="actionProviderClass"/>

        
        <attr format="string" name="contentDescription"/>

        
        <attr format="string" name="tooltipText"/>

        
        <attr format="color" name="iconTint"/>

        
        <attr name="iconTintMode">
            <!-- The tint is drawn on top of the icon.
                 [Sa + (1 - Sa)*Da, Rc = Sc + (1 - Sa)*Dc] -->
            <enum name="src_over" value="3"/>
            <!-- The tint is masked by the alpha channel of the icon. The icon’s
                 color channels are thrown out. [Sa * Da, Sc * Da] -->
            <enum name="src_in" value="5"/>
            <!-- The tint is drawn above the icon, but with the icon’s alpha
                 channel masking the result. [Da, Sc * Da + (1 - Sa) * Dc] -->
            <enum name="src_atop" value="9"/>
            <!-- Multiplies the color and alpha channels of the icon with those of
                 the tint. [Sa * Da, Sc * Dc] -->
            <enum name="multiply" value="14"/>
            <!-- [Sa + Da - Sa * Da, Sc + Dc - Sc * Dc] -->
            <enum name="screen" value="15"/>
            <!-- Combines the tint and icon color and alpha channels, clamping the
                 result to valid color values. Saturate(S + D) -->
            <enum name="add" value="16"/>
        </attr>

    </declare-styleable>
    <declare-styleable name="MenuView">
        
        <attr name="android:itemTextAppearance"/>
        
        <attr name="android:horizontalDivider"/>
        
        <attr name="android:verticalDivider"/>
        
        <attr name="android:headerBackground"/>
        
        <attr name="android:itemBackground"/>
        
        <attr name="android:windowAnimationStyle"/>
        
        <attr name="android:itemIconDisabledAlpha"/>
        
        <attr format="boolean" name="preserveIconSpacing"/>
        
        <attr format="reference" name="subMenuArrow"/>
    </declare-styleable>
    <declare-styleable name="MockView"><attr format="string" name="mock_label"/><attr format="color" name="mock_labelColor"/><attr format="color" name="mock_labelBackgroundColor"/><attr format="color" name="mock_diagonalsColor"/><attr format="boolean" name="mock_showDiagonals"/><attr format="boolean" name="mock_showLabel"/></declare-styleable>
    <declare-styleable name="Motion"><attr name="animate_relativeTo"/><attr name="transitionEasing"/><attr name="pathMotionArc"/><attr name="motionPathRotate"/><attr name="motionStagger"/><attr name="drawPath"/></declare-styleable>
    <declare-styleable name="MotionHelper"><attr format="boolean" name="onShow"/><attr format="boolean" name="onHide"/></declare-styleable>
    <declare-styleable name="MotionLayout"><attr name="layoutDescription"/><attr format="reference" name="currentState"/><attr name="motionProgress"/><attr format="boolean" name="applyMotionScene"/><attr format="boolean" name="showPaths"/><attr format="enum" name="motionDebug">
            <enum name="NO_DEBUG" value="0"/>
            <enum name="SHOW_PROGRESS" value="1"/>
            <enum name="SHOW_PATH" value="2"/>
            <enum name="SHOW_ALL" value="3"/>
        </attr></declare-styleable>
    <declare-styleable name="MotionScene"><attr name="defaultDuration"/><attr name="layoutDuringTransition"/></declare-styleable>
    <declare-styleable name="MotionTelltales"><attr format="enum" name="telltales_velocityMode">
            <enum name="layout" value="0"/>
            <enum name="postLayout" value="1"/>
            <enum name="staticPostLayout" value="2"/>
            <enum name="staticLayout" value="3"/>
        </attr><attr format="color" name="telltales_tailColor"/><attr format="float" name="telltales_tailScale"/></declare-styleable>
    <declare-styleable name="NavigationView">
    <attr name="android:background"/>
    <attr name="android:fitsSystemWindows"/>
    <attr name="android:maxWidth"/>
    <attr name="elevation"/>
    
    <attr format="reference" name="menu"/>
    <attr format="color" name="itemIconTint"/>
    <attr name="itemTextColor"/>
    
    <attr format="reference" name="itemBackground"/>
    <attr format="reference" name="itemTextAppearance"/>
    
    <attr format="reference" name="headerLayout"/>
    
    <attr format="dimension" name="itemHorizontalPadding"/>
    
    <attr format="dimension" name="itemIconPadding"/>
    
    <attr format="dimension" name="itemIconSize"/>
    
    <attr format="integer" min="1" name="itemMaxLines"/>
    
    <attr name="itemShapeAppearance"/>
    
    <attr name="itemShapeAppearanceOverlay"/>
    
    <attr format="dimension" name="itemShapeInsetStart"/>
    
    <attr format="dimension" name="itemShapeInsetTop"/>
    
    <attr format="dimension" name="itemShapeInsetEnd"/>
    
    <attr format="dimension" name="itemShapeInsetBottom"/>
    
    <attr format="color" name="itemShapeFillColor"/>
  </declare-styleable>
    <declare-styleable name="OnClick"><attr name="targetId"/><attr name="clickAction">
            <flag name="toggle" value="0x0011"/>
            <flag name="transitionToEnd" value="0x0001"/>
            <flag name="transitionToStart" value="0x0010"/>
            <flag name="jumpToEnd" value="0x100"/>
            <flag name="jumpToStart" value="0x1000"/>
        </attr></declare-styleable>
    <declare-styleable name="OnSwipe"><attr format="float" name="dragScale"/><attr format="float" name="dragThreshold"/><attr format="float" name="maxVelocity"/><attr format="float" name="maxAcceleration"/><attr name="dragDirection"/><attr name="touchAnchorId"/><attr name="touchAnchorSide"/><attr format="reference" name="touchRegionId"/><attr format="reference" name="limitBoundsTo"/><attr format="flags" name="nestedScrollFlags">
            <flag name="none" value="0"/>
            <flag name="disablePostScroll" value="1"/>
            <flag name="disableScroll" value="2"/>
        </attr><attr format="boolean" name="moveWhenScrollAtTop"/><attr format="enum" name="onTouchUp">
            <enum name="autoComplete" value="0"/>
            <enum name="autoCompleteToStart" value="1"/>
            <enum name="autoCompleteToEnd" value="2"/>
            <enum name="stop" value="3"/>
            <enum name="decelerate" value="4"/>
            <enum name="decelerateAndComplete" value="5"/>
        </attr></declare-styleable>
    <declare-styleable name="PopupWindow">
        
        <attr format="boolean" name="overlapAnchor"/>
        <attr name="android:popupBackground"/>
        <attr name="android:popupAnimationStyle"/>
    </declare-styleable>
    <declare-styleable name="PopupWindowBackgroundState">
        
        <attr format="boolean" name="state_above_anchor"/>
    </declare-styleable>
    <declare-styleable name="PropertySet"><attr name="android:visibility"/><attr name="visibilityMode"/><attr format="float" name="android:alpha"/><attr name="motionProgress"/><attr name="layout_constraintTag"/></declare-styleable>
    <declare-styleable name="RangeSlider">
    <attr format="reference" name="values"/>
  </declare-styleable>
    <declare-styleable name="RecycleListView">
        
        <attr format="dimension" name="paddingBottomNoButtons"/>
        
        <attr format="dimension" name="paddingTopNoTitle"/>
    </declare-styleable>
    <declare-styleable name="RecyclerView">
        
        <attr format="string" name="layoutManager"/>

        
        
        
        <eat-comment/>

        <attr name="android:orientation"/>
        <attr name="android:descendantFocusability"/>
        <attr name="android:clipToPadding"/>
        <attr format="integer" name="spanCount"/>
        <attr format="boolean" name="reverseLayout"/>
        <attr format="boolean" name="stackFromEnd"/>
        <attr format="boolean" name="fastScrollEnabled"/>
        <attr format="reference" name="fastScrollVerticalThumbDrawable"/>
        <attr format="reference" name="fastScrollVerticalTrackDrawable"/>
        <attr format="reference" name="fastScrollHorizontalThumbDrawable"/>
        <attr format="reference" name="fastScrollHorizontalTrackDrawable"/>
    </declare-styleable>
    <declare-styleable name="ScrimInsetsFrameLayout">
    <attr format="color|reference" name="insetForeground"/>
  </declare-styleable>
    <declare-styleable name="ScrollingViewBehavior_Layout">
    
    <attr format="dimension" name="behavior_overlapTop"/>
  </declare-styleable>
    <declare-styleable name="SearchView">
        
        <attr format="reference" name="layout"/>
        
        <attr format="boolean" name="iconifiedByDefault"/>
        
        <attr name="android:maxWidth"/>
        
        <attr format="string" name="queryHint"/>
        
        <attr format="string" name="defaultQueryHint"/>
        
        <attr name="android:imeOptions"/>
        
        <attr name="android:inputType"/>
        
        <attr format="reference" name="closeIcon"/>
        
        <attr format="reference" name="goIcon"/>
        
        <attr format="reference" name="searchIcon"/>
        
        <attr format="reference" name="searchHintIcon"/>
        
        <attr format="reference" name="voiceIcon"/>
        
        <attr format="reference" name="commitIcon"/>
        
        <attr format="reference" name="suggestionRowLayout"/>
        
        <attr format="reference" name="queryBackground"/>
        
        <attr format="reference" name="submitBackground"/>
        <attr name="android:focusable"/>
    </declare-styleable>
    <declare-styleable name="ShapeAppearance">
    
    <attr format="dimension|fraction" name="cornerSize"/>
    
    <attr format="dimension|fraction" name="cornerSizeTopLeft"/>
    
    <attr format="dimension|fraction" name="cornerSizeTopRight"/>
    
    <attr format="dimension|fraction" name="cornerSizeBottomRight"/>
    
    <attr format="dimension|fraction" name="cornerSizeBottomLeft"/>

    
    <attr format="enum" name="cornerFamily">
      <enum name="rounded" value="0"/>
      <enum name="cut" value="1"/>
    </attr>
    
    <attr format="enum" name="cornerFamilyTopLeft">
      <enum name="rounded" value="0"/>
      <enum name="cut" value="1"/>
    </attr>
    
    <attr format="enum" name="cornerFamilyTopRight">
      <enum name="rounded" value="0"/>
      <enum name="cut" value="1"/>
    </attr>
    
    <attr format="enum" name="cornerFamilyBottomRight">
      <enum name="rounded" value="0"/>
      <enum name="cut" value="1"/>
    </attr>
    
    <attr format="enum" name="cornerFamilyBottomLeft">
      <enum name="rounded" value="0"/>
      <enum name="cut" value="1"/>
    </attr>
  </declare-styleable>
    <declare-styleable name="ShapeableImageView">
    <attr name="strokeWidth"/>
    <attr name="strokeColor"/>

    
    <attr name="shapeAppearance"/>
    
    <attr name="shapeAppearanceOverlay"/>
  </declare-styleable>
    <declare-styleable name="Slider">
    <attr name="android:value"/>
    <attr name="android:valueFrom"/>
    <attr name="android:valueTo"/>
    <attr name="android:stepSize"/>
    
    <attr name="android:enabled"/>
    
    <attr format="color" name="haloColor"/>
    
    <attr format="dimension" name="haloRadius"/>
    
    <attr name="labelBehavior">
      <!-- Mode that draws the label floating above the bounds of this view. -->
      <enum name="floating" value="0"/>
      <!-- Mode that draws the label within the bounds of the view. -->
      <enum name="withinBounds" value="1"/>
      <!-- Mode that prevents the label from being drawn -->
      <enum name="gone" value="2"/>
    </attr>
    
    <attr format="reference" name="labelStyle"/>
    
    <attr format="color" name="thumbColor"/>
    
    <attr format="dimension" name="thumbElevation"/>
    
    <attr format="dimension" name="thumbRadius"/>
    
    <attr format="color" name="tickColor"/>
    
    <attr format="color" name="tickColorActive"/>
    
    <attr format="color" name="tickColorInactive"/>
    
    <attr name="trackColor"/>
    
    <attr format="color" name="trackColorActive"/>
    
    <attr format="color" name="trackColorInactive"/>
    
    <attr format="dimension" name="trackHeight"/>
  </declare-styleable>
    <declare-styleable name="Snackbar">
    
    <attr format="reference" name="snackbarStyle"/>
    
    <attr format="reference" name="snackbarButtonStyle"/>
    
    <attr format="reference" name="snackbarTextViewStyle"/>
  </declare-styleable>
    <declare-styleable name="SnackbarLayout">
    <attr name="android:maxWidth"/>
    <attr name="elevation"/>
    <attr format="dimension" name="maxActionInlineWidth"/>
    
    <attr format="enum" name="animationMode">
      <!-- Mode that corresponds to the slide in and out animations. -->
      <enum name="slide" value="0"/>
      <!-- Mode that corresponds to the fade in and out animations. -->
      <enum name="fade" value="1"/>
    </attr>
    
    <attr format="float" name="backgroundOverlayColorAlpha"/>
    
    <attr name="backgroundTint"/>
    
    <attr name="backgroundTintMode"/>
    
    <attr format="float" name="actionTextColorAlpha"/>
  </declare-styleable>
    <declare-styleable name="Spinner">
        
        <attr name="android:prompt"/>
        
        <attr name="popupTheme"/>
        
        <attr name="android:popupBackground"/>
        
        <attr name="android:dropDownWidth"/>
        
        <attr name="android:entries"/>
    </declare-styleable>
    <declare-styleable name="State"><attr name="android:id"/><attr format="reference" name="constraints"/></declare-styleable>
    <declare-styleable name="StateListDrawable">
        
        <attr name="android:visible"/>
        
        <attr name="android:variablePadding"/>
        
        <attr name="android:constantSize"/>
        
        <attr name="android:dither"/>
        
        <attr name="android:enterFadeDuration"/>
        
        <attr name="android:exitFadeDuration"/>
        
        
    </declare-styleable>
    <declare-styleable name="StateListDrawableItem">
        
        <attr name="android:drawable"/>
    </declare-styleable>
    <declare-styleable name="StateSet"><attr format="reference" name="defaultState"/></declare-styleable>
    <declare-styleable name="SwipeRefreshLayout">
        
        <attr format="color" name="swipeRefreshLayoutProgressSpinnerBackgroundColor"/>
    </declare-styleable>
    <declare-styleable name="SwitchCompat">
        
        <attr name="android:thumb"/>
        
        <attr format="color" name="thumbTint"/>
        
        <attr name="thumbTintMode">
            <!-- The tint is drawn on top of the drawable.
                 [Sa + (1 - Sa)*Da, Rc = Sc + (1 - Sa)*Dc] -->
            <enum name="src_over" value="3"/>
            <!-- The tint is masked by the alpha channel of the drawable. The drawable’s
                 color channels are thrown out. [Sa * Da, Sc * Da] -->
            <enum name="src_in" value="5"/>
            <!-- The tint is drawn above the drawable, but with the drawable’s alpha
                 channel masking the result. [Da, Sc * Da + (1 - Sa) * Dc] -->
            <enum name="src_atop" value="9"/>
            <!-- Multiplies the color and alpha channels of the drawable with those of
                 the tint. [Sa * Da, Sc * Dc] -->
            <enum name="multiply" value="14"/>
            <!-- [Sa + Da - Sa * Da, Sc + Dc - Sc * Dc] -->
            <enum name="screen" value="15"/>
            <!-- Combines the tint and drawable color and alpha channels, clamping the
                 result to valid color values. Saturate(S + D) -->
            <enum name="add" value="16"/>
        </attr>
        
        <attr format="reference" name="track"/>
        
        <attr format="color" name="trackTint"/>
        
        <attr name="trackTintMode">
            <!-- The tint is drawn on top of the drawable.
                 [Sa + (1 - Sa)*Da, Rc = Sc + (1 - Sa)*Dc] -->
            <enum name="src_over" value="3"/>
            <!-- The tint is masked by the alpha channel of the drawable. The drawable’s
                 color channels are thrown out. [Sa * Da, Sc * Da] -->
            <enum name="src_in" value="5"/>
            <!-- The tint is drawn above the drawable, but with the drawable’s alpha
                 channel masking the result. [Da, Sc * Da + (1 - Sa) * Dc] -->
            <enum name="src_atop" value="9"/>
            <!-- Multiplies the color and alpha channels of the drawable with those of
                 the tint. [Sa * Da, Sc * Dc] -->
            <enum name="multiply" value="14"/>
            <!-- [Sa + Da - Sa * Da, Sc + Dc - Sc * Dc] -->
            <enum name="screen" value="15"/>
            <!-- Combines the tint and drawable color and alpha channels, clamping the
                 result to valid color values. Saturate(S + D) -->
            <enum name="add" value="16"/>
        </attr>
        
        <attr name="android:textOn"/>
        
        <attr name="android:textOff"/>
        
        <attr format="dimension" name="thumbTextPadding"/>
        
        <attr format="reference" name="switchTextAppearance"/>
        
        <attr format="dimension" name="switchMinWidth"/>
        
        <attr format="dimension" name="switchPadding"/>
        
        <attr format="boolean" name="splitTrack"/>
        
        <attr format="boolean" name="showText"/>
    </declare-styleable>
    <declare-styleable name="SwitchMaterial">
    
    <attr name="useMaterialThemeColors"/>
  </declare-styleable>
    <declare-styleable name="TabItem">
    
    <attr name="android:text"/>
    
    <attr name="android:icon"/>
    
    <attr name="android:layout"/>
  </declare-styleable>
    <declare-styleable name="TabLayout">
    
    <attr format="color" name="tabIndicatorColor"/>
    
    <attr format="dimension" name="tabIndicatorHeight"/>
    
    <attr format="dimension" name="tabContentStart"/>
    
    <attr format="reference" name="tabBackground"/>
    
    <attr format="reference" name="tabIndicator"/>
    
    <attr name="tabIndicatorGravity">
      <!-- Align indicator to the bottom of this tab layout. -->
      <enum name="bottom" value="0"/>
      <!-- Align indicator along the center of this tab layout. -->
      <enum name="center" value="1"/>
      <!-- Align indicator to the top of this tab layout. -->
      <enum name="top" value="2"/>
      <!-- Stretch indicator to match the height and width of a tab item in this layout. -->
      <enum name="stretch" value="3"/>
    </attr>
    
    <attr format="integer" name="tabIndicatorAnimationDuration"/>
    
    <attr format="boolean" name="tabIndicatorFullWidth"/>
    
    <attr name="tabMode">
      <enum name="scrollable" value="0"/>
      <enum name="fixed" value="1"/>
      <enum name="auto" value="2"/>
    </attr>
    
    <attr name="tabGravity">
      <enum name="fill" value="0"/>
      <enum name="center" value="1"/>
      <enum name="start" value="2"/>
    </attr>
    
    <attr format="boolean" name="tabInlineLabel"/>
    
    <attr format="dimension" name="tabMinWidth"/>
    
    <attr format="dimension" name="tabMaxWidth"/>
    
    <attr format="reference" name="tabTextAppearance"/>
    
    <attr format="color" name="tabTextColor"/>
    
    <attr format="color" name="tabSelectedTextColor"/>
    
    <attr format="dimension" name="tabPaddingStart"/>
    
    <attr format="dimension" name="tabPaddingTop"/>
    
    <attr format="dimension" name="tabPaddingEnd"/>
    
    <attr format="dimension" name="tabPaddingBottom"/>
    
    <attr format="dimension" name="tabPadding"/>
    
    <attr format="color" name="tabIconTint"/>
    
    <attr name="tabIconTintMode">
      <enum name="src_over" value="3"/>
      <enum name="src_in" value="5"/>
      <enum name="src_atop" value="9"/>
      <enum name="multiply" value="14"/>
      <enum name="screen" value="15"/>
      <enum name="add" value="16"/>
    </attr>
    
    <attr format="color" name="tabRippleColor"/>
    
    <attr format="boolean" name="tabUnboundedRipple"/>
  </declare-styleable>
    <declare-styleable name="TextAppearance">
        <attr name="android:textSize"/>
        <attr name="android:textColor"/>
        <attr name="android:textColorHint"/>
        <attr name="android:textColorLink"/>
        <attr name="android:textStyle"/>
        <attr name="android:textFontWeight"/>
        <attr name="android:typeface"/>
        <attr name="android:fontFamily"/>
        <attr name="fontFamily"/>
        <attr name="textAllCaps"/>
        
        <attr name="textLocale"/>
        <attr name="android:shadowColor"/>
        <attr name="android:shadowDy"/>
        <attr name="android:shadowDx"/>
        <attr name="android:shadowRadius"/>
        
        <attr name="fontVariationSettings"/>
    </declare-styleable>
    <declare-styleable name="TextInputEditText">
    
    <attr format="boolean" name="textInputLayoutFocusedRectEnabled"/>
  </declare-styleable>
    <declare-styleable name="TextInputLayout">
    
    <attr name="android:enabled"/>
    
    <attr name="android:hint"/>
    
    <attr name="android:textColorHint"/>

    
    <attr format="boolean" name="hintEnabled"/>
    
    <attr format="boolean" name="hintAnimationEnabled"/>
    
    <attr format="reference" name="hintTextAppearance"/>
    
    <attr format="color" name="hintTextColor"/>

    
    <attr format="string" name="helperText"/>
    
    <attr format="boolean" name="helperTextEnabled"/>
    
    <attr format="reference" name="helperTextTextAppearance"/>
    
    <attr format="color" name="helperTextTextColor"/>

    
    <attr format="boolean" name="errorEnabled"/>
    
    <attr format="reference" name="errorTextAppearance"/>
    
    <attr format="color" name="errorTextColor"/>
    
    <attr format="string" name="errorContentDescription"/>
    
    <attr format="reference" name="errorIconDrawable"/>
    
    <attr format="reference" name="errorIconTint"/>
    
    <attr name="errorIconTintMode">
      <!-- The tint is drawn on top of the drawable.
           [Sa + (1 - Sa)*Da, Rc = Sc + (1 - Sa)*Dc] -->
      <enum name="src_over" value="3"/>
      <!-- The tint is masked by the alpha channel of the drawable. The drawable’s
           color channels are thrown out. [Sa * Da, Sc * Da] -->
      <enum name="src_in" value="5"/>
      <!-- The tint is drawn above the drawable, but with the drawable’s alpha
           channel masking the result. [Da, Sc * Da + (1 - Sa) * Dc] -->
      <enum name="src_atop" value="9"/>
      <!-- Multiplies the color and alpha channels of the drawable with those of
           the tint. [Sa * Da, Sc * Dc] -->
      <enum name="multiply" value="14"/>
      <!-- [Sa + Da - Sa * Da, Sc + Dc - Sc * Dc] -->
      <enum name="screen" value="15"/>
    </attr>

    
    <attr format="boolean" name="counterEnabled"/>
    
    <attr format="integer" name="counterMaxLength"/>
    
    <attr format="reference" name="counterTextAppearance"/>
    
    <attr format="reference" name="counterTextColor"/>
    
    <attr format="reference" name="counterOverflowTextAppearance"/>
    
    <attr format="reference" name="counterOverflowTextColor"/>

    
    <attr format="string" name="placeholderText"/>
    
    <attr format="reference" name="placeholderTextAppearance"/>
    
    <attr format="color" name="placeholderTextColor"/>

    
    <attr format="string" name="prefixText"/>
    
    <attr format="reference" name="prefixTextAppearance"/>
    
    <attr format="color" name="prefixTextColor"/>
    
    <attr format="string" name="suffixText"/>
    
    <attr format="reference" name="suffixTextAppearance"/>
    
    <attr format="color" name="suffixTextColor"/>

    
    <attr format="reference" name="startIconDrawable"/>
    
    <attr format="string" name="startIconContentDescription"/>
    
    <attr format="boolean" name="startIconCheckable"/>
    
    <attr format="color" name="startIconTint"/>
    
    <attr name="startIconTintMode">
      <!-- The tint is drawn on top of the drawable.
           [Sa + (1 - Sa)*Da, Rc = Sc + (1 - Sa)*Dc] -->
      <enum name="src_over" value="3"/>
      <!-- The tint is masked by the alpha channel of the drawable. The drawable’s
           color channels are thrown out. [Sa * Da, Sc * Da] -->
      <enum name="src_in" value="5"/>
      <!-- The tint is drawn above the drawable, but with the drawable’s alpha
           channel masking the result. [Da, Sc * Da + (1 - Sa) * Dc] -->
      <enum name="src_atop" value="9"/>
      <!-- Multiplies the color and alpha channels of the drawable with those of
           the tint. [Sa * Da, Sc * Dc] -->
      <enum name="multiply" value="14"/>
      <!-- [Sa + Da - Sa * Da, Sc + Dc - Sc * Dc] -->
      <enum name="screen" value="15"/>
    </attr>
    
    <attr name="endIconMode">
      <!-- The view will display a custom icon specified by the user. -->
      <enum name="custom" value="-1"/>
      <!-- No end icon. -->
      <enum name="none" value="0"/>
      <!-- The view will display a toggle when the EditText has a password. -->
      <enum name="password_toggle" value="1"/>
      <!-- The view will display a clear text button while the EditText contains input. -->
      <enum name="clear_text" value="2"/>
      <!-- The view will display a toggle that displays/hides a dropdown menu. -->
      <enum name="dropdown_menu" value="3"/>
    </attr>
    
    <attr format="reference" name="endIconDrawable"/>
    
    <attr format="string" name="endIconContentDescription"/>
    
    <attr format="boolean" name="endIconCheckable"/>
    
    <attr format="color" name="endIconTint"/>
    
    <attr name="endIconTintMode">
      <!-- The tint is drawn on top of the drawable.
           [Sa + (1 - Sa)*Da, Rc = Sc + (1 - Sa)*Dc] -->
      <enum name="src_over" value="3"/>
      <!-- The tint is masked by the alpha channel of the drawable. The drawable’s
           color channels are thrown out. [Sa * Da, Sc * Da] -->
      <enum name="src_in" value="5"/>
      <!-- The tint is drawn above the drawable, but with the drawable’s alpha
           channel masking the result. [Da, Sc * Da + (1 - Sa) * Dc] -->
      <enum name="src_atop" value="9"/>
      <!-- Multiplies the color and alpha channels of the drawable with those of
           the tint. [Sa * Da, Sc * Dc] -->
      <enum name="multiply" value="14"/>
      <!-- [Sa + Da - Sa * Da, Sc + Dc - Sc * Dc] -->
      <enum name="screen" value="15"/>
    </attr>

    
    <attr name="boxBackgroundMode">
      <!-- Specifies that there should be no box set on the text input area. -->
      <enum name="none" value="0"/>
      <!-- Filled box mode for the text input box. -->
      <enum name="filled" value="1"/>
      <!-- Outline box mode for the text input box. -->
      <enum name="outline" value="2"/>
    </attr>
    
    <attr format="dimension" name="boxCollapsedPaddingTop"/>
    
    <attr format="dimension" name="boxCornerRadiusTopStart"/>
    
    <attr format="dimension" name="boxCornerRadiusTopEnd"/>
    
    <attr format="dimension" name="boxCornerRadiusBottomStart"/>
    
    <attr format="dimension" name="boxCornerRadiusBottomEnd"/>
    
    <attr format="color" name="boxStrokeColor"/>
    
    <attr format="color" name="boxStrokeErrorColor"/>
    
    <attr format="color" name="boxBackgroundColor"/>
    
    <attr format="dimension" name="boxStrokeWidth"/>
    
    <attr format="dimension" name="boxStrokeWidthFocused"/>

    
    <attr name="shapeAppearance"/>
    
    <attr name="shapeAppearanceOverlay"/>

    
    <attr format="boolean" name="passwordToggleEnabled"/>
    
    <attr format="reference" name="passwordToggleDrawable"/>
    
    <attr format="string" name="passwordToggleContentDescription"/>
    
    <attr format="color" name="passwordToggleTint"/>
    
    <attr name="passwordToggleTintMode">
      <!-- The tint is drawn on top of the drawable.
           [Sa + (1 - Sa)*Da, Rc = Sc + (1 - Sa)*Dc] -->
      <enum name="src_over" value="3"/>
      <!-- The tint is masked by the alpha channel of the drawable. The drawable’s
           color channels are thrown out. [Sa * Da, Sc * Da] -->
      <enum name="src_in" value="5"/>
      <!-- The tint is drawn above the drawable, but with the drawable’s alpha
           channel masking the result. [Da, Sc * Da + (1 - Sa) * Dc] -->
      <enum name="src_atop" value="9"/>
      <!-- Multiplies the color and alpha channels of the drawable with those of
           the tint. [Sa * Da, Sc * Dc] -->
      <enum name="multiply" value="14"/>
      <!-- [Sa + Da - Sa * Da, Sc + Dc - Sc * Dc] -->
      <enum name="screen" value="15"/>
    </attr>
  </declare-styleable>
    <declare-styleable name="ThemeEnforcement">
    
    <attr format="boolean" name="enforceMaterialTheme"/>
    
    <attr format="boolean" name="enforceTextAppearance"/>
    
    <attr name="android:textAppearance"/>
  </declare-styleable>
    <declare-styleable name="Toolbar">
        <attr format="reference" name="titleTextAppearance"/>
        <attr format="reference" name="subtitleTextAppearance"/>
        <attr name="title"/>
        <attr name="subtitle"/>
        <attr name="android:gravity"/>
        
        <attr format="dimension" name="titleMargin"/>
        
        <attr format="dimension" name="titleMarginStart"/>
        
        <attr format="dimension" name="titleMarginEnd"/>
        
        <attr format="dimension" name="titleMarginTop"/>
        
        <attr format="dimension" name="titleMarginBottom"/>
        
        <attr format="dimension" name="titleMargins"/>
        <attr name="contentInsetStart"/>
        <attr name="contentInsetEnd"/>
        <attr name="contentInsetLeft"/>
        <attr name="contentInsetRight"/>
        <attr name="contentInsetStartWithNavigation"/>
        <attr name="contentInsetEndWithActions"/>
        <attr format="dimension" name="maxButtonHeight"/>
        <attr name="buttonGravity">
            <!-- Place object in the vertical center of its container, not changing its size. -->
            <flag name="center_vertical" value="0x10"/>
            <!-- Push object to the top of its container, not changing its size. -->
            <flag name="top" value="0x30"/>
            <!-- Push object to the bottom of its container, not changing its size. -->
            <flag name="bottom" value="0x50"/>
        </attr>
        
        <attr format="reference" name="collapseIcon"/>
        
        <attr format="string" name="collapseContentDescription"/>
        
        <attr name="popupTheme"/>
        
        <attr format="reference" name="navigationIcon"/>
        
        <attr format="string" name="navigationContentDescription"/>
        
        <attr name="logo"/>
        
        <attr format="string" name="logoDescription"/>
        
        <attr format="color" name="titleTextColor"/>
        
        <attr format="color" name="subtitleTextColor"/>
        <attr name="android:minHeight"/>
        
        <attr format="reference" name="menu"/>
    </declare-styleable>
    <declare-styleable name="Tooltip">
    <attr name="android:text"/>
    <attr name="android:textAppearance"/>
    <attr name="android:layout_margin"/>
    <attr name="android:minWidth"/>
    <attr name="android:minHeight"/>
    <attr name="android:padding"/>
    <attr format="color" name="backgroundTint"/>
  </declare-styleable>
    <declare-styleable name="Transform"><attr name="android:elevation"/><attr name="android:rotation"/><attr name="android:rotationX"/><attr name="android:rotationY"/><attr name="android:scaleX"/><attr name="android:scaleY"/><attr name="android:transformPivotX"/><attr name="android:transformPivotY"/><attr format="dimension" name="android:translationX"/><attr format="dimension" name="android:translationY"/><attr format="dimension" name="android:translationZ"/></declare-styleable>
    <declare-styleable name="Transition"><attr name="android:id"/><attr format="reference" name="constraintSetStart"/><attr format="reference" name="constraintSetEnd"/><attr format="boolean" name="transitionDisable"/><attr name="layoutDuringTransition"/><attr name="pathMotionArc"/><attr format="enum" name="autoTransition">
            <enum name="none" value="0"/>
            <enum name="jumpToStart" value="1"/>
            <enum name="jumpToEnd" value="2"/>
            <enum name="animateToStart" value="3"/>
            <enum name="animateToEnd" value="4"/>
        </attr><attr format="string|reference|enum" name="motionInterpolator">
            <enum name="easeInOut" value="0"/>
            <enum name="easeIn" value="1"/>
            <enum name="easeOut" value="2"/>
            <enum name="linear" value="3"/>
            <enum name="bounce" value="5"/>
        </attr><attr name="duration"/><attr format="float" name="staggered"/><attr name="transitionFlags">
            <flag name="none" value="0"/>
            <flag name="beginOnFirstDraw" value="1"/>
            />
        </attr></declare-styleable>
    <declare-styleable name="Variant"><attr format="dimension" name="region_widthLessThan"/><attr format="dimension" name="region_widthMoreThan"/><attr format="dimension" name="region_heightLessThan"/><attr format="dimension" name="region_heightMoreThan"/><attr name="constraints"/></declare-styleable>
    <declare-styleable name="View">
        
        <attr format="dimension" name="paddingStart"/>
        
        <attr format="dimension" name="paddingEnd"/>
        
        <attr name="android:focusable"/>
        
        <attr format="reference" name="theme"/>
        
        <attr name="android:theme"/>
    </declare-styleable>
    <declare-styleable name="ViewBackgroundHelper">
        <attr name="android:background"/>
        
        <attr format="color" name="backgroundTint"/>

        
        <attr name="backgroundTintMode">
            <!-- The tint is drawn on top of the drawable.
                 [Sa + (1 - Sa)*Da, Rc = Sc + (1 - Sa)*Dc] -->
            <enum name="src_over" value="3"/>
            <!-- The tint is masked by the alpha channel of the drawable. The drawable’s
                 color channels are thrown out. [Sa * Da, Sc * Da] -->
            <enum name="src_in" value="5"/>
            <!-- The tint is drawn above the drawable, but with the drawable’s alpha
                 channel masking the result. [Da, Sc * Da + (1 - Sa) * Dc] -->
            <enum name="src_atop" value="9"/>
            <!-- Multiplies the color and alpha channels of the drawable with those of
                 the tint. [Sa * Da, Sc * Dc] -->
            <enum name="multiply" value="14"/>
            <!-- [Sa + Da - Sa * Da, Sc + Dc - Sc * Dc] -->
            <enum name="screen" value="15"/>
            <!-- Combines the tint and icon color and alpha channels, clamping the
                 result to valid color values. Saturate(S + D) -->
            <enum name="add" value="16"/>
        </attr>
    </declare-styleable>
    <declare-styleable name="ViewPager2">
        <attr name="android:orientation"/>
    </declare-styleable>
    <declare-styleable name="ViewStubCompat">
        
        <attr name="android:layout"/>
        
        <attr name="android:inflatedId"/>
        <attr name="android:id"/>
    </declare-styleable>
</resources>