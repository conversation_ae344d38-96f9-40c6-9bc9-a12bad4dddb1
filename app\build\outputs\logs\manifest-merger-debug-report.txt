-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\AndroidManifest.xml:2:1-37:12
INJECTED from C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\AndroidManifest.xml:2:1-37:12
INJECTED from C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\AndroidManifest.xml:2:1-37:12
INJECTED from C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\AndroidManifest.xml:2:1-37:12
MERGED from [com.google.android.material:material:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\32174b9aa61ba085e525e198023dc11d\transformed\material-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ba29756262577ddb0ba083f3bc1906e0\transformed\constraintlayout-2.0.1\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\313b7d5dbe5dc9219fc547695506f52e\transformed\appcompat-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [de.hdodenhof:circleimageview:2.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc982891fe19f3acfb996fe987479678\transformed\circleimageview-2.1.0\AndroidManifest.xml:2:1-11:12
MERGED from [com.github.bumptech.glide:glide:4.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7b62f7a2ffe70c90964ed6ac0596f772\transformed\glide-4.13.0\AndroidManifest.xml:2:1-12:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46efaa686bf028d01bc0e373db9a6a46\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b2f03a6c34802a46a45d6399c7ae921\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat-resources:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\75bf6e238f30e08aa7091daa7f89966e\transformed\appcompat-resources-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd4d41628e5e0c081b523aaadd165524\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a60c159c7ca9eb3593d57ef6199673cc\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f7e9ae46787c47ae351e8a61be234d65\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\71ea803259349f97814933245451a8d3\transformed\transition-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e3333dea171f507cf27a6711021ecc98\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c669eb0ac81dfb09b80a2425fba5a6f3\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a30430c884775336c1d568fa5c935b41\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5bcc968f85331eb1072bf2759fc6ebd4\transformed\fragment-1.3.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bef938e02d3b77a5a565924eddcd5232\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a17fcb820eb6f9d68b4346d0296d65d\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3c0b595a199549f3e428240151565589\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\084a750bd95de8b03c30a62db7c865b0\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b1c04140ddc1a13cec7cdb9e2e4536ea\transformed\activity-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8d06f4c23c6b78afc6b8e18c56daf21c\transformed\core-1.3.1\AndroidManifest.xml:17:1-26:12
MERGED from [com.github.bumptech.glide:gifdecoder:4.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f5ec70cf03fc7967a54a932f339baaae\transformed\gifdecoder-4.13.0\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.exifinterface:exifinterface:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d5a0438819f29ac96c91cdc1b4e78c64\transformed\exifinterface-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\474639784496fdcb1dff3c865820bc2e\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0cc7dfd2c0fcae0982c28937b18f49ab\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ede02a27188e30128c4e6e8452cf3ac9\transformed\versionedparcelable-1.1.0\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\12191f4969130b684c49fb0a55573cc2\transformed\lifecycle-viewmodel-savedstate-2.3.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7bc413bb10aa4874437e4505be60a4b5\transformed\lifecycle-viewmodel-2.3.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.savedstate:savedstate:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c112e2907b8fb468f4b8ec308058a79b\transformed\savedstate-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\720f06221cf12f1b14583ec7d17bccd8\transformed\lifecycle-runtime-2.3.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0465b0c41e232f9758bbbe67283f8116\transformed\lifecycle-livedata-2.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e964f83214172eb2c0536ff4f5b1c13a\transformed\lifecycle-livedata-core-2.3.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.arch.core:core-runtime:2.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\017843d23ff14032b1831a1cd6fb2431\transformed\core-runtime-2.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.annotation:annotation-experimental:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da3062c51e9c5e1bed992976ccc3982a\transformed\annotation-experimental-1.0.0\AndroidManifest.xml:17:1-24:12
	package
		ADDED from C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\AndroidManifest.xml:3:5-36
		INJECTED from C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\AndroidManifest.xml
	android:versionCode
		INJECTED from C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\AndroidManifest.xml:2:11-69
application
ADDED from C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\AndroidManifest.xml:5:5-35:19
INJECTED from C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\AndroidManifest.xml:5:5-35:19
MERGED from [com.google.android.material:material:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\32174b9aa61ba085e525e198023dc11d\transformed\material-1.2.1\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\32174b9aa61ba085e525e198023dc11d\transformed\material-1.2.1\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ba29756262577ddb0ba083f3bc1906e0\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ba29756262577ddb0ba083f3bc1906e0\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [de.hdodenhof:circleimageview:2.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc982891fe19f3acfb996fe987479678\transformed\circleimageview-2.1.0\AndroidManifest.xml:9:5-20
MERGED from [de.hdodenhof:circleimageview:2.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc982891fe19f3acfb996fe987479678\transformed\circleimageview-2.1.0\AndroidManifest.xml:9:5-20
MERGED from [com.github.bumptech.glide:glide:4.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7b62f7a2ffe70c90964ed6ac0596f772\transformed\glide-4.13.0\AndroidManifest.xml:10:5-20
MERGED from [com.github.bumptech.glide:glide:4.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7b62f7a2ffe70c90964ed6ac0596f772\transformed\glide-4.13.0\AndroidManifest.xml:10:5-20
MERGED from [androidx.core:core:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8d06f4c23c6b78afc6b8e18c56daf21c\transformed\core-1.3.1\AndroidManifest.xml:24:5-89
MERGED from [androidx.core:core:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8d06f4c23c6b78afc6b8e18c56daf21c\transformed\core-1.3.1\AndroidManifest.xml:24:5-89
MERGED from [com.github.bumptech.glide:gifdecoder:4.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f5ec70cf03fc7967a54a932f339baaae\transformed\gifdecoder-4.13.0\AndroidManifest.xml:9:5-20
MERGED from [com.github.bumptech.glide:gifdecoder:4.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f5ec70cf03fc7967a54a932f339baaae\transformed\gifdecoder-4.13.0\AndroidManifest.xml:9:5-20
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ede02a27188e30128c4e6e8452cf3ac9\transformed\versionedparcelable-1.1.0\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ede02a27188e30128c4e6e8452cf3ac9\transformed\versionedparcelable-1.1.0\AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8d06f4c23c6b78afc6b8e18c56daf21c\transformed\core-1.3.1\AndroidManifest.xml:24:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\AndroidManifest.xml:10:9-35
	android:label
		ADDED from C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\AndroidManifest.xml:8:9-41
	android:roundIcon
		ADDED from C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\AndroidManifest.xml:9:9-44
	android:icon
		ADDED from C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\AndroidManifest.xml:7:9-39
	android:allowBackup
		ADDED from C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\AndroidManifest.xml:6:9-35
	android:theme
		ADDED from C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\AndroidManifest.xml:11:9-39
activity#njust.dzh.ordersystem.Activity.OrderActivity
ADDED from C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\AndroidManifest.xml:12:9-14:49
	android:label
		ADDED from C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\AndroidManifest.xml:13:13-47
	android:exported
		ADDED from C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\AndroidManifest.xml:14:13-37
	android:name
		ADDED from C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\AndroidManifest.xml:12:19-57
activity#njust.dzh.ordersystem.Activity.FoodActivity
ADDED from C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\AndroidManifest.xml:15:9-18:40
	android:exported
		ADDED from C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\AndroidManifest.xml:18:13-37
	android:theme
		ADDED from C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\AndroidManifest.xml:17:13-53
	android:name
		ADDED from C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\AndroidManifest.xml:16:13-50
activity#njust.dzh.ordersystem.Activity.LoginActivity
ADDED from C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\AndroidManifest.xml:19:9-20:40
	android:exported
		ADDED from C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\AndroidManifest.xml:20:13-37
	android:name
		ADDED from C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\AndroidManifest.xml:19:19-57
activity#njust.dzh.ordersystem.Activity.RegisterActivity
ADDED from C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\AndroidManifest.xml:21:9-22:40
	android:exported
		ADDED from C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\AndroidManifest.xml:22:13-37
	android:name
		ADDED from C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\AndroidManifest.xml:21:19-60
activity#njust.dzh.ordersystem.Activity.WelcomeActivity
ADDED from C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\AndroidManifest.xml:23:9-30:20
	android:exported
		ADDED from C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\AndroidManifest.xml:24:13-36
	android:name
		ADDED from C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\AndroidManifest.xml:23:19-59
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\AndroidManifest.xml:25:13-29:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\AndroidManifest.xml:26:17-69
	android:name
		ADDED from C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\AndroidManifest.xml:26:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\AndroidManifest.xml:28:17-77
	android:name
		ADDED from C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\AndroidManifest.xml:28:27-74
activity#njust.dzh.ordersystem.Activity.MainActivity
ADDED from C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\AndroidManifest.xml:31:9-34:20
	android:exported
		ADDED from C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\AndroidManifest.xml:32:13-37
	android:name
		ADDED from C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\AndroidManifest.xml:31:19-56
uses-sdk
INJECTED from C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\AndroidManifest.xml
MERGED from [com.google.android.material:material:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\32174b9aa61ba085e525e198023dc11d\transformed\material-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\32174b9aa61ba085e525e198023dc11d\transformed\material-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ba29756262577ddb0ba083f3bc1906e0\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ba29756262577ddb0ba083f3bc1906e0\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\313b7d5dbe5dc9219fc547695506f52e\transformed\appcompat-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\313b7d5dbe5dc9219fc547695506f52e\transformed\appcompat-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [de.hdodenhof:circleimageview:2.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc982891fe19f3acfb996fe987479678\transformed\circleimageview-2.1.0\AndroidManifest.xml:5:5-7:41
MERGED from [de.hdodenhof:circleimageview:2.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc982891fe19f3acfb996fe987479678\transformed\circleimageview-2.1.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:glide:4.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7b62f7a2ffe70c90964ed6ac0596f772\transformed\glide-4.13.0\AndroidManifest.xml:6:5-8:41
MERGED from [com.github.bumptech.glide:glide:4.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7b62f7a2ffe70c90964ed6ac0596f772\transformed\glide-4.13.0\AndroidManifest.xml:6:5-8:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46efaa686bf028d01bc0e373db9a6a46\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46efaa686bf028d01bc0e373db9a6a46\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b2f03a6c34802a46a45d6399c7ae921\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b2f03a6c34802a46a45d6399c7ae921\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\75bf6e238f30e08aa7091daa7f89966e\transformed\appcompat-resources-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\75bf6e238f30e08aa7091daa7f89966e\transformed\appcompat-resources-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd4d41628e5e0c081b523aaadd165524\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd4d41628e5e0c081b523aaadd165524\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a60c159c7ca9eb3593d57ef6199673cc\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a60c159c7ca9eb3593d57ef6199673cc\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f7e9ae46787c47ae351e8a61be234d65\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f7e9ae46787c47ae351e8a61be234d65\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\71ea803259349f97814933245451a8d3\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\71ea803259349f97814933245451a8d3\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e3333dea171f507cf27a6711021ecc98\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e3333dea171f507cf27a6711021ecc98\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c669eb0ac81dfb09b80a2425fba5a6f3\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c669eb0ac81dfb09b80a2425fba5a6f3\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a30430c884775336c1d568fa5c935b41\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a30430c884775336c1d568fa5c935b41\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5bcc968f85331eb1072bf2759fc6ebd4\transformed\fragment-1.3.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5bcc968f85331eb1072bf2759fc6ebd4\transformed\fragment-1.3.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bef938e02d3b77a5a565924eddcd5232\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bef938e02d3b77a5a565924eddcd5232\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a17fcb820eb6f9d68b4346d0296d65d\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a17fcb820eb6f9d68b4346d0296d65d\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3c0b595a199549f3e428240151565589\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3c0b595a199549f3e428240151565589\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\084a750bd95de8b03c30a62db7c865b0\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\084a750bd95de8b03c30a62db7c865b0\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b1c04140ddc1a13cec7cdb9e2e4536ea\transformed\activity-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b1c04140ddc1a13cec7cdb9e2e4536ea\transformed\activity-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8d06f4c23c6b78afc6b8e18c56daf21c\transformed\core-1.3.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8d06f4c23c6b78afc6b8e18c56daf21c\transformed\core-1.3.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f5ec70cf03fc7967a54a932f339baaae\transformed\gifdecoder-4.13.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f5ec70cf03fc7967a54a932f339baaae\transformed\gifdecoder-4.13.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.exifinterface:exifinterface:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d5a0438819f29ac96c91cdc1b4e78c64\transformed\exifinterface-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d5a0438819f29ac96c91cdc1b4e78c64\transformed\exifinterface-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\474639784496fdcb1dff3c865820bc2e\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\474639784496fdcb1dff3c865820bc2e\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0cc7dfd2c0fcae0982c28937b18f49ab\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0cc7dfd2c0fcae0982c28937b18f49ab\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ede02a27188e30128c4e6e8452cf3ac9\transformed\versionedparcelable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ede02a27188e30128c4e6e8452cf3ac9\transformed\versionedparcelable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\12191f4969130b684c49fb0a55573cc2\transformed\lifecycle-viewmodel-savedstate-2.3.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\12191f4969130b684c49fb0a55573cc2\transformed\lifecycle-viewmodel-savedstate-2.3.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7bc413bb10aa4874437e4505be60a4b5\transformed\lifecycle-viewmodel-2.3.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7bc413bb10aa4874437e4505be60a4b5\transformed\lifecycle-viewmodel-2.3.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.savedstate:savedstate:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c112e2907b8fb468f4b8ec308058a79b\transformed\savedstate-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.savedstate:savedstate:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c112e2907b8fb468f4b8ec308058a79b\transformed\savedstate-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-runtime:2.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\720f06221cf12f1b14583ec7d17bccd8\transformed\lifecycle-runtime-2.3.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-runtime:2.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\720f06221cf12f1b14583ec7d17bccd8\transformed\lifecycle-runtime-2.3.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0465b0c41e232f9758bbbe67283f8116\transformed\lifecycle-livedata-2.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0465b0c41e232f9758bbbe67283f8116\transformed\lifecycle-livedata-2.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e964f83214172eb2c0536ff4f5b1c13a\transformed\lifecycle-livedata-core-2.3.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e964f83214172eb2c0536ff4f5b1c13a\transformed\lifecycle-livedata-core-2.3.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\017843d23ff14032b1831a1cd6fb2431\transformed\core-runtime-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\017843d23ff14032b1831a1cd6fb2431\transformed\core-runtime-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.annotation:annotation-experimental:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da3062c51e9c5e1bed992976ccc3982a\transformed\annotation-experimental-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.annotation:annotation-experimental:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da3062c51e9c5e1bed992976ccc3982a\transformed\annotation-experimental-1.0.0\AndroidManifest.xml:20:5-22:41
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\AndroidManifest.xml
