1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="njust.dzh.ordersystem"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="32" />
10
11    <application
11-->C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\AndroidManifest.xml:5:5-35:19
12        android:allowBackup="true"
12-->C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\AndroidManifest.xml:6:9-35
13        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
13-->[androidx.core:core:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8d06f4c23c6b78afc6b8e18c56daf21c\transformed\core-1.3.1\AndroidManifest.xml:24:18-86
14        android:debuggable="true"
15        android:extractNativeLibs="true"
16        android:icon="@mipmap/ic_icon"
16-->C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\AndroidManifest.xml:7:9-39
17        android:label="@string/app_name"
17-->C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\AndroidManifest.xml:8:9-41
18        android:roundIcon="@mipmap/ic_icon"
18-->C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\AndroidManifest.xml:9:9-44
19        android:supportsRtl="true"
19-->C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\AndroidManifest.xml:10:9-35
20        android:testOnly="true"
21        android:theme="@style/MyTheme" >
21-->C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\AndroidManifest.xml:11:9-39
22        <activity
22-->C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\AndroidManifest.xml:12:9-14:49
23            android:name="njust.dzh.ordersystem.Activity.OrderActivity"
23-->C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\AndroidManifest.xml:12:19-57
24            android:exported="false"
24-->C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\AndroidManifest.xml:14:13-37
25            android:label="@string/order_info" />
25-->C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\AndroidManifest.xml:13:13-47
26        <activity
26-->C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\AndroidManifest.xml:15:9-18:40
27            android:name="njust.dzh.ordersystem.Activity.FoodActivity"
27-->C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\AndroidManifest.xml:16:13-50
28            android:exported="false"
28-->C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\AndroidManifest.xml:18:13-37
29            android:theme="@style/FoodActivityTheme" />
29-->C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\AndroidManifest.xml:17:13-53
30        <activity
30-->C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\AndroidManifest.xml:19:9-20:40
31            android:name="njust.dzh.ordersystem.Activity.LoginActivity"
31-->C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\AndroidManifest.xml:19:19-57
32            android:exported="false" />
32-->C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\AndroidManifest.xml:20:13-37
33        <activity
33-->C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\AndroidManifest.xml:21:9-22:40
34            android:name="njust.dzh.ordersystem.Activity.RegisterActivity"
34-->C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\AndroidManifest.xml:21:19-60
35            android:exported="false" />
35-->C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\AndroidManifest.xml:22:13-37
36        <activity
36-->C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\AndroidManifest.xml:23:9-30:20
37            android:name="njust.dzh.ordersystem.Activity.WelcomeActivity"
37-->C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\AndroidManifest.xml:23:19-59
38            android:exported="true" >
38-->C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\AndroidManifest.xml:24:13-36
39            <intent-filter>
39-->C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\AndroidManifest.xml:25:13-29:29
40                <action android:name="android.intent.action.MAIN" />
40-->C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\AndroidManifest.xml:26:17-69
40-->C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\AndroidManifest.xml:26:25-66
41
42                <category android:name="android.intent.category.LAUNCHER" />
42-->C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\AndroidManifest.xml:28:17-77
42-->C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\AndroidManifest.xml:28:27-74
43            </intent-filter>
44        </activity>
45        <activity
45-->C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\AndroidManifest.xml:31:9-34:20
46            android:name="njust.dzh.ordersystem.Activity.MainActivity"
46-->C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\AndroidManifest.xml:31:19-56
47            android:exported="false" >
47-->C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\AndroidManifest.xml:32:13-37
48        </activity>
49    </application>
50
51</manifest>
