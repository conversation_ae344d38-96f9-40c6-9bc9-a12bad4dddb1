{"logs": [{"outputFile": "njust.dzh.ordersystem.app-mergeDebugResources-19:/values-pt-rPT/values-pt-rPT.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\8d06f4c23c6b78afc6b8e18c56daf21c\\transformed\\core-1.3.1\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2934", "endColumns": "100", "endOffsets": "3030"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\313b7d5dbe5dc9219fc547695506f52e\\transformed\\appcompat-1.2.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,319,426,515,616,740,825,905,997,1091,1188,1282,1381,1475,1571,1666,1758,1850,1935,2042,2153,2255,2363,2471,2578,2749,2848", "endColumns": "107,105,106,88,100,123,84,79,91,93,96,93,98,93,95,94,91,91,84,106,110,101,107,107,106,170,98,85", "endOffsets": "208,314,421,510,611,735,820,900,992,1086,1183,1277,1376,1470,1566,1661,1753,1845,1930,2037,2148,2250,2358,2466,2573,2744,2843,2929"}}]}]}