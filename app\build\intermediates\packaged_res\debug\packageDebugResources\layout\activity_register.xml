<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="10dp"
    tools:context=".Activity.RegisterActivity">
    <TextView
        android:id="@+id/tv_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="订餐系统"
        android:textSize="40sp"
        android:textStyle="bold"
        android:typeface="sans"
        android:textColor="@color/black"
        android:gravity="center_horizontal"
        android:background="@drawable/bg_button" />
    <androidx.cardview.widget.CardView
        android:id="@+id/card_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/tv_title"
        android:layout_marginTop="20dp"
        app:cardCornerRadius="10dp">
        <ImageView
            android:layout_width="match_parent"
            android:layout_height="250dp"
            android:layout_below="@+id/tv_title"
            android:background="@drawable/logo"/>
    </androidx.cardview.widget.CardView>

    <EditText
        android:id="@+id/et_account"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_below="@+id/card_view"
        android:hint="请输入账号"
        android:textSize="25sp"
        android:drawableStart="@drawable/ic_account"
        android:drawablePadding="10dp"
        android:textColor="@color/DeepSkyBlue"
        android:background="@drawable/bg_textview"
        android:inputType="number"
        android:padding="10dp"
        android:layout_marginTop="30dp" />

    <EditText
        android:id="@+id/et_password"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_below="@+id/et_account"
        android:hint="请输入密码"
        android:textSize="25sp"
        android:drawableStart="@drawable/ic_password"
        android:drawablePadding="10dp"
        android:textColor="@color/DeepSkyBlue"
        android:background="@drawable/bg_textview"
        android:inputType="textPassword"
        android:padding="10dp"
        android:layout_marginTop="20dp" />

    <EditText
        android:id="@+id/et_confirm_password"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_below="@+id/et_password"
        android:hint="请确认密码"
        android:textSize="25sp"
        android:drawableStart="@drawable/ic_password"
        android:drawablePadding="10dp"
        android:textColor="@color/DeepSkyBlue"
        android:background="@drawable/bg_textview"
        android:inputType="textPassword"
        android:padding="10dp"
        android:layout_marginTop="20dp" />
    <LinearLayout
        android:id="@+id/linear"
        android:orientation="horizontal"
        android:layout_below="@id/et_confirm_password"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="30dp">
        <Button
            android:id="@+id/btn_register"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="注册"
            android:typeface="monospace"
            android:textSize="25sp"
            android:layout_marginRight="10dp"
            android:background="@drawable/bg_button" />
        <Button
            android:id="@+id/btn_cancel"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="取消"
            android:typeface="monospace"
            android:textSize="25sp"
            android:background="@drawable/bg_button" />
    </LinearLayout>


</RelativeLayout>