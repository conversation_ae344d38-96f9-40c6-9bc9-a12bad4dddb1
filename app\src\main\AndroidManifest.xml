<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="njust.dzh.ordersystem">

    <application
        android:allowBackup="true"
        android:icon="@mipmap/ic_icon"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_icon"
        android:supportsRtl="true"
        android:theme="@style/MyTheme">
        <activity android:name=".Activity.OrderActivity"
            android:label="@string/order_info"
            android:exported="false"></activity>
        <activity
            android:name=".Activity.FoodActivity"
            android:theme="@style/FoodActivityTheme"
            android:exported="false" />
        <activity android:name=".Activity.LoginActivity"
            android:exported="false" />
        <activity android:name=".Activity.RegisterActivity"
            android:exported="false" />
        <activity android:name=".Activity.WelcomeActivity"
            android:exported="true" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity android:name=".Activity.MainActivity"
            android:exported="false">

        </activity>
    </application>

</manifest>