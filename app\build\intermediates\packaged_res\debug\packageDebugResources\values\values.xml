<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:ns1="http://schemas.android.com/tools">
    <color name="Azure">#F0FFFF</color>
    <color name="Black">#000000</color>
    <color name="Blue">#3C8DC4</color>
    <color name="CadetBlue">#5F9EA0</color>
    <color name="Cyan">#00FFFF</color>
    <color name="DarkBlue">#00008B</color>
    <color name="DarkCyan">#008B8B</color>
    <color name="DarkOrange">#FF8C00</color>
    <color name="DarkTurquoise">#00CED1</color>
    <color name="DeepSkyBlue">#00BFFF</color>
    <color name="Gold">#FFD700</color>
    <color name="Gray">#808080</color>
    <color name="Green">#008000</color>
    <color name="Honeydew">#F0FFF0</color>
    <color name="Lavender">#E6E6FA</color>
    <color name="LightCyan">#E1FFFF</color>
    <color name="Olive">#808000</color>
    <color name="Red">#FF0000</color>
    <color name="SpringGreen">#3CB371</color>
    <color name="White">#FFFFFF</color>
    <color name="black">#FF000000</color>
    <color name="purple_200">#FFBB86FC</color>
    <color name="purple_500">#FF6200EE</color>
    <color name="purple_700">#FF3700B3</color>
    <color name="teal_200">#FF03DAC5</color>
    <color name="teal_700">#FF018786</color>
    <color name="white">#FFFFFFFF</color>
    <string name="app_name">OrderSystem</string>
    <string name="login">用户登录</string>
    <string name="order">历史订单</string>
    <string name="order_info">订单信息</string>
    <string name="person">个人信息</string>
    <string name="register">用户注册</string>
    <string name="share">分享软件</string>
    <style name="AppTheme" parent="Theme.AppCompat.Light.NoActionBar">
        
        <item name="colorPrimary">@color/DarkCyan</item>
        <item name="colorPrimaryDark">@color/black</item>
        <item name="colorAccent">@color/Red</item>
    </style>
    <style name="FoodActivityTheme" parent="AppTheme">
    </style>
    <style name="MyTheme" parent="AppTheme">

    </style>
    <style name="PersonImageStyle">
        <item name="android:layout_height">50dp</item>
        <item name="android:layout_width">50dp</item>
        <item name="android:layout_margin">5dp</item>
    </style>
    <style name="PersonLineStyle">
        <item name="background">@color/Lavender</item>
        <item name="android:layout_height">3dp</item>
        <item name="android:layout_width">match_parent</item>
    </style>
    <style name="PersonTvStyle">
        <item name="android:layout_height">match_parent</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:textColor">@color/Black</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textSize">18sp</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:layout_marginLeft">10dp</item>
        <item name="background">@color/Honeydew</item>
    </style>
    <style name="Theme.OrderSystem" parent="Theme.MaterialComponents.DayNight.DarkActionBar.Bridge">
        
        <item name="colorPrimary">@color/purple_500</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor" ns1:targetApi="l">?attr/colorPrimaryVariant</item>
        
    </style>
</resources>