<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_margin="6dp"
    app:cardCornerRadius="6dp"
    android:backgroundTint="@color/Azure">
    <RelativeLayout
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <ImageView
            android:id="@+id/cart_image"
            android:layout_width="match_parent"
            android:layout_height="200dp"
            android:scaleType="centerCrop"
            android:background="@drawable/p1"/>
        <TextView
            android:id="@+id/cart_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/cart_image"
            android:layout_margin="5dp"
            android:text="口水鸡"
            android:layout_centerInParent="true"
            android:textColor="@color/Black"
            android:textSize="20sp"
            android:textStyle="bold"/>
        <TextView
            android:id="@+id/tv_price"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_margin="5dp"
            android:layout_below="@+id/cart_name"
            android:text="价格："
            android:textSize="20sp"
            android:textColor="@color/Black" />
        <TextView
            android:id="@+id/cart_price"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_margin="5dp"
            android:layout_toRightOf="@+id/tv_price"
            android:layout_below="@+id/cart_name"
            android:textColor="@color/purple_700"
            android:textSize="20sp"
            android:text="19.9"/>
        <TextView
            android:id="@+id/cart_yuan"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_margin="5dp"
            android:layout_toRightOf="@+id/cart_price"
            android:layout_below="@+id/cart_name"
            android:textColor="@color/purple_700"
            android:textSize="20sp"
            android:text="元"/>
        <LinearLayout
            android:layout_alignParentRight="true"
            android:layout_below="@id/cart_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">
            <TextView
                android:id="@+id/cart_minus"
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:layout_marginTop="5dp"
                android:layout_marginBottom="5dp"
                android:layout_marginRight="10dp"
                android:background="@drawable/ic_minus"
                android:textSize="20sp"
                android:textColor="@color/DarkOrange"/>
            <TextView
                android:id="@+id/cart_num"
                android:layout_width="40dp"
                android:layout_height="wrap_content"
                android:layout_margin="5dp"
                android:gravity="center"
                android:background="@drawable/bg_textview"
                android:textSize="20sp"
                android:text="1"/>
            <TextView
                android:id="@+id/cart_add"
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:layout_marginTop="5dp"
                android:layout_marginRight="10dp"
                android:layout_marginLeft="10dp"
                android:layout_marginBottom="5dp"
                android:textSize="20sp"
                android:background="@drawable/ic_add"/>
        </LinearLayout>

    </RelativeLayout>

</androidx.cardview.widget.CardView>