<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_margin="6dp"
    app:cardCornerRadius="6dp"
    android:backgroundTint="@color/Azure"
    android:elevation="5dp">
    <LinearLayout
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_margin="5dp">
            <TextView
                android:id="@+id/tv_orderId"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="订单号码："
                android:textSize="18sp"
                android:textColor="@color/DarkOrange"/>
            <TextView
                android:id="@+id/orderId"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="1"
                android:textSize="18sp"
                android:textColor="@color/Black"/>
        </LinearLayout>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_margin="5dp">
            <TextView
                android:id="@+id/tv_orderTime"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="下单时间："
                android:textSize="18sp"
                android:textColor="@color/DarkOrange"/>
            <TextView
                android:id="@+id/orderTime"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="2022.06.09 11:28"
                android:textSize="18sp"
                android:textColor="@color/Black"/>
        </LinearLayout>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_margin="5dp">
            <TextView
                android:id="@+id/tv_orderPrice"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="订单价格："
                android:textSize="18sp"
                android:textColor="@color/DarkOrange"/>
            <TextView
                android:id="@+id/orderPrice"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="864"
                android:textSize="18sp"
                android:textColor="@color/Black"/>
            <TextView
                android:id="@+id/orderPriceUnit"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="元"
                android:textSize="18sp"
                android:layout_marginLeft="2dp"
                android:textColor="@color/Black"/>
        </LinearLayout>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_margin="5dp">
            <TextView
                android:id="@+id/tv_orderContent"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="订单内容："
                android:textSize="18sp"
                android:textColor="@color/DarkOrange"/>
            <TextView
                android:id="@+id/orderContent"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="蒜泥龙虾x3 鸡腿饭x2"
                android:textSize="18sp"
                android:maxLines="10"
                android:textColor="@color/Black"/>
        </LinearLayout>

    </LinearLayout>



</androidx.cardview.widget.CardView>