<?xml version="1.0" encoding="utf-8"?>
<merger version="3" xmlns:ns1="http://schemas.android.com/tools"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\res"><file name="ic_launcher_background" path="C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="item_selector" path="C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\res\drawable\item_selector.xml" qualifiers="" type="drawable"/><file name="bg_button" path="C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\res\drawable-v24\bg_button.xml" qualifiers="v24" type="drawable"/><file name="bg_textview" path="C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\res\drawable-v24\bg_textview.xml" qualifiers="v24" type="drawable"/><file name="bg_welcome" path="C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\res\drawable-v24\bg_welcome.jpg" qualifiers="v24" type="drawable"/><file name="icon" path="C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\res\drawable-v24\icon.png" qualifiers="v24" type="drawable"/><file name="ic_account" path="C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\res\drawable-v24\ic_account.png" qualifiers="v24" type="drawable"/><file name="ic_add" path="C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\res\drawable-v24\ic_add.png" qualifiers="v24" type="drawable"/><file name="ic_cart" path="C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\res\drawable-v24\ic_cart.png" qualifiers="v24" type="drawable"/><file name="ic_chihuo" path="C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\res\drawable-v24\ic_chihuo.jpg" qualifiers="v24" type="drawable"/><file name="ic_delete" path="C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\res\drawable-v24\ic_delete.png" qualifiers="v24" type="drawable"/><file name="ic_home" path="C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\res\drawable-v24\ic_home.png" qualifiers="v24" type="drawable"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\res\drawable-v24\ic_launcher_foreground.xml" qualifiers="v24" type="drawable"/><file name="ic_minus" path="C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\res\drawable-v24\ic_minus.png" qualifiers="v24" type="drawable"/><file name="ic_more" path="C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\res\drawable-v24\ic_more.jpg" qualifiers="v24" type="drawable"/><file name="ic_order" path="C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\res\drawable-v24\ic_order.png" qualifiers="v24" type="drawable"/><file name="ic_password" path="C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\res\drawable-v24\ic_password.png" qualifiers="v24" type="drawable"/><file name="ic_person" path="C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\res\drawable-v24\ic_person.png" qualifiers="v24" type="drawable"/><file name="ic_share" path="C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\res\drawable-v24\ic_share.png" qualifiers="v24" type="drawable"/><file name="logo" path="C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\res\drawable-v24\logo.png" qualifiers="v24" type="drawable"/><file name="order_bg" path="C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\res\drawable-v24\order_bg.jpg" qualifiers="v24" type="drawable"/><file name="p1" path="C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\res\drawable-v24\p1.jpg" qualifiers="v24" type="drawable"/><file name="p10" path="C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\res\drawable-v24\p10.jpg" qualifiers="v24" type="drawable"/><file name="p11" path="C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\res\drawable-v24\p11.jpg" qualifiers="v24" type="drawable"/><file name="p12" path="C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\res\drawable-v24\p12.jpg" qualifiers="v24" type="drawable"/><file name="p2" path="C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\res\drawable-v24\p2.jpg" qualifiers="v24" type="drawable"/><file name="p3" path="C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\res\drawable-v24\p3.jpg" qualifiers="v24" type="drawable"/><file name="p4" path="C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\res\drawable-v24\p4.jpg" qualifiers="v24" type="drawable"/><file name="p5" path="C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\res\drawable-v24\p5.jpg" qualifiers="v24" type="drawable"/><file name="p6" path="C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\res\drawable-v24\p6.jpg" qualifiers="v24" type="drawable"/><file name="p7" path="C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\res\drawable-v24\p7.jpg" qualifiers="v24" type="drawable"/><file name="p8" path="C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\res\drawable-v24\p8.jpg" qualifiers="v24" type="drawable"/><file name="p9" path="C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\res\drawable-v24\p9.jpg" qualifiers="v24" type="drawable"/><file name="tv_style" path="C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\res\drawable-v24\tv_style.xml" qualifiers="v24" type="drawable"/><file name="activity_food" path="C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\res\layout\activity_food.xml" qualifiers="" type="layout"/><file name="activity_login" path="C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\res\layout\activity_login.xml" qualifiers="" type="layout"/><file name="activity_main" path="C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="activity_order" path="C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\res\layout\activity_order.xml" qualifiers="" type="layout"/><file name="activity_register" path="C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\res\layout\activity_register.xml" qualifiers="" type="layout"/><file name="activity_welcome" path="C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\res\layout\activity_welcome.xml" qualifiers="" type="layout"/><file name="cart_empty" path="C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\res\layout\cart_empty.xml" qualifiers="" type="layout"/><file name="cart_item" path="C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\res\layout\cart_item.xml" qualifiers="" type="layout"/><file name="food_item" path="C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\res\layout\food_item.xml" qualifiers="" type="layout"/><file name="fragment_cart" path="C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\res\layout\fragment_cart.xml" qualifiers="" type="layout"/><file name="fragment_home" path="C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\res\layout\fragment_home.xml" qualifiers="" type="layout"/><file name="fragment_person" path="C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\res\layout\fragment_person.xml" qualifiers="" type="layout"/><file name="nav_header" path="C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\res\layout\nav_header.xml" qualifiers="" type="layout"/><file name="order_item" path="C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\res\layout\order_item.xml" qualifiers="" type="layout"/><file name="main" path="C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\res\menu\main.xml" qualifiers="" type="menu"/><file name="nav_menu" path="C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\res\menu\nav_menu.xml" qualifiers="" type="menu"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\res\mipmap-hdpi\ic_launcher.png" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\res\mipmap-hdpi\ic_launcher_round.png" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\res\mipmap-mdpi\ic_launcher.png" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\res\mipmap-mdpi\ic_launcher_round.png" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\res\mipmap-xhdpi\ic_launcher.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\res\mipmap-xhdpi\ic_launcher_round.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_icon" path="C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\res\mipmap-xxhdpi\ic_icon.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\res\mipmap-xxhdpi\ic_launcher.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="nav_call" path="C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\res\mipmap-xxhdpi\nav_call.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="nav_friends" path="C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\res\mipmap-xxhdpi\nav_friends.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="nav_icon" path="C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\res\mipmap-xxhdpi\nav_icon.jpg" qualifiers="xxhdpi-v4" type="mipmap"/><file name="nav_location" path="C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\res\mipmap-xxhdpi\nav_location.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="nav_mail" path="C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\res\mipmap-xxhdpi\nav_mail.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="nav_task" path="C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\res\mipmap-xxhdpi\nav_task.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\res\mipmap-xxxhdpi\ic_launcher.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\res\values\colors.xml" qualifiers=""><color name="purple_200">#FFBB86FC</color><color name="purple_500">#FF6200EE</color><color name="purple_700">#FF3700B3</color><color name="teal_200">#FF03DAC5</color><color name="teal_700">#FF018786</color><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color><color name="SpringGreen">#3CB371</color><color name="DarkTurquoise">#00CED1</color><color name="CadetBlue">#5F9EA0</color><color name="LightCyan">#E1FFFF</color><color name="DeepSkyBlue">#00BFFF</color><color name="Cyan">#00FFFF</color><color name="DarkCyan">#008B8B</color><color name="Black">#000000</color><color name="Gray">#808080</color><color name="Green">#008000</color><color name="Red">#FF0000</color><color name="Blue">#3C8DC4</color><color name="White">#FFFFFF</color><color name="DarkOrange">#FF8C00</color><color name="Azure">#F0FFFF</color><color name="Olive">#808000</color><color name="DarkBlue">#00008B</color><color name="Gold">#FFD700</color><color name="Honeydew">#F0FFF0</color><color name="Lavender">#E6E6FA</color></file><file path="C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">OrderSystem</string><string name="login">用户登录</string><string name="register">用户注册</string><string name="order_info">订单信息</string><string name="order">历史订单</string><string name="person">个人信息</string><string name="share">分享软件</string></file><file path="C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\res\values\style.xml" qualifiers=""><style name="AppTheme" parent="Theme.AppCompat.Light.NoActionBar">
        
        <item name="colorPrimary">@color/DarkCyan</item>
        <item name="colorPrimaryDark">@color/black</item>
        <item name="colorAccent">@color/Red</item>
    </style><style name="FoodActivityTheme" parent="AppTheme">
    </style><style name="PersonTvStyle">
        <item name="android:layout_height">match_parent</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:textColor">@color/Black</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textSize">18sp</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:layout_marginLeft">10dp</item>
        <item name="background">@color/Honeydew</item>
    </style><style name="PersonLineStyle">
        <item name="background">@color/Lavender</item>
        <item name="android:layout_height">3dp</item>
        <item name="android:layout_width">match_parent</item>
    </style><style name="PersonImageStyle">
        <item name="android:layout_height">50dp</item>
        <item name="android:layout_width">50dp</item>
        <item name="android:layout_margin">5dp</item>
    </style></file><file path="C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.OrderSystem" parent="Theme.MaterialComponents.DayNight.DarkActionBar.Bridge">
        
        <item name="colorPrimary">@color/purple_500</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor" ns1:targetApi="l">?attr/colorPrimaryVariant</item>
        
    </style><style name="MyTheme" parent="AppTheme">

    </style></file><file path="C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\res\values-night\themes.xml" qualifiers="night-v8"><style name="Theme.OrderSystem" parent="Theme.MaterialComponents.DayNight.DarkActionBar.Bridge">
        
        <item name="colorPrimary">@color/purple_200</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/black</item>
        
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_200</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor" ns1:targetApi="l">?attr/colorPrimaryVariant</item>
        
    </style></file><file path="C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\main\res\values-v21\styles.xml" qualifiers="v21"><style name="FoodActivityTheme" parent="AppTheme">
        <item name="android:statusBarColor">@android:color/transparent</item>
    </style><style name="PersonTvStyle">
        <item name="android:layout_height">match_parent</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:textColor">@color/Black</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textSize">18sp</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:layout_marginLeft">10dp</item>
        <item name="background">@color/Honeydew</item>
    </style><style name="PersonLineStyle">
        <item name="background">@color/Lavender</item>
        <item name="android:layout_height">3dp</item>
        <item name="android:layout_width">match_parent</item>
    </style><style name="PersonImageStyle">
        <item name="android:layout_height">50dp</item>
        <item name="android:layout_width">50dp</item>
        <item name="android:layout_margin">5dp</item>
    </style></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\OrderSystem\OrderSystem\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>